export default {
    wkOrder: {
        query: {
            status: "OrderStatus",
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            lineCode: "LineCode",
            materialCode: "MaterialCode",
            dateRangeProduct: "ProductDate",
            dateRangeProductTip1: "StartDate",
            dateRangeProductTip2: "EndDate",
            dateRangeBegin: "StartPlanDate",
            dateRangeBeginTip1: "StartDate",
            dateRangeBeginTip2: "EndDate",
            dateRangeEnd: "EndPlanDate",
            dateRangeEndTip1: "StartDate",
            dateRangeEndTip2: "EndDate"
        },
        tbCol: {
            scheduleCode: "ScheduleCode",
            planTime: "PlanTime",
            planStartTime: "StartPlanDate",
            planEndTime: "EndPlanDate",
            materialCode: "MaterialCode",
            materialName: "MaterialName",
            lineCode: "LineCode",
            scheduleStatus: "ScheduleStatus",
            scheduleQty: "ScheduleQty",
            orderCode: "OrderCode",
            orderType: "OrderType",
            orderStatus: "OrderStatus",
            orderQty: "OrderQty",
            onlineQty: "OnlineQty",
            completedQty: "CompletedQty",
            scheduleEditTime: "ScheduleEditTime",
            workshopCode: "WorkshopCode",
            materialVersion: "MaterialVersion",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            start: "Start",
            stop: "Stop",
            restore: "Restore",
            cancle: "Cancle",
            close: "Close",
            sync: "Synchronous",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
            orderCode: "OrderCode",
            orderType: "OrderType",
            orderQty: "OrderQty",
            orderStatus: "OrderStatus",
            scheduleCode: "ScheduleCode",
            scheduleQty: "ScheduleQty",
            scheduleStatus: "ScheduleStatus",
            scheduleEditTime: "ScheduleEditTime",
            onlineQty: "OnlineQty",
            completedQty: "CompletedQty",

            workshopCode: "WorkshopCode",
            lineCode: "LineCode",
            materialCode: "MaterialCode",
            materialName: "MaterialName",
            materialVersion: "MaterialVersion",
            planStartTime: "StartPlanDate",
            planEndTime: "EndPlanDate",
            planTime: "PlanTime",

            status: "Status",
            remark: "Remark",
            remarkTip: "Please enter content",
        },
        message: {
            alarmMsg01: "Please fill in the order code",
            alarmMsg02: "Please fill in the order type",
            alarmMsg03: "Please fill in the order quantity",
            alarmMsg04: "Please fill in the schedule code",
            alarmMsg05: "Please fill in the schedule quantity",
            alarmMsg06: "Please select the schedule edit time",
            alarmMsg07: "Please fill in the workshop code",
            alarmMsg08: "Please fill in the line code",
            alarmMsg09: "Please fill in the material code",
            alarmMsg10: "Please fill in the material name",
            alarmMsg11: "Please fill in the material version",
            alarmMsg12: "Please select the start time",
            alarmMsg13: "Please select the end time",

            editPlanTime: "Edit success",
            editOrderNum: "Edit success",
            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",

            statusTitle01: "Please confirm the start of production work order:",
            statusTitle02: "Please confirm the pause of production work order:",
            statusTitle03: "Please confirm the resumption of production work order:",
            statusTitle04: "Please confirm the cancellation of production work order:",
            statusTitle05: "Please confirm the closure of production work order:",
            statusMiddleTitle: "Schedule:",
        }
    },
    orderBom: {
        query: {
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            materialCode: "ProductCode",
            operationCode: "OperationCode",
            assemblyMaterialCode: "MaterialCode",
        },
        tbCol: {
            orderCode: "OrderCode",
            orderQty: "OrderQty",
            scheduleCode: "ScheduleCode",
            scheduleQty: "ScheduleQty",
            lineCode: "LineCode",
            lineName: "LineName",
            bomId: "BomId",
            bomType: "BomType",
            bomVersion: "BomVersion",
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",
            operationCode: "OperationCode",
            assemblyMaterialId: "MaterialId",
            assemblyMaterialCode: "MaterialCode",
            assemblyMaterialName: "MaterialName",
            assemblyMaterialVersion: "MaterialVersion",
            assemblyUnitCode: "UnitCode",
            assemblyConsumption: "Usage",
            standWorkHours: "WorkHour",
            retroactive: "TraceMode",
            addTime: "AddTime",
            editTime: "EditTime",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            start: "Start",
            stop: "Stop",
            restore: "Restore",
            cancle: "Cancle",
            close: "Close",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
            orderCode: "OrderCode",
            orderQty: "OrderQty",
            scheduleCode: "ScheduleCode",
            scheduleQty: "ScheduleQty",
            scheduleEditTime: "ScheduleEditTime",
            bomId: "BomId",
            bomType: "BomType",
            bomVersion: "BomVersion",
            lineCode: "LineCode",
            lineName: "LineName",
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",

            operationCode: "OperationCode",
            assemblyMaterialId: "MaterialId",
            assemblyMaterialCode: "MaterialCode",
            assemblyMaterialName: "MaterialName",
            assemblyMaterialVersion: "MaterialVersion",
            assemblyUnitCode: "UnitCode",
            assemblyConsumption: "Usage",
            standWorkHours: "WorkHour",
            retroactive: "TraceMode",
            addTime: "AddTime",
            editTime: "EditTime",

            status: "Status",
            remark: "Remark",
            remarkTip: "Please enter content",
        },
        message: {
            alarmMsg01: "Please fill in the work order code",
            alarmMsg02: "Please fill in the work order quantity",
            alarmMsg03: "Please fill in the schedule code",
            alarmMsg04: "Please fill in the schedule quantity",
            alarmMsg05: "Please fill in the Bom Id",
            alarmMsg06: "Please select the Bom type",
            alarmMsg07: "Please fill in the Bom version",
            alarmMsg08: "Please fill in the line code",
            alarmMsg09: "Please fill in the product code",
            alarmMsg10: "Please fill in the product version",
            alarmMsg11: "Please fill in the operation code",
            alarmMsg12: "Please fill in the material Id",
            alarmMsg13: "Please fill in the material code",
            alarmMsg14: "Please fill in the material code",
            alarmMsg15: "Please fill in the material version",
            alarmMsg16: "Please fill in the material unit code",
            alarmMsg17: "Please fill in the material usage",
            alarmMsg18: "Please fill in the standard working hours",
            alarmMsg19: "Please fill in the trace mode",

            editPlanTime: "Edit success",
            editOrderNum: "Edit success",
            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",

            syncTit01: "Please fill in the order code:",
            syncTit02: "Please fill in the schedule code:",
        }
    },
    orderRoute: {
        query: {
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            routeNumber: "PressCode",
            operationCode: "OperationCode",
        },
        tbCol: {
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            routeNumber: "PressCode",
            routeVersion: "PressVersion",
            lastOperationCode: "PreOperationCode",
            nextOperationCode: "NextOperationCode",
            operationCode: "OperationCode",
            operationName: "OperationName",
            sort: "OperationSort",
            stationCode: "StationCode",
            editTime: "EditTime",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            start: "Start",
            stop: "Stop",
            restore: "Restore",
            cancle: "Cancle",
            close: "Close",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            routeNumber: "PressCode",
            routeVersion: "PressVersion",
            lastOperationCode: "PreOperationCode",
            nextOperationCode: "NextOperationCode",
            operationCode: "OperationCode",
            operationName: "OperationName",
            sort: "OperationSort",
            stationCode: "StationCode",
            editTime: "EditTime",

            status: "Status",
            remark: "Remark",
            remarkTip: "Please enter content",
        },
        message: {
            alarmMsg01: "Please fill in the order code",
            alarmMsg02: "Please fill in the schedule code",
            alarmMsg03: "Please fill in the press code",
            alarmMsg04: "Please fill in the press version",
            alarmMsg05: "Please fill in the pre operation code",
            alarmMsg06: "Please fill in the next operation code",
            alarmMsg07: "Please fill in the operation code",
            alarmMsg08: "Please fill in the operation name",
            alarmMsg09: "Please fill in the operation sort",
            alarmMsg10: "Please fill in the station code",

            editPlanTime: "Edit success",
            editOrderNum: "Edit success",
            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",

            syncTit01: "Please fill in the order code:",
            syncTit02: "Please fill in the schedule code:",
        }
    }
}