import request from '@/utils/request'


export function listDataAsync(query) {
    return request({
        url: '/bad-codes/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/bad-codes',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/bad-codes/' + id,
        method: 'get'
    })
}

// 删除
export function delData(id) {
    return request({
        url: `/bad-codes/${id}`,
        method: 'delete',
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/bad-codes',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(data) {
    return request({
        url: `/bad-codes/${data.id}`,
        method: 'put',
        data: data
    })
}

// 同步数据
export function getDown() {
  return request({
    url: '/bad-codes/getDown',
    method: 'get'
  })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/bad-codes/' + roleId,
        method: 'get'
    })
}