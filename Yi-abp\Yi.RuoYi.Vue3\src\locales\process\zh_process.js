export default {
    bom: {
    },
    route: {
    },
    rtfile: {
        query: {
            materialCode: "产品编号",
            materialCodeTip: "产品编号",
            routeCode: "工艺编码",
            routeCodeTip: "工艺编码",
            operationCode: "工序编号",
            operationCodeTip: "工序编号",
        },
        tbCol: {
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            routeCode: "工艺编码",
            routeName: "工艺名称",
            routeVersion: "工艺版本",
            operationCode: "工序编号",
            fileType: "文件类型",
            fileName: "文件名称",
            fileLoad: "文件路径",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            synchronous: "同步",
            import: "导入",
            importSOP: "导入SOP",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
            materialCode: "产品编号",
            materialCodeTip: "产品编号",
            materialName: "产品名称",
            materialNameTip: "产品名称",
            materialVersion: "产品版本",
            materialVersionTip: "产品版本",
            routeCode: "工艺编号",
            routeCodeTip: "工艺编号",
            routeName: "工艺名称",
            routeNameTip: "工艺名称",
            routeVersion: "工艺版本",
            routeVersionTip: "工艺版本",
            operationCode: "工序编号",
            operationCodeTip: "工序编号",

            fileType: "文件类型",
            fileTypeTip: "文件类型",
            fileName: "文件名称",
            fileNameTip: "文件名称",
            fileLoad: "文件路径",
            fileLoadTip: "文件路径",
            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarmMsg01: "请填写产品编码",
            alarmMsg02: "请填写产品版本",
            alarmMsg03: "请填工艺编号",
            alarmMsg04: "请填工艺版本",
            alarmMsg05: "请填写工序编号",
            alarmMsg06: "导入失败,只能上传PDF格式的文件",
            alarmMsg07: "导入失败,文件大小不能超过50MB",
            alarmMsg08: "数据保存成功",
            alarmMsg09: "导入失败",
            alarmMsg10: "",

            addTitle: "新增",
            addSuccess: "新增成功",
            editTitle: "编辑",
            editSuccess: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuccess: "删除成功",
            syncSuccess: "同步成功:",
            importSuccess: "导入成功",
        }
    },
    rtparam: {
        query: {
            lineCode: "产线编号",
            lineCodeTip: "产线编号",
            materialCode: "产品编号",
            materialCodeTip: "产品编号",
            operationCode: "工序编号",
            operationCodeTip: "工序编号",
            paramCode: "参数编号",
            paramCodeTip: "参数编号",
        },
        tbCol: {
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            lineCode: "产线编号",
            operationCode: "工序编号",
            machineCode: "机台编码",
            paramCode: "参数编码",
            paramName: "参数名称",
            standardRange1: "下限值",
            standardRange2: "上限值",
            standardValue: "标准值",
            targetValue: "目标值",
            addTime: "新增时间",
            editTime: "更新时间",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            lineCode: "产线编号",
            operationCode: "工序编号",
            machineCode: "机台编码",
            paramCode: "参数编码",
            paramName: "参数名称",
            standardRange1: "下限值",
            standardRange2: "上限值",
            standardValue: "标准值",
            targetValue: "目标值",
            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarmMsg01: "请填写产品编号",
            alarmMsg02: "请填写产品版本",
            alarmMsg03: "请填写产线编号",
            alarmMsg04: "请填写工序编号",
            alarmMsg05: "请填写参数编码",
            alarmMsg06: "请填写下限值",
            alarmMsg07: "请填写上限值",
            alarmMsg08: "请填写标准值",
            alarmMsg09: "请填写目标值",
            alarmMsg10: "请填写产线编号",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    }
}