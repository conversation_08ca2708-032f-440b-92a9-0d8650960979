export default {
    badCode: {
        query: {
            operationCode: "Código operação",
            badCode: "Código defeito",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        tbCol: {
            operationCode: "Código operação",
            badCode: "Código defeito",
            badName: "Nome defeito",
            badTypeCode: "Código tipo",
            badTypeName: "Nome tipo",
            enableStatus: "Ativado",
            dataStatus: "Excluído",
            addTime: "Data adição",
            editTime: "Data edição",
            
            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
            operationCode: "Código operação",
            badCode: "Código defeito",
            badName: "Nome defeito",
            badTypeCode: "Código tipo",
            badTypeName: "Nome tipo",
            enableStatus: "Ativado",
            dataStatus: "Excluído",
            addTime: "Data adição",
            editTime: "Data edição",
            
            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor, insira o conteúdo",
        },
        message: {
            alarm01: "Por favor, preencha o código do processo",
            alarm02: "Por favor, preencha o código do defeito",
            alarm03: "Por favor, preencha o nome do defeito",
            alarm04: "Por favor, preencha o código do tipo de defeito",
            alarm05: "Por favor, preencha o nome do tipo de defeito",

            addTit: "Adicionar",
            addSuc: "Adicionar sucesso",
            editTit: "Editar",
            editSuc: "Editar sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Apagar sucesso",
            syncSuc: "Sincronização sucesso",
            importSuc: "Importação sucesso",
        }
    },
    badData: {
        query: {
            orderCode: "Código ordem",
            scheduleCode: "Código agendamento",
            lineCode: 'Código linha',
            operationCode: "Código operação",
            snNumber: "Código Sn",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        tbCol: {
            orderCode: "Código ordem",
            scheduleCode: "Código agendamento",
            snNumber: "Código Sn",
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",
            lineCode: "Código linha",
            stationCode: "Código estação",
            operationCode: "Código operação",
            badCode: "Código defeito",
            badFactor: "Fator defeito",
            badQty: "Quantidade defeito",
            userId: "Id usuário",
            editTime: "Tempo criação",
            description: "Descrição",
            auditOpinion: "Opinião auditoria",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
            orderCode: "Código ordem",
            scheduleCode: "Código agendamento",
            snNumber: "Código Sn",
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",
            lineCode: "Código linha",
            stationCode: "Código estação",
            operationCode: "Código operação",
            badCode: "Código defeito",
            badFactor: "Fator defeito",
            badQty: "Quantidade defeito",
            userId: "Id usuário",
            editTime: "Tempo criação",
            images:"Imagens",
            description: "Descrição",
            auditOpinion: "Opinião auditoria",

            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor, insira o conteúdo",
        },
        message: {
            alarm01: "Por favor escolha o estado",
            alarm02: "Preencha os pareceres de auditoria",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "Adicionar",
            addSuc: "Adicionar sucesso",
            editTit: "Editar",
            editSuc: "Editar sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Apagar sucesso",
            syncSuc: "Sincronização sucesso",
            importSuc: "Importação sucesso",
        }
    },
    retrace: {
        query: {
            snNumber: "Código Sn",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        tbCol: {
            orderCode: "Código ordem",
            orderType: "Tipo ordem",
            orderQty: "Quantidade ordem",
            orderStatus: "Estado ordem",
            lineCode: "Código linha",

            scheduleCode: "Código agendamento",
            scheduleQty: "Quantidade agendamento",
            scheduleStatus: "Estado agendamento",
            planStartTime: "Início planejado",
            planEndTime: "Fim planejado",

            psStation: "Estação passagem",
            stationCode: "Código estação",
            passBeginTime: "Plano Início",
            passEndTime: "Plano Fim",

            mBind: "Ligar material",
            operationCode: "Código operação",
            assemblyMaterialCode: "Código material",
            assemblyTime: "Tempo online",
            assemblyMaterialSn: "Sn material",

            wkParams: "Parâmetro operação",
            operationCode: "Código operação",
            creationTime: "Tempo criação",
            paramCode: "Código parâmetro",
            standardRange1: "Limite inferior",
            standardRange2: "Limite superior",
            standardValue: "Valor padrão",
            targetValue: "Valor alvo",
            realValue: "Valor real",
            checkResult: "Resultado verificação",

            andon: "Andon",
            stationCode: "Código estação ",
            unusualAlarmCode: "Código alarme",
            lastModificationTime: "Data modificação",
            remark: "Observação",
            lastModifierName: "Nome modificação",

            badData: "Dados ruins",
            operationCode: "Código operação",
            badCode: "Código defeito",
            badFactor: "Fator defeito",
            description: "Descrição",
            lastModificationTime: "Data modificação",
            lastModifierName: "Nome modificação",
            auditOpinion: "Opinião auditoria",
        },
        form: {},
        message: {},
    }
}