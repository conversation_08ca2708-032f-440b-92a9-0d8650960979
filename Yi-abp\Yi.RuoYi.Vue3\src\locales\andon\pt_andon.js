export default {
    // 安灯记录
    record: {
        queryField: {
            lineCode: "Linha produção",
            andonType: "Tipo",
            andonStatus: "Estado",
            dateRange: "Tempo",
            startTime: "Hora Início",
            endTime: "Hora Fim",
        },
        tbColumn: {
            Id: "Número",
            workShopCode: "Oficina",
            lineCode: "Linha produção",
            stationCode: "Estação trabalho",
            machineCode: "Máquina",
            andonType: "Tipo lâmpada",
            andonLevel: "Nível anormal",
            unusualAlarmCode: "Tempo aviso anormal",
            andonStatus: "Estado lâmpada",
            alarmMessage: "Informação anormal",
            remark: "Recomendações auditoria",
            creationTime: "Tempo criação",
            lastModifierName: "Pessoal transformação",
            lastModificationTime: "Tempo processamento"
        },
        button: {
            searchBtn: "Procurar",
            resetBtn: "Reset",

            importBtn: "Importa<PERSON>",
            exportBtn: "Exporta<PERSON>",

            addBtn: "Aumento",
            removeBtn: "Apagar",
            editBtn: "Editar",

            confirmBtn: "Confirmar",
            cancelBtn: "Cancelar",
        },
        message: {
            confirmMsg: "Por favor confirme para executar esta operação?",
            deleteMsg: "Deseja apagar este registo?",
            editMsg: "Deseja editar estes dados?",
        }
    },
    // 未处理安灯记录
    untreated: {
    }
}