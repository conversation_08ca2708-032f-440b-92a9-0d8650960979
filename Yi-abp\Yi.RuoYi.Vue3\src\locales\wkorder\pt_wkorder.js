export default {
    wkOrder: {
        query: {
            status: "Estado pedido",
            orderCode: "Código pedido",
            scheduleCode: "Código Agenda",
            lineCode: "Código linha",
            materialCode: "Código produto",
            dateRangeProduct: "Data produto",
            dateRangeProductTip1: "Tempo iniciar",
            dateRangeProductTip2: "Tempo fim",
            dateRangeBegin: "Plano iniciar",
            dateRangeBeginTip1: "Tempo iniciar",
            dateRangeBeginTip2: "Tempo fim",
            dateRangeEnd: "Plano fim",
            dateRangeEndTip1: "Tempo iniciar",
            dateRangeEndTip2: "Tempo fim",
        },
        tbCol: {
            scheduleCode: "Código Agenda",
            planTime: "Data produto",
            planStartTime: "Plano iniciar",
            planEndTime: "Plano fim",
            materialCode: "Código produto",
            materialName: "Nome produto",
            lineCode: "Código linha",
            scheduleStatus: "Estado Agenda",
            scheduleQty: "Quantidade Agenda",
            orderCode: "Código pedido",
            orderType: "Tipo pedido",
            orderStatus: "Estado pedido",
            orderQty: "Quantidade pedido",
            onlineQty: "Quantidade WIP",
            completedQty: "Quantidade fim",
            scheduleEditTime: "Tempo Agenda",
            workshopCode: "Código oficina",
            materialVersion: "Versão produto",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            start: "Iniciar",
            stop: "Pausar",
            restore: "Retomar",
            cancle: "Cancelar",
            close: "Fechar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
            orderCode: "Código pedido",
            orderType: "Tipo pedido",
            orderQty: "Quantidade pedido",
            orderStatus: "Status pedido",
            scheduleCode: "Código Agenda",
            scheduleQty: "Quantidade Agenda",
            scheduleStatus: "Status Agenda",
            scheduleEditTime: "Tempo Agenda",
            onlineQty: "Quantidade produção",
            completedQty: "Quantidade concluída",

            workshopCode: "Código oficina",
            lineCode: "Código linha",
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",
            planStartTime: "Plano início",
            planEndTime: "Plano término",
            planTime: "Tempo planejado",

            status: "Status",
            remark: "Observação",
            remarkTip: "Por favor insira o conteúdo",
        },
        message: {
            alarmMsg01: "Por favor preencha o código do pedido",
            alarmMsg02: "Por favor preencha o tipo de pedido",
            alarmMsg03: "Por favor preencha a quantidade do pedido",
            alarmMsg04: "Por favor preencha o código do Agenda",
            alarmMsg05: "Por favor preencha a quantidade Agenda",
            alarmMsg06: "Por favor selecione o tempo de edição do Agenda",
            alarmMsg07: "Por favor preencha o código da oficina",
            alarmMsg08: "Por favor preencha o código da linha",
            alarmMsg09: "Por favor preencha o código do produto",
            alarmMsg10: "Por favor preencha o nome do produto",
            alarmMsg11: "Por favor preencha a versão do produto",
            alarmMsg12: "Por favor selecione o tempo de início planejado",
            alarmMsg13: "Por favor selecione o tempo de término planejado",

            editPlanTime: "Ajuste bem-sucedido",
            editOrderNum: "Ajuste bem-sucedido",
            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",

            statusTitle01: "Confirmar início do pedido de produção:",
            statusTitle02: "Confirmar pausa do pedido de produção:",
            statusTitle03: "Confirmar retomada do pedido de produção:",
            statusTitle04: "Confirmar cancelamento do pedido de produção:",
            statusTitle05: "Confirmar fechamento do pedido de produção:",
            statusMiddleTitle: "Agenda:",
        }
    },
    orderBom: {
        query: {
            orderCode: "Código pedido",
            scheduleCode: "Código Agenda",
            materialCode: "Código produto",
            operationCode: "Código operação",
            assemblyMaterialCode: "Código material",
        },
        tbCol: {
            orderCode: "Código pedido",
            orderQty: "Quantidade pedido",
            scheduleCode: "Código Agenda",
            scheduleQty: "Quantidade Agenda",
            lineCode: "Código linha",
            lineName: "Nome linha",
            bomId: "BomId",
            bomType: "Tipo Bom",
            bomVersion: "Versão Bom",
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Nome produto",
            operationCode: "Código operação",
            assemblyMaterialId: "Id material",
            assemblyMaterialCode: "Código material",
            assemblyMaterialName: "Nome material",
            assemblyMaterialVersion: "Versão material",
            assemblyUnitCode: "Unidade material",
            assemblyConsumption: "Consumo de material",
            standWorkHours: "Horas padrão",
            retroactive: "Método rastreamento",
            addTime: "Tempo criação",
            editTime: "Tempo atualização",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        button: {
            search: "Procurar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronização",
            import: "Importação",
            edit: "Editar",
            delete: "Apagar",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
            orderCode: "Código pedido",
            orderQty: "Quantidade pedido",
            scheduleCode: "Código Agenda",
            scheduleQty: "Quantidade Agenda",
            scheduleEditTime: "Tempo Agenda",
            bomId: "BomId",
            bomType: "Tipo Bom",
            bomVersion: "Versão Bom",
            lineCode: "Código linha",
            lineName: "Nome linha",
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",

            operationCode: "Código operação",
            assemblyMaterialId: "Id material",
            assemblyMaterialCode: "Código material",
            assemblyMaterialName: "Nome material",
            assemblyMaterialVersion: "Versão material",
            assemblyUnitCode: "Unidade material",
            assemblyConsumption: "Consumo material",
            standWorkHours: "Horas padrão",
            retroactive: "Método rastreamento",
            addTime: "Tempo criação",
            editTime: "Tempo atualização",

            status: "Status",
            remark: "Observação",
            remarkTip: "Por favor insira o conteúdo",
        },
        message: {
            alarmMsg01: "Por favor preencha o Código pedido",
            alarmMsg02: "Por favor preencha a quantidade do pedido",
            alarmMsg03: "Por favor preencha o Código Agenda",
            alarmMsg04: "Por favor preencha a quantidade Agenda",
            alarmMsg05: "Por favor preencha o BomId",
            alarmMsg06: "Por favor selecione o tipo de Bom",
            alarmMsg07: "Por favor preencha a versão do Bom",
            alarmMsg08: "Por favor preencha o código da linha",
            alarmMsg09: "Por favor preencha o Código produto",
            alarmMsg10: "Por favor preencha a versão do produto",
            alarmMsg11: "Por favor preencha o código da operação",
            alarmMsg12: "Por favor preencha o Id do material",
            alarmMsg13: "Por favor preencha o Código material",
            alarmMsg14: "Por favor preencha a versão do material",
            alarmMsg15: "Por favor preencha a unidade do material",
            alarmMsg16: "Por favor preencha o consumo do material",
            alarmMsg17: "Por favor preencha as horas padrão",
            alarmMsg18: "Por favor preencha o método de rastreamento",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",

            syncTit01: "Por favor preencha o Código pedido:",
            syncTit02: "Por favor preencha o Código Agenda:",
        }
    },
    orderRoute: {
        query: {
            orderCode: "Código pedido",
            scheduleCode: "Código Agenda",
            routeNumber: "Rotaprocesso",
            operationCode: "Código operação",
        },
        tbCol: {
            orderCode: "Código pedido",
            scheduleCode: "Código Agenda",
            routeNumber: "Rota processo",
            routeVersion: "Versão processo",
            lastOperationCode: "operação anterior",
            nextOperationCode: "operação próxima",
            operationCode: "código operação",
            operationName: "Nome operação",
            sort: "ordenar",
            stationCode: "Código estação",
            editTime: "Tempo atualização",

            orderNum: "Ordenação",
            status: "Status",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Conta criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Conta modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
            orderCode: "Código pedido",
            scheduleCode: "Código Agenda",
            routeNumber: "Rota processo",
            routeVersion: "Versão processo",
            lastOperationCode: "operação anterior",
            nextOperationCode: "operação próxima",
            operationCode: "código operação",
            operationName: "Nome operação",
            sort: "ordenar",
            stationCode: "Código estação",
            editTime: "Tempo atualização",

            status: "Status",
            remark: "Observação",
            remarkTip: "Por favor insira o conteúdo",
        },
        message: {
            alarmMsg01: "Por favor preencha o código pedido",
            alarmMsg02: "Por favor preencha o código Agenda",
            alarmMsg03: "Por favor preencha o código da rota de processo",
            alarmMsg04: "Por favor preencha a versão da rota de processo",
            alarmMsg05: "Por favor preencha o código da operação anterior",
            alarmMsg06: "Por favor preencha o código da próxima operação",
            alarmMsg07: "Por favor preencha o código da operação",
            alarmMsg08: "Por favor preencha o nome da operação",
            alarmMsg09: "Por favor preencha a ordem das operações",
            alarmMsg10: "Por favor preencha o código da estação",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",

            syncTit01: "Por favor preencha o código pedido:",
            syncTit02: "Por favor preencha o código Agenda:",
        }
    }
}