<template>
  <!-- 授权用户 -->
  <el-dialog :title="t('system.sltUser.choice')" v-model="visible" width="800px" top="5vh" append-to-body>
    <el-form :model="queryParams" ref="queryRef" :inline="true">
      <el-form-item :label="t('system.sltUser.userName')" prop="userName">
        <el-input v-model="queryParams.userName" :placeholder="t('system.sltUser.userName')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('system.sltUser.phone')" prop="phone">
        <el-input v-model="queryParams.phone" :placeholder="t('system.sltUser.phone')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{t('system.authUser.search')}}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{t('system.authUser.reset')}}</el-button>
      </el-form-item>
    </el-form>
    <el-row>
      <el-table @row-click="clickRow" ref="refTable" :data="userList" @selection-change="handleSelectionChange" height="260px">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column :label="t('system.authUser.userName')" prop="userName" :show-overflow-tooltip="true" />
        <el-table-column :label="t('system.authUser.nickName')" prop="nick" :show-overflow-tooltip="true" />
        <el-table-column :label="t('system.authUser.email')" prop="email" :show-overflow-tooltip="true" />
        <el-table-column :label="t('system.authUser.phone')" prop="phone" :show-overflow-tooltip="true" />
        <el-table-column :label="t('system.authUser.status')" align="center" prop="state">
          <template #default="scope">
            <dict-tag :options="sys_normal_disable" :value="scope.row.state" />
          </template>
        </el-table-column>
        <el-table-column :label="t('system.authUser.creationTime')" align="center" prop="creationTime" width="180">
          <template #default="scope">
            <span>{{ parseTime(scope.row.creationTime) }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleSelectUser">{{t('system.authUser.confirm')}}</el-button>
        <el-button @click="visible = false">{{t('system.authUser.cancle')}}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup name="SelectUser">
import { authUserSelectAll, unallocatedUserList } from "@/api/system/role";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const props = defineProps({
  roleId: {
    type: [Number, String]
  }
});

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const userList = ref([]);
const visible = ref(false);
const total = ref(0);
const userIds = ref([]);

const queryParams = reactive({
  skipCount: 1,
  maxResultCount: 10,
  roleId: undefined,
  userName: undefined,
  phone: undefined
});

// 显示弹框
function show() {
  queryParams.roleId = props.roleId;
  getList();
  visible.value = true;
}
/**选择行 */
function clickRow(row) {
  proxy.$refs["refTable"].toggleRowSelection(row);
}
// 多选框选中数据
function handleSelectionChange(selection) {
  userIds.value = selection.map(item => item.id);
}
// 查询表数据
function getList() {
  unallocatedUserList(queryParams.roleId,queryParams).then(res => {
    userList.value = res.data.items;
    total.value = res.data.total;
  });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.skipCount = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
const emit = defineEmits(["ok"]);
/** 选择授权用户操作 */
function handleSelectUser() {
  const roleId = queryParams.roleId;

  const uIds = userIds.value;
  if (uIds == []) {
    proxy.$modal.msgError(t('system.sltUser.choiceUser'));
    return;
  }
  authUserSelectAll({ roleId: roleId, userIds:uIds }).then(res => {
    if (res.status == 200 || res.status == 204) {
      proxy.$modal.msgSuccess(t('system.sltUser.success'));
      visible.value = false;
      emit("ok");
    }
  });
}

defineExpose({
  show,
});
</script>
