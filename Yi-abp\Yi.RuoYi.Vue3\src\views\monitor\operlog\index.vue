<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('monitor.operation.query.optTime')" style="width: 308px">
            <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="t('monitor.operation.query.startTime')" :end-placeholder="t('monitor.operation.query.endTime')"></el-date-picker>
         </el-form-item>
         <el-form-item :label="t('monitor.operation.query.optTime')" prop="title">
            <el-input v-model="queryParams.title" placeholder="请输入系统模块" clearable style="width: 240px;" @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="类型" prop="operType">
            <el-select v-model="queryParams.operType" placeholder="操作类型" clearable style="width: 240px">
               <el-option v-for="dict in sys_oper_type" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
         </el-form-item>
         <el-form-item label="操作人员" prop="operUser">
            <el-input v-model="queryParams.operUser" placeholder="请输入操作人员" clearable style="width: 240px;" @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['monitor:operlog:remove']" v-if="false">删除</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" @click="handleClean" v-hasPermi="['monitor:operlog:remove']">清空</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['monitor:operlog:export']">导出</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table ref="operlogRef" v-loading="loading" :data="operlogList" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="系统模块" align="center" prop="title" />
         <el-table-column label="操作类型" align="center" prop="operType">
            <template #default="scope">
               <dict-tag :options="sys_oper_type" :value="scope.row.operType" />
            </template>
         </el-table-column>
         <el-table-column label="请求方式" align="center" prop="requestMethod" />
         <el-table-column label="操作人员" align="center" prop="operUser" :show-overflow-tooltip="true" sortable="custom" :sort-orders="['descending', 'ascending']" width="100" />
         <el-table-column label="主机" align="center" prop="operIp" width="130" :show-overflow-tooltip="true" />
         <el-table-column label="操作日期" align="center" prop="creationTime" sortable="custom" :sort-orders="['descending', 'ascending']" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.creationTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link icon="View" @click="handleView(scope.row, scope.index)" v-hasPermi="['monitor:operlog:query']">详情</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 操作日志详细 -->
      <el-dialog title="操作日志详细" v-model="open" width="1200px" append-to-body>
         <el-form :model="form">
            <el-row :gutter="24" wrap>
               <el-col :span="12">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b" size="large">操作模块: {{ form.title }}</el-text>
                        <el-text tag="b" size="large">用户信息: {{  form.operUser }} / {{ form.operIp }}</el-text>       
                     </el-space>
                  </el-card>
               </el-col>
               <el-col :span="12">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b" size="large">操作时间: {{ form.creationTime }}</el-text>     
                        <el-text tag="b" size="large">路由地址: {{ form.method }} </el-text>            
                     </el-space>
                  </el-card>
               </el-col>

               <el-col :span="24">
                  <el-card shadow="hover" style="margin-top: 10px; padding: 10px">
                        <el-text tag="b" size="large">变更数据: </el-text>
                        <hr />
                        <div>
                           <el-text tag="b"> {{ JSON.parse( JSON.stringify(form.requestParam, null, 4)   ) }} </el-text>
                           <!-- <pre> {{  JSON.parse( JSON.stringify(form.requestParam, null, 4)) }} </pre> -->
                           <!-- <code> {{ JSON.stringify(form.requestParam, null, 4) }} </code> -->
                        </div>
                  </el-card>
               </el-col>
               <el-col :span="24">
                  <el-card shadow="hover" style="margin-top: 10px; padding: 10px">
                        <el-text tag="b" size="large">异常信息: </el-text>
                        <hr />
                        <el-text tag="b">{{ form.errorMsg }}</el-text>
                  </el-card>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button @click="open = false">关 闭</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Operlog">
import { list, delOperlog, cleanOperlog } from "@/api/monitor/operlog";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { sys_oper_type, sys_common_status } = proxy.useDict("sys_oper_type", "sys_common_status");

const operlogList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const defaultSort = ref({ prop: "operTime", order: "descending" });

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      title: undefined,
      operUser: undefined,
      operType: undefined,
      state: undefined,
      orderByColumn: undefined
   }
});

const { queryParams, form } = toRefs(data);

/** 查询登录日志 */
function getList() {
   loading.value = true;
   list(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
      operlogList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 操作日志类型字典翻译 */
function typeFormat(row, column) {
   return proxy.selectDictLabel(sys_oper_type.value, row.businessType);
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   dateRange.value = [];
   proxy.resetForm("queryRef");
   proxy.$refs["operlogRef"].sort(defaultSort.value.prop, defaultSort.value.order);
   handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
}
/** 排序触发事件 */
function handleSortChange(column, prop, order) {
   if (!column.order) {
      queryParams.value.orderByColumn = null;
   } else {
      queryParams.value.orderByColumn = column.prop;
   }
   queryParams.value.isAsc = column.order;
   getList();
}
/** 详细按钮操作 */
function handleView(row) {
   open.value = true;
   form.value = row;
}
/** 删除按钮操作 */
function handleDelete(row) {
   const operIds = row.id || ids.value;
   proxy.$modal.confirm('是否确认删除日志编号为"' + operIds + '"的数据项?').then(function () {
      return delOperlog(operIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}
/** 清空按钮操作 */
function handleClean() {
   proxy.$modal.confirm("是否确认清空所有操作日志数据项?").then(function () {
      return cleanOperlog();
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("清空成功");
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   proxy.download("monitor/operlog/export", {
      ...queryParams.value,
   }, `config_${new Date().getTime()}.xlsx`);
}

getList();
</script>
