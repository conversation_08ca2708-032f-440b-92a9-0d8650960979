{"name": "ruoyi", "version": "3.0.0", "description": "BYD MES", "author": "意框架", "license": "MIT", "scripts": {"dev": "vite", "build:prod": "vite build", "build:stage": "vite build --mode staging", "preview": "vite preview"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Vue.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@microsoft/signalr": "^8.0.0", "@vueuse/core": "10.9.0", "axios": "^1.9.0", "echarts": "5.5.0", "element-plus": "2.6.3", "file-saver": "2.0.5", "fuse.js": "7.0.0", "js-cookie": "3.0.5", "jsencrypt": "3.3.2", "json-bigint": "^1.0.0", "moment": "^2.30.1", "nprogress": "0.2.0", "pinia": "2.1.7", "qs": "^6.12.0", "typeface-roboto": "^1.1.13", "vform3-builds": "^3.0.10", "vue": "3.4.21", "vue-cropper": "1.0.3", "vue-i18n": "^11.1.2", "vue-router": "4.3.0", "xlsx": "^0.18.5", "yarn": "^1.22.22"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.21", "consola": "^3.2.3", "sass": "^1.83.1", "unplugin-auto-import": "^0.17.5", "vite": "^5.2.8", "vite-plugin-compression": "^0.5.1", "vite-plugin-style-import": "^2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-vue-setup-extend": "^0.4.0"}, "type": "module"}