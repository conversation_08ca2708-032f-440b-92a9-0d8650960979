import { createI18n } from "vue-i18n";
import Cookies from 'js-cookie'

// andon
import zh_andon from "./andon/zh_andon";
import en_andon from "./andon/en_andon";
import pt_andon from "./andon/pt_andon";

// basedata
import zh_basedata from "./basedata/zh_basedata";
import en_basedata from "./basedata/en_basedata";
import pt_basedata from "./basedata/pt_basedata";

// common
import zh_common from "./common/zh_common";
import en_common from "./common/en_common";
import pt_common from "./common/pt_common";

// equip
import zh_equip from "./equip/zh_equip";
import en_equip from "./equip/en_equip";
import pt_equip from "./equip/pt_equip";

// monitor
import zh_monitor from "./monitor/zh_monitor";
import en_monitor from "./monitor/en_monitor";
import pt_monitor from "./monitor/pt_monitor";

// process
import zh_process from "./process/zh_process";
import en_process from "./process/en_process";
import pt_process from "./process/pt_process";

// production
import zh_production from "./production/zh_production";
import en_production from "./production/en_production";
import pt_production from "./production/pt_production";

// quality
import zh_quality from "./quality/zh_quality";
import en_quality from "./quality/en_quality";
import pt_quality from "./quality/pt_quality";

// system
import zh_system from "./system/zh_system";
import en_system from "./system/en_system";
import pt_system from "./system/pt_system";

// wkorder
import zh_wkorder from "./wkorder/zh_wkorder";
import en_wkorder from "./wkorder/en_wkorder";
import pt_wkorder from "./wkorder/pt_wkorder";


// 划拨资源
const messages = {
  zh: {
    andon: zh_andon,
    basedata: zh_basedata,
    common: zh_common,
    equip: zh_equip,
    monitor: zh_monitor,
    process: zh_process,
    production: zh_production,
    quality: zh_quality,
    system: zh_system,
    wkorder: zh_wkorder,
  },
  en: {
    andon: en_andon,
    basedata: en_basedata,
    common: en_common,
    equip: en_equip,
    monitor: en_monitor,
    process: en_process,
    production: en_production,
    quality: en_quality,
    system: en_system,
    wkorder: en_wkorder,
  },
  pt: {
    andon: pt_andon,
    basedata: pt_basedata,
    common: pt_common,
    equip: pt_equip,
    monitor: pt_monitor,
    process: pt_process,
    production: pt_production,
    quality: pt_quality,
    system: pt_system,
    wkorder: pt_wkorder,
  }
}

let localLang = Cookies.get('language') || "zh";
// let localLang =  localStorage.getItem('language') || "zh";
// let localLang =  sessionStorage.getItem('language') || "zh";


// 初始化实例
const i18n = createI18n({
  legacy: false,
  allowComposition: true,
  globalInjection: true,
  locale: localLang,
  messages
})

export default i18n;  