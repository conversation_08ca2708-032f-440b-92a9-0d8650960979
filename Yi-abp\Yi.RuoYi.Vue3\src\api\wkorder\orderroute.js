import request from '@/utils/request'

export function listDataAsync(query) {
    return request({
        url: '/order-route/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/order-route',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/order-route/' + id,
        method: 'get'
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/order-route',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(data) {
    return request({
        url: `/order-route/` + data.id,
        method: 'put',
        data: data
    })
}

// 删除
export function delData(id) {
    return request({
        url: `/order-route/${id}`,
        method: 'delete',
    })
}

// 同步数据
export function getDown(orderCode, scheduleCode) {
    return request({
        url: '/order-route/getDown?orderCode=' + orderCode + "&scheduleCode=" + scheduleCode,
        method: 'get'
    })
}

// 导入
export function importAsync(data) {
    return request({
        url: '/order-route/importAsync',
        method: 'post',
        data: data
    })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/order-route/' + roleId,
        method: 'get'
    })
}