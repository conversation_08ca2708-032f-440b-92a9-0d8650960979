<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item :label="t('basedata.factoryModel.queryField.modelNumber')" prop="modelCode">
            <el-input v-model="queryParams.modelCode" :placeholder="t('basedata.factoryModel.queryField.modelNumber')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.factoryModel.queryField.modelType')" prop="modelType">
            <el-input v-model="queryParams.modelType" :placeholder="t('basedata.factoryModel.queryField.modelType')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('basedata.factoryModel.button.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('basedata.factoryModel.button.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="toggleExpandAll">{{ $t('basedata.factoryModel.button.expandCollapse') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:post:add']">{{ $t('basedata.factoryModel.button.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['basic:model:down']" :loading="downLoading">{{ $t('basedata.factoryModel.button.synchronous') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['basic:model:import']" :loading="importLoading" v-if="false">{{ $t('basedata.factoryModel.button.Import') }}</el-button>
         </el-col>
      </el-row>

      <el-table v-if="refreshTable" v-loading="loading" :data="modelList" row-key="modelCode" :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }" :row-class-name="tableRowClassName">
         <el-table-column prop="modelName" :label="t('basedata.factoryModel.elForm.modelName')" width="255"></el-table-column>
         <el-table-column prop="modelCode" :label="t('basedata.factoryModel.elForm.modelNumber')"></el-table-column>
         <el-table-column prop="modelType" :label="t('basedata.factoryModel.elForm.modelType')"></el-table-column>
         <el-table-column prop="parentModelCode" :label="t('basedata.factoryModel.elForm.parentModelCode')"></el-table-column>
         <el-table-column prop="enableStatus" :label="t('basedata.factoryModel.elForm.mesStart')"></el-table-column>
         <el-table-column prop="dataStatus" :label="t('basedata.factoryModel.elForm.mesDelete')"></el-table-column>
         <el-table-column prop="addTime" :label="t('basedata.factoryModel.elForm.addTime')" width="155"></el-table-column>
         <el-table-column prop="editTime" :label="t('basedata.factoryModel.elForm.changeTime')" width="155"></el-table-column>

         <el-table-column prop="orderNum" :label="t('basedata.factoryModel.elForm.order')" v-if="false"></el-table-column>
         <el-table-column prop="status" :label="t('basedata.factoryModel.elForm.state')">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.factoryModel.elForm.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('basedata.factoryModel.elForm.creater')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('basedata.factoryModel.elForm.creater')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('basedata.factoryModel.elForm.creatTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('basedata.factoryModel.elForm.processors')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('basedata.factoryModel.elForm.processors')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('basedata.factoryModel.elForm.processTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('basedata.factoryModel.elForm.select')" prop="isDeleted" v-if="false" />

         <el-table-column :label="t('basedata.factoryModel.elForm.operate')" align="center" class-name="small-padding fixed-width" width="220">
            <template #default="scope">
               <el-button link icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['basic:model:add']">{{ $t('basedata.factoryModel.button.add') }}</el-button>
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:model:edit']">{{ $t('basedata.factoryModel.button.edit') }}</el-button>
               <el-button v-if="scope.row.parentModelCode != 0" link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:model:remove']">{{ $t('basedata.factoryModel.button.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <!-- 添加或编辑建模对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form ref="modelRef" :model="form" :rules="rules" label-width="140px">
            <el-row>
               <el-col :span="24" v-if="form.parentModelCode !== 0">
                  <el-form-item :label="t('basedata.factoryModel.tbColumn.parentModel')" prop="parentModelCode"> 
                     <el-tree-select v-model="form.parentModelCode" :data="modelOptions" :props="{ value: 'modelCode', label: 'modelName', children: 'children' }" value-key="modelCode" placeholder="选择上级建模" check-strictly />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('basedata.factoryModel.tbColumn.modelNumber')" prop="modelCode">
                     <el-input v-model="form.modelCode" :placeholder="t('basedata.factoryModel.tbColumn.numberTip')" />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('basedata.factoryModel.tbColumn.modelName')" prop="modelName">
                     <el-input v-model="form.modelName" :placeholder="t('basedata.factoryModel.tbColumn.nameTip')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('basedata.factoryModel.tbColumn.order')" prop="orderNum">
                     <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('basedata.factoryModel.tbColumn.modelType')" prop="modelType">
                     <el-input v-model="form.modelType" :placeholder="t('basedata.factoryModel.tbColumn.typeTip')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('basedata.factoryModel.tbColumn.modelState')">
                     <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in base_is_open" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ $t('basedata.factoryModel.button.confirm') }}</el-button>
               <el-button @click="cancel">{{ $t('basedata.factoryModel.button.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Model">
import { getWkshopAsync, getLineAsync, listDataAsync, listData, getData, getInfoAsync, delData, addData, updateData, getDown} from "@/api/basic/model";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

const modelList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const isdisabledParent = ref(true);
const modelOptions = ref([]);
const isExpandAll = ref(true);
const refreshTable = ref(true);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {
   },
   baseQueryParams: {
      skipCount: 0,
      maxResultCount: 9999,
      Sorting: undefined,
   },
   queryParams: {
      skipCount: 0,
      maxResultCount: 9999,
      Sorting: undefined,
      modelType: undefined
   },
   rules: {
      modelCode: [{ required: true, message: t('basedata.factoryModel.message.alarm01'), trigger: "blur" }],
      modelName: [{ required: true, message: t('basedata.factoryModel.message.alarm02'), trigger: "blur" }],
   },
});

const { baseQueryParams, queryParams, form, rules } = toRefs(data);

/** 查询建模列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      modelList.value = proxy.handleTree(response.data.items, "modelCode", "parentModelCode");
      loading.value = false;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1
   };
   proxy.resetForm("modelRef");
}
/** 搜索按钮操作 */
function handleQuery() {
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
   reset();
   listDataAsync(baseQueryParams.value).then(response => {
      modelOptions.value = proxy.handleTree(response.data.items, "modelCode", "parentModelCode");
   });
   if (row.modelType == undefined) {
      form.value.modelType = "workshop";
   }
   if (row.modelType == "workshop") {
      form.value.modelType = "product_line";
   }
   if (row.modelType == "product_line") {
      form.value.modelType = "station";
   }
   
   if (row.modelCode != undefined) {
      form.value.parentModelCode = row.modelCode;
   }
   open.value = true;
   title.value = t('basedata.factoryModel.message.add_title');
}
/** 展开/折叠操作 */
function toggleExpandAll() {
   refreshTable.value = false;
   isExpandAll.value = !isExpandAll.value;
   nextTick(() => {
      refreshTable.value = true;
   });
}
/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   if (row.parentModelCode != undefined){
      listDataAsync(baseQueryParams.value).then(response => {
         modelOptions.value = proxy.handleTree(response.data.items, "modelCode", "parentModelCode");  
         modelOptions.value == removeNodesByCondition(modelOptions.value, row.modelCode);
      });
   } 
   getData(row.id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

// 递归移除 条件节点及其子节点
function removeNodesByCondition(tree, modelCode) {
   for (let i = tree.length - 1; i >= 0; i--) {
      const node = tree[i]
      if (node.modelCode == modelCode) {
         tree.splice(i, 1) // 移除当前节点
         continue;
      }
      if (node.children) {
         removeNodesByCondition(node.children, modelCode)
      }
  }
  return tree
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["modelRef"].validate(valid => {
      if (valid) {
         getInfoAsync(form.value.modelCode).then(response => {
            if (form.value.id != undefined) {
               if (form.value.modelCode == form.value.parentCode) {
                  proxy.$modal.msgWarning("当前建模不可以作为自己的上级"); return;
               }
               if(response.data.length > 1){
                  proxy.$modal.msgWarning("编号重复,不允许保存"); return;
               }
               if(response.data.length == 1){
                  if(response.data[0].id != form.value.id){
                     proxy.$modal.msgWarning("编号重复,不允许保存"); return;
                  }
               }
               updateData(form.value).then(response => {
                  proxy.$modal.msgSuccess(t('basedata.factoryModel.message.editAlarm'));
                  open.value = false;
                  getList();
               });
            } else {
               if(response.data.length > 0){
                  proxy.$modal.msgWarning("编号重复,不允许保存"); return;
               }
               addData(form.value).then(response => {
                  proxy.$modal.msgSuccess(t('basedata.factoryModel.message.addAlarm'));
                  open.value = false;
                  getList();
               });
            }
         });
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   proxy.$modal.confirm(t('basedata.factoryModel.message.ifDelete') + row.modelName + t('basedata.factoryModel.message.ifData')).then(function () {
      return delData(row.id);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('basedata.factoryModel.message.deleteAlarm'));
   }).catch(() => { });
}
/** 同步按钮操作 */
function handleDown() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('basedata.factoryModel.message.success')+response.data);
      }, 1500);
   });
}
/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess("t('basedata.factoryModel.message.importAlarm')");
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>