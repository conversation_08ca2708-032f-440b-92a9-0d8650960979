import request from '@/utils/request'


export function listDataAsync(query) {
    return request({
        url: '/material/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/material',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/material/' + id,
        method: 'get'
    })
}

// 删除
export function delData(id) {
    return request({
        url: `/material/${id}`,
        method: 'delete',
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/material',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(data) {
    return request({
        url: `/material/${data.id}`,
        method: 'put',
        data: data
    })
}

// 同步数据
export function getDown() {
    return request({
        url: '/material/getDown',
        method: 'get'
    })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/material/' + roleId,
        method: 'get'
    })
}