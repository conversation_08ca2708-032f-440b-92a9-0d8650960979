<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="140px">
         <el-form-item :label="t('basedata.dictionary.queryField.dictName')" prop="dictName">
            <el-input v-model="queryParams.dictName" :placeholder="t('basedata.dictionary.queryField.tipContent')" clearable style="width: 240px" @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.dictionary.queryField.dictype')" prop="dictType">
            <el-input v-model="queryParams.dictType" :placeholder="t('basedata.dictionary.queryField.tipType')" clearable style="width: 240px" @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.dictionary.queryField.state')" prop="state">
            <el-select v-model="queryParams.state" :placeholder="t('basedata.dictionary.queryField.tipState')" clearable style="width: 240px">
               <el-option v-for="dict in sys_normal_disable" :key="JSON.parse(dict.value)" :label="dict.label" :value="JSON.parse(dict.value)" />
            </el-select>
         </el-form-item>
         <el-form-item :label="t('basedata.dictionary.queryField.creattime')" style="width: 408px">
            <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="t('basedata.dictionary.queryField.startTime')" :end-placeholder="t('basedata.dictionary.queryField.endTime')"></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('basedata.dictionary.button.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('basedata.dictionary.button.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:dict:add']">{{ $t('basedata.dictionary.button.increase') }}</el-button>
         </el-col>
         <el-col :span="1.5" v-if="false">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['basic:dict:edit']">{{ $t('basedata.dictionary.button.edit') }}</el-button>
         </el-col>
         <el-col :span="1.5" v-if="false">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['basic:dict:remove']">{{ $t('basedata.dictionary.button.delete') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['basic:dict:export']">导出</el-button>
         </el-col>
         <el-col :span="1.5" v-if="false">
            <el-button type="danger" plain icon="Refresh" @click="handleRefreshCache" v-hasPermi="['basic:dict:remove']">刷新缓存</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="typeList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="字典Id" align="center" prop="id" v-if="false" />
         <el-table-column :label="t('basedata.dictionary.tbColumn.dictName')" align="center" prop="dictName" :show-overflow-tooltip="true" />
         <el-table-column :label="t('basedata.dictionary.tbColumn.dicEngName')" align="center" prop="dictEngName" :show-overflow-tooltip="true" />
         <el-table-column :label="t('basedata.dictionary.tbColumn.dicPtName')" align="center" prop="dictPtName" :show-overflow-tooltip="true" />
         <el-table-column :label="t('basedata.dictionary.tbColumn.dicttype')" align="center" :show-overflow-tooltip="true">
            <template #default="scope">
               <span style="color: blue; cursor: pointer; " @click="handleToPath(scope.row.id)">{{ scope.row.dictType }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.dictionary.tbColumn.state')" align="center" prop="state">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.state" />
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.dictionary.tbColumn.remark')" align="center" prop="remark" :show-overflow-tooltip="true" />
         <el-table-column :label="t('basedata.dictionary.tbColumn.creationTime')" align="center" prop="creationTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.creationTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.dictionary.tbColumn.operate')" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:dict:edit']">{{ $t('basedata.dictionary.tbColumn.edit') }}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:dict:remove']">{{ $t('basedata.dictionary.tbColumn.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑参数配置对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form ref="dictRef" :model="form" :rules="rules" label-width="125px">
            <el-form-item :label="t('basedata.dictionary.elForm.dictName')" prop="dictName">
               <el-input v-model="form.dictName" :placeholder="t('basedata.dictionary.elForm.chinaTips')" />
            </el-form-item>
            <el-form-item :label="t('basedata.dictionary.elForm.dictEngName')" prop="dictEngName">
               <el-input v-model="form.dictEngName" :placeholder="t('basedata.dictionary.elForm.engTips')" />
            </el-form-item>
            <el-form-item :label="t('basedata.dictionary.elForm.dictPtName')" prop="dictPtName">
               <el-input v-model="form.dictPtName" :placeholder="t('basedata.dictionary.elForm.ptTips')" />
            </el-form-item>
            <el-form-item :label="t('basedata.dictionary.elForm.dictType')" prop="dictType">
               <el-input v-model="form.dictType" :placeholder="t('basedata.dictionary.elForm.tipType')" />
            </el-form-item>
            <el-form-item :label="t('basedata.dictionary.elForm.state')" prop="state">
               <el-radio-group v-model="form.state">
                  <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
               </el-radio-group>
            </el-form-item>
            <el-form-item :label="t('basedata.dictionary.elForm.remark')" prop="remark">
               <el-input v-model="form.remark" type="textarea" :placeholder="t('basedata.dictionary.elForm.tipContent')"></el-input>
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ $t('basedata.dictionary.elForm.confirm') }}</el-button>
               <el-button @click="cancel">{{ $t('basedata.dictionary.elForm.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Dict">
import useDictStore from '@/store/modules/dict'
import { listType, getType, delType, addType, updateType, refreshCache } from "@/api/basic/dict/type";
import router from '../../../router';
import { tr } from 'element-plus/es/locales.mjs';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const typeList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      dictName: undefined,
      dictType: undefined,
      state: true
   },
   rules: {
      dictName: [{ required: true, message: t('basedata.dictionary.message.alarm01'), trigger: "blur" }],
      dictType: [{ required: true, message: t('basedata.dictionary.message.alarm02'), trigger: "blur" }]
   },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询字典类型列表 */
function getList() {
   loading.value = true;
   listType(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
      typeList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      dictName: undefined,
      dictType: undefined,
      state: true,
      remark: undefined
   };
   proxy.resetForm("dictRef");
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   dateRange.value = [];
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('basedata.dictionary.message.addTitle');
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}
/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const dictId = row.id || ids.value;
   getType(dictId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('basedata.dictionary.message.editTitle');
   });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["dictRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateType(form.value).then(response => {
               proxy.$modal.msgSuccess(t("basedata.dictionary.message.editSuccess"));
               open.value = false;
               getList();
            });
         } else {
            addType(form.value).then(response => {
               proxy.$modal.msgSuccess(t("basedata.dictionary.message.addSuccess"));
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   const dictIds = row.id || ids.value;
   proxy.$modal.confirm(t("basedata.dictionary.message.delMsg")).then(function () {
      return delType(dictIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t("basedata.dictionary.message.delSuccess"));
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   proxy.download("basic/dict/type/export", {
      ...queryParams.value
   }, `dict_${new Date().getTime()}.xlsx`);
}
/** 刷新缓存按钮操作 */
function handleRefreshCache() {
   refreshCache().then(() => {
      proxy.$modal.msgSuccess(t("basedata.dictionary.message.refreshSuccess"));
      useDictStore().cleanDict();
   });
}

getList();

const handleToPath = (id) => {
   router.push({
      path: '/basic/dict-data/index',
      query: {
         dictId: id
      }
   })
}
</script>
