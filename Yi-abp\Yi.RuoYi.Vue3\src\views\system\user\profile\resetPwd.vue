<template>
   <el-form ref="pwdRef" :model="user" :rules="rules" label-width="80px">
      <el-form-item :label="t('common.userProfile.oldPwd')" prop="oldPassword">
         <el-input v-model="user.oldPassword" :placeholder="t('common.userProfile.oldPwdTip')" type="password" show-password />
      </el-form-item>
      <el-form-item :label="t('common.userProfile.newPwd')" prop="newPassword">
         <el-input v-model="user.newPassword" :placeholder="t('common.userProfile.newPwdTip')" type="password" show-password />
      </el-form-item>
      <el-form-item :label="t('common.userProfile.confirmPwd')" prop="confirmPassword">
         <el-input v-model="user.confirmPassword" :placeholder="t('common.userProfile.confirmPwdTip')" type="password" show-password/>
      </el-form-item>
      <el-form-item>
      <el-button type="primary" @click="submit">{{ t('common.userProfile.saveBtn') }}</el-button>
      <el-button type="danger" @click="close">{{ t('common.userProfile.colseBtn') }}</el-button>
      </el-form-item>
   </el-form>
</template>

<script setup>
import { updateUserPwd } from "@/api/system/user";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const { proxy } = getCurrentInstance();
const user = reactive({
  oldPassword: undefined,
  newPassword: undefined,
  confirmPassword: undefined
});

const equalToPassword = (rule, value, callback) => {
  if (user.newPassword !== value) {
    callback(new Error(t('common.userProfile.pwdComparisonMsg')));
  } else {
    callback();
  }
};
const rules = ref({
  oldPassword: [{ required: true, message: t('common.userProfile.newPwdVerify'), trigger: "blur" }],
  newPassword: [{ required: true, message: t('common.userProfile.oldPwdVerify'), trigger: "blur" }, { min: 6, max: 20, message: t('common.userProfile.newPwdRegex'), trigger: "blur" }],
  confirmPassword: [{ required: true, message: t('common.userProfile.confirmPwdVerify'), trigger: "blur" }, { required: true, validator: equalToPassword, trigger: "blur" }]
});

/** 提交按钮 */
function submit() {
  proxy.$refs.pwdRef.validate(valid => {
    if (valid) {
      updateUserPwd(user.oldPassword, user.newPassword).then(response => {
        proxy.$modal.msgSuccess(t('common.userProfile.editMsg'));
      });
    }
  });
};
/** 关闭按钮 */
function close() {
  proxy.$tab.closePage();
};
</script>
