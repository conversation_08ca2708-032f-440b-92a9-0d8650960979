import request from '@/utils/request'

// 分页查询
export function listData(query) {
  return request({
    url: '/order-ready/getListAsync',
    method: 'get',
    params: query
  })
}

// 查询列表
export function typeListData(query) {
  return request({
    url: '/order-ready',
    method: 'get',
    params: query
  })
}

// id查询
export function getData(id) {
  return request({
    url: `/order-ready/${id}`,
    method: 'get'
  })
}

// 新增
export function addData(data) {
  return request({
    url: '/order-ready',
    method: 'post',
    data: data
  })
}

// 编辑
export function updateData(id,data) {
  return request({
    url: `/order-ready/${id}`,
    method: 'put',
    data: data
  })
}

// 删除
export function delData(id) {
  return request({
    url: `/order-ready/${id}`,
    method: 'delete',
  })
}
