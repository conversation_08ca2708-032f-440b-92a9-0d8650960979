export default {
    equipList: {
        queryField: {
            lineCode: "Código linha",
            lineCodeTip: "Código linha",
            stationCode: "Código Estação",
            stationCodeTip: "Código Estação",
            machineCode: "Código máquina",
            machineCodeTip: "Código máquina",
        },
        tbColumn: {
            lineCode: "Código linha",
            stationCode: "Código Estação",
            resourceCode: "Código recursos",
            machineCode: "Código máquina",
            machineName: "Nome máquina",
            machineStatus: "Estado máquina",

            orderNum: "Número ordem",
            status: "Estado",
            remark: "Comentário",
            creatorId: "Criador Id",
            creatorName: "Criador Nome",
            creationTime: "Tempo criação",
            lastModifierId: "Último ID modificador",
            lastModifierName: "Último Nome modificador",
            lastModificationTime: "Tempo Modificação",
            isDeleted: "Apagado",
        },
        button: {
            search: "Procurar",
            reset: "Redefinir",
            add: "Adicionar",
            synchronous: "Sin<PERSON>roniza<PERSON>",
            import: "Importa<PERSON>",
            edit: "Editar",
            delete: "Apagar",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        elForm: {
            lineCode: "Código linha",
            lineCodeTip: "Código linha",
            stationCode: "Código Estação",
            stationCodeTip: "Código Estação",
            machineCode: "Código máquina",
            machineCodeTip: "Código máquina",
            resourceCode: "Código recursos",
            resourceCodeTip: "Código recursos",
            machineName: "Nome máquina",
            machineNameTip: "Nome máquina",
            machineStatus: "Estado máquina",
            machineStatusTip: "Estado máquina",
            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor, insira o conteúdo",
        },
        message: {
            alarmMsg01: "Por favor preencha o código de linha",
            alarmMsg02: "Por favor preencha o código da estação",
            alarmMsg03: "Por favor preencha o código de recurso",
            alarmMsg04: "Por favor preencha o código da máquina",

            addTitle: "Adicionar",
            addSuccess: "Adicionar sucesso",
            editTitle: "Editar",
            editSuccess: "Editar sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuccess: "Apagar sucesso",
            importSuccess: "Importação sucesso",
        }
    },
    equipLog: {
        queryField: {
            lineCode: "Código linha",
            lineCodeTip: "Código linha",
            stationCode: "Código estação",
            stationCodeTip: "Código estação",
            machineCode: "Código máquina",
            machineCodeTip: "Código máquina",
        },
        tbColumn: {
            id: "编号",
            lineCode: "Código linha",
            stationCode: "Código estação",
            machineCode: "Código máquina",
            resourceCode: "Código recursos",
            machineStatus: "Estado máquina",
            machineStatusBegin: "Estado Iniciar",
            machineStatusEnd: "Fim Estado",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Criador Id",
            creatorName: "Criador Nome",
            creationTime: "Tempo criação",
            lastModifierId: "Último ID modificador",
            lastModifierName: "Último Nome modificador",
            lastModificationTime: "Tempo Modificação",
            isDeleted: "Apagado",
        },
        button: {
            search: "Procurar",
            reset: "Redefinir",
            add: "Adicionar",
            synchronous: "Sincronização",
            import: "Importação",
            edit: "Editar",
            delete: "Apagar",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        elForm: {
            lineCode: "Código linha",
            lineCodeTip: "Código linha",
            stationCode: "Código estação",
            stationCodeTip: "Código estação",
            machineCode: "Código máquina",
            machineCodeTip: "Código máquina",
            resourceCode: "Código recursos",
            resourceCodeTip: "Código recursos",
            machineStatus: "Estado máquina",


            machineStatusTip: "Estado Iniciar",
            machineStatusBegin: "Estado Iniciar",
            machineStatusBeginTip: "Estado Iniciar",
            machineStatusEnd: "Fim Estado",
            machineStatusEndTip: "Fim Estado",
            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor, insira o conteúdo",
        },
        message: {
            alarmMsg01: "Por favor preencha o código de linha",
            alarmMsg02: "Por favor preencha o código da estação",
            alarmMsg03: "Por favor preencha o código da máquina",
            alarmMsg04: "Por favor preencha o código de recurso",

            addTitle: "Adicionar",
            addSuccess: "Adicionar sucesso",
            editTitle: "Editar",
            editSuccess: "Editar sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuccess: "Apagar sucesso",
            importSuccess: "Importação sucesso",
        },

    },
    equipWarning: {
        queryField: {
            lineCode: "Código linha",
            lineCodeTip: "Código linha",
            stationCode: "Código estação",
            stationCodeTip: "Código estação",
            machineCode: "Código máquina",
            machineCodeTip: "Código máquina",
        },
        tbColumn: {
            id: "编号",
            lineCode: "Código linha",
            stationCode: "Código estação",
            machineCode: "Código máquina",
            resourceCode: "Código recursos",
            deviceStatus: "Estado dispositivo",
            deviceAlarm: "Alarme dispositivo ",
            accumulatedPowerOnTime: "Poder no tempo",
            accumulatedOperationTime: "Tempo operação",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Criador Id",
            creatorName: "Criador Nome",
            creationTime: "Tempo criação",
            lastModifierId: "Último ID modificador",
            lastModifierName: "Último Nome modificador",
            lastModificationTime: "Tempo Modificação",
            isDeleted: "Apagado",
        },
        button: {
            search: "Procurar",
            reset: "Redefinir",
            add: "Adicionar",
            synchronous: "Sincronização",
            import: "Importação",
            edit: "Editar",
            delete: "Apagar",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        elForm: {
            lineCode: "Código linha",
            lineCodeTip: "Código linha",
            stationCode: "Código estação",
            stationCodeTip: "Código estação",
            machineCode: "Código máquina",
            machineCodeTip: "Código máquina",
            resourceCode: "Código recursos",
            resourceCodeTip: "Código recursos",
            deviceStatus: "Estado dispositivo",
            deviceStatusTip: "Estado dispositivo",
            deviceAlarm: "Alarme dispositivo ",
            deviceAlarmTip: "Alarme dispositivo ",
            accumulatedPowerOnTime: "Poder no tempo",
            accumulatedPowerOnTimeTip: "Poder no tempo",
            accumulatedOperationTime: "Tempo operação",
            accumulatedOperationTimeTip: "Tempo operação",
            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor, insira o conteúdo",
        },
        message: {
            alarmMsg01: "Por favor preencha o código de linha",
            alarmMsg02: "Por favor preencha o código da estação",
            alarmMsg03: "Por favor preencha o código da máquina",
            alarmMsg04: "Por favor preencha o código de recurso",

            addTitle: "Adicionar",
            addSuccess: "Adicionar sucesso",
            editTitle: "Editar",
            editSuccess: "Editar sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuccess: "Apagar sucesso",
            importSuccess: "Importação sucesso",
        },
    }
}