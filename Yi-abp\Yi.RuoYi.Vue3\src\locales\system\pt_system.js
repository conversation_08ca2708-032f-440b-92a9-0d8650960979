export default {
    user: {
        query: {
            deptName: "Nome departamento",
            userName: "Nome usuário",
            phone: "Telefone",
            userState: "Estado usuário",
            creationTime: "Tempo criação",
            creationTime1: "Tempo início",
            creationTime2: "Tempo fim",
        },
        button: {
            search: "Pesquisar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            export: "Exportar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
            resetPwd: "Redefinir senha",
        },
        tbCol: {
            userId: "ID usuário",
            userName: "Nome Usuário",
            nickName: "Apelido",
            gender: "Gênero",
            deptName: "Nome Departamento",
            phone: "Telefone",
            userState: "Estado Usuário",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
            nickName: "Apelido",
            deptName: "Nome Departamento",
            phone: "Telefone",
            e_mail: "E-mail",
            userName: "Nome Usuário",
            password: "Senha",
            gender: "Gênero",
            userState: "Estado Usuário",
            posts: "Post",
            roles: "Rôlo",
            creationTime: "Tempo criação",
            plsChoice: "Por favor, escolha",
            status: "Status",
            remark: "Observação",
            remarkTip: "Por favor insira o conteúdo",

            dragFile: "Arraste o arquivo aqui, ou",
            clickUpload: "Clique para Enviar",
            onlyAllowImport: "Apenas arquivos nos formatos xls e xlsx são permitidos",
            downloadTemplate: "Baixar Modelo",
        },
        message: {
            alarm01: "O nome do usuário não pode estar vazio",
            alarm02: "O nome do usuário deve ter entre 2 e 20 caracteres",
            alarm03: "O apelido do usuário não pode estar vazio",
            alarm04: "Por favor, insira um endereço de e-mail válido",
            alarm05: "Por favor, insira seu número de telefone",

            enable: "Ativado",
            disable: "Desativado",
            useTips1: "Tem certeza de que deseja ",
            useTips2: " usuários?",
            useTips3: "Sucesso",

            pwdTips1: "Por favor, insira a nova senha para:",
            pwdTips2: "Aviso",
            pwdTips3: "OK",
            pwdTips4: "Cancelar",
            pwdTips5: "A senha do usuário deve ter entre 5 e 20 caracteres",
            pwdTips6: "Editado com sucesso, a nova senha é:",

            importTips: "Importar Usuários",
            importTips1: "Resultados Importar",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",
        }
    },
    authRole: {
        basicInfo: "Informações Básicas",
        nickName: "Apelido",
        userName: "Conta Usuário",
        roleInfo: "Informações Rôlo",
        roleId: "ID Rôlo",
        roleName: "Nome Rôlo",
        roleKey: "Chave Rôlo",
        createTime: "Data Criação",
        submit: "Enviar",
        goBack: "Voltar",
        grantAccess: "Conceder Acesso",
    },
    role: {
        query: {
            roleName: "Nome Rôlo",
            roleCode: "Código Rôlo",
            state: "Estado Rôlo",
            creationTime: "Data Criação",
            startTime: "Tempo início",
            endTime: "Tempo fim",
        },
        button: {
            search: "Pesquisar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            export: "Exportar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
            dataAuth: "Autorização Dados",
            userAuth: "Autorização Usuário",
        },
        tbCol: {
            roleCode: "Código Rôlo",
            roleName: "Nome Rôlo",
            roleKey: "Chave Rôlo",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
            roleName: "Nome Rôlo",
            roleCode: "Código Rôlo",
            roleKey: "Chave Rôlo",
            roleCodeTip: "A chave da Rôlo definida no controlador, como",
            roleCodeTip1: "Chave Rôlo",
            roleCodeTip2: "Por favor, preencha a Chave da Rôlo",
            menuAuth: "Autorização Menú",
            menuAuth1: "Alternar/Expandir",
            menuAuth2: "Todos/Cancelar Todos",
            menuAuth3: "Pai/Filho",
            menuAuth4: "Carregando...",
            authScope: "Escope Autorização",
            dataAuth: "Autorização Dados",
            dataAuth1: "Alternar/Expandir",
            dataAuth2: "Todos/Cancelar Todos",
            dataAuth3: "Pai/Filho",
            dataAuth4: "Carregando...",
            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor insira o conteúdo",
        },
        message: {
            alarm01: "autorização de todos os dados",
            alarm02: "autorização de dados personalizada",
            alarm03: "autorização de dados do departamento",
            alarm04: "autorização de dados do departamento e abaixo",
            alarm05: "autorização do usuário atual",
            alarm06: "O Nome da Rôlo não pode estar vazio",
            alarm07: "A Chave da Rôlo não pode estar vazia",
            alarm08: "O Número de Ordem não pode estar vazio",
            enabled: "ativado",
            disabled: "desativado",
            useTips1: "Tem certeza de que deseja ",
            useTips2: " esse papel?",
            useTips3: "Sucesso",
            dataAuth: "Autorização dados",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",
        }
    },
    authUser: {
        userName: "Nome do Usuário",
        phone: "Telefone",
        search: "Pesquisar",
        reset: "Redefinir",
        add: "Adicionar",
        cancleAuth: "Cancelar Autorização",
        cancleTip: "Deseja cancelar a autorização?",
        cancleAcc: "Autorização Cancelada com Sucesso",
        close: "Fechar",
        nickName: "Apelido",
        email: "E-mail",
        status: "Estado",
        creationTime: "Data Criação",
        operation: "Operação",
    },
    sltUser: {
        choice: "Escolher Usuário",
        userName: "Nome Usuário",
        phone: "Telefone",
        nickName: "Apelido",
        email: "E-mail",
        status: "Estado",
        creationTime: "Data Criação",
        search: "Pesquisar",
        reset: "Redefinir",
        confirm: "Confirmar",
        cancle: "Cancelar",
        choiceUser: "Por favor, escolha um usuário",
        success: "Sucesso",
    },
    menu: {
        query: {
            menuName: "Nome Menú",
            status: "Estado",
        },
        button: {
            search: "Pesquisar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            export: "Exportar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",

            toggleExpand: "Alternar/Expandir",
        },
        tbCol: {
            menuName: "Nome Menú",
            engName: "Nome Inglês",
            ptName: "Nome Português",
            menuIcon: "Ícone Menú",
            permissionCode: "Código Permissão",
            router: "Roteador",
            component: "Componente",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
            lastMenu: "Último Menú",
            menuType: "Tipo Menú",
            catalogue: "Catálogo",
            menu: "Menú",
            button: "Botão",
            menuIcon: "Ícone Menú",
            choiceIcon: "Escolher Ícone",
            menuName: "Nome Chinês",
            engName: "Nome Inglês",
            ptName: "Nome Português",
            orderNum: "Ordem",
            outLink: "Link Externo",
            linkTip: "Por favor, preencha o endereço do roteador, como `http(s)://`",
            yes: "Sim",
            no: "Não",
            router: "Caminho Rota",
            routerContent: "O caminho da rota, como: `user`. Se o endereço da rede externa exigir acesso por link interno, comece com 'http'",
            component: "caminho componente",
            componentContent: "O caminho da rota, como: 'system/user/index'. Diretório padrão 'views'",
            permission: "Bytes permissão   ",
            permissionContent: "Os bytes de permissão definidos no controlador, como @PreAuthorize(`@ss.hasPermit('system:user:list')  `)",
            query: "Parâmetros Rota",
            queryContent: "Acessando os parâmetros padrão passados para RouteParams, como: {\"id\": 1, \"name\": \"ry\"}`",
            cache: "Está Cache",
            cacheYes: "Em Cache",
            cacheNo: "Não Cache",
            cacheContent: "Será armazenado em cache por 'keep-alive' após escolher 'sim'. É necessário corresponder ao 'nome' e 'endereço' do componente para garantir consistência",
            show: "Mostrar estado",
            showContent: "O roteador não aparecerá na barra lateral após escolher 'ocultar', mas ainda estará acessível",
            menuState: "Estado Menú",
            menuStateContent: "O roteador não aparecerá na barra lateral após escolher 'desativar' e não estará acessível",

            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor, preencha o conteúdo",
        },
        message: {
            alarm01: "O Nome do Menú não pode estar vazio",
            alarm02: "O Nome em Inglês não pode estar vazio",
            alarm03: "O Nome em Português não pode estar vazio",
            alarm04: "A ordem do Menú não pode estar vazia",
            alarm05: "O caminho da rota não pode estar vazio",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            mainClass: "Categoria principal",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",
        }
    },
    httpOption: {
        query: {
            serviceId: "Instância Serviço",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            export: "Exportar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",

            toggleExpand: "Alternar/Expandir",
        },
        tbCol: {
            name: "Nome",
            appId: "AppId",
            appKey: "AppKey",
            facCode: "Código Fábrica",
            serviceId: "Id Serviço",
            httpUrl: "URL HTTP",
            httpRoute: "Rota HTTP",
            updateTime: "Tempo Atualização",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
            name: "Nome",
            appId: "AppId",
            appKey: "AppKey",
            facCode: "Código Fábrica",
            serviceId: "Id Serviço",
            httpUrl: "URL HTTP",
            httpRoute: "Rota HTTP",
            updateTime: "Tempo Atualização",

            status: "Status",
            remark: "Observação",
            remarkTip: "Por favor, preencha o conteúdo",
        },
        message: {
            alarm01: "Por favor, preencha o nome",
            alarm02: "Por favor, preencha o AppId",
            alarm03: "Por favor, preencha o AppKey",
            alarm04: "Por favor, preencha o código da fábrica",
            alarm05: "Por favor, preencha a instância de serviço",
            alarm06: "Por favor, preencha o endereço URL",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",
        }
    },
}