<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
      <el-form-item :label="t('wkorder.orderRoute.query.orderCode')" prop="orderCode">
        <el-input v-model="queryParams.orderCode" :placeholder="t('wkorder.orderRoute.query.orderCode')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('wkorder.orderRoute.query.scheduleCode')" prop="scheduleCode" label-width="160px">
        <el-input v-model="queryParams.scheduleCode" :placeholder="t('wkorder.orderRoute.query.scheduleCode')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('wkorder.orderRoute.query.routeNumber')" prop="routeNumber">
        <el-input v-model="queryParams.routeNumber" :placeholder="t('wkorder.orderRoute.query.routeNumber')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('wkorder.orderRoute.query.operationCode')" prop="operationCode" label-width="130px">
        <el-input v-model="queryParams.operationCode" :placeholder="t('wkorder.orderRoute.query.operationCode')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{t('wkorder.orderRoute.button.search')}}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{t('wkorder.orderRoute.button.reset')}}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['wkorder:orderroute:add']">{{t('wkorder.orderRoute.button.add')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['wkorder:orderroute:down']" :loading="downLoading">{{t('wkorder.orderRoute.button.sync')}}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-upload class="upload-demo" :disabled="importDisabled" :on-change="handleImport" accept="xls,xlsx" :auto-upload="false" :multiple="true" :limit="2" :show-file-list ="false">
          <el-button type="warning" plain icon="Download" v-hasPermi="['wkorder:orderroute:import']" :loading="importLoading">{{t('wkorder.orderRoute.button.import')}}</el-button>
        </el-upload>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" ></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
      <el-table-column type="selection" width="30" align="center" />
      <el-table-column label="编号" align="center" prop="id" v-if="false" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.orderCode')" align="center" prop="orderCode" min-width="140" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.scheduleCode')" align="center" prop="scheduleCode" min-width="160" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.routeNumber')" align="center" prop="routeNumber" min-width="280" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.routeVersion')" align="center" prop="routeVersion" min-width="130" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.lastOperationCode')" align="center" prop="lastOperationCode" min-width="160" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.nextOperationCode')" align="center" prop="nextOperationCode" min-width="140" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.operationCode')" align="center" prop="operationCode" min-width="120" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.operationName')" align="center" prop="operationName" min-width="140" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.sort')" align="center" prop="sort" min-width="80" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.stationCode')" align="center" prop="stationCode" min-width="120" :show-overflow-tooltip=true />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.editTime')" align="center" prop="editTime" min-width="155" />

      <el-table-column :label="t('wkorder.orderRoute.tbCol.orderNum')" prop="orderNum" v-if="false"> </el-table-column>
      <el-table-column :label="t('wkorder.orderRoute.tbCol.status')" prop="status" v-if="false">
         <template #default="scope">
            <dict-tag :options="base_order_state"    :value="scope.row.status" />
         </template>
      </el-table-column>
      <el-table-column :label="t('wkorder.orderRoute.tbCol.remark')" prop="remark" v-if="false" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.creatorId')" prop="creatorId" v-if="false" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.creatorName')" prop="creatorName" v-if="false" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.creationTime')" prop="creationTime" width="155" v-if="false" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.lastModifierId')" prop="lastModifierId" v-if="false" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.lastModifierId')" prop="lastModifierName" v-if="false" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
      <el-table-column :label="t('wkorder.orderRoute.tbCol.ifdelete')" prop="isDeleted" v-if="false" />
      
      <el-table-column :label="t('wkorder.orderRoute.tbCol.operation')" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
        <template #default="scope">
          <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['wkorder:orderroute:edit']">{{t('wkorder.orderRoute.button.edit')}}</el-button>
          <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['wkorder:orderroute:remove']">{{t('wkorder.orderRoute.button.delete')}}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

    <!-- 添加或编辑对话框 -->
    <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
      <el-form ref="submitRef" :model="form" :rules="rules" label-width="160px">
        <el-row>
          <el-col :span="12">
            <el-form-item :label="t('wkorder.orderRoute.form.orderCode')" prop="orderCode">
              <el-input v-model="form.orderCode" :placeholder="t('wkorder.orderRoute.form.orderCode')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.scheduleCode')" prop="scheduleCode" label-width="200px">
              <el-input v-model="form.scheduleCode" :placeholder="t('wkorder.orderRoute.form.scheduleCode')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.routeNumber')" prop="routeNumber">
              <el-input v-model="form.routeNumber" :placeholder="t('wkorder.orderRoute.form.routeNumber')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.routeVersion')" prop="routeVersion">
              <el-input v-model="form.routeVersion" :placeholder="t('wkorder.orderRoute.form.routeVersion')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.lastOperationCode')" prop="lastOperationCode">
              <el-input v-model="form.lastOperationCode" :placeholder="t('wkorder.orderRoute.form.lastOperationCode')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.nextOperationCode')" prop="nextOperationCode">
              <el-input v-model="form.nextOperationCode" :placeholder="t('wkorder.orderRoute.form.nextOperationCode')" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item :label="t('wkorder.orderRoute.form.operationCode')" prop="operationCode">
              <el-input v-model="form.operationCode" :placeholder="t('wkorder.orderRoute.form.operationCode')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.operationName')" prop="operationName">
              <el-input v-model="form.operationName" :placeholder="t('wkorder.orderRoute.form.operationName')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.sort')" prop="sort">
              <el-input v-model="form.sort" :placeholder="t('wkorder.orderRoute.form.sort')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.editTime')" prop="editTime">
              <el-input v-model="form.editTime" :placeholder="t('wkorder.orderRoute.form.editTime')" />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item :label="t('wkorder.orderRoute.form.stationCode')" prop="stationCode">
              <el-input v-model="form.stationCode" type="textarea" rows="5" :placeholder="t('wkorder.orderRoute.form.stationCode')" />
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.status')" v-if="false">
              <el-radio-group v-model="form.status">
                <el-radio v-for="dict in base_is_open" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item :label="t('wkorder.orderRoute.form.remark')" prop="remark">
              <el-input v-model="form.remark" type="textarea" rows="5" :placeholder="t('wkorder.orderRoute.form.remarkTip')" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OrderRoute">
/** 引入----------------------------------------------------------------------------*/
import {  listDataAsync,  listData,  getData,  delData,  addData,  updateData,  getDown, importAsync} from "@/api/wkorder/orderroute";
import * as XLSX from 'xlsx';  
import moment from 'moment';
import { genFileId } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { ElConfigProvider } from 'element-plus';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

/** 定义变量----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const importDisabled = ref(false);
const tableData = ref([]);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
  form: {},
  queryParams: {
    skipCount: 1,
    maxResultCount: 10,
    Sorting: undefined,
  },
  rules: {
    orderCode: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg01'), trigger: "blur" }],
    scheduleCode: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg02'), trigger: "blur" }],
    routeNumber: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg03'), trigger: "blur" }],
    routeVersion: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg04'), trigger: "blur" }],
    lastOperationCode: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg05'), trigger: "blur" }],
    nextOperationCode: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg06'), trigger: "blur" }],
    operationCode: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg07'), trigger: "blur" }],
    operationName: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg08'), trigger: "blur" }],
    sort: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg09'), trigger: "blur" }],
    stationCode: [{ required: true, message: t('wkorder.orderRoute.message.alarmMsg10'), trigger: "blur" }],
  },
});

const { queryParams, form, rules } = toRefs(data);

/** 列表操作 ----------------------------------------------------------------------------*/
function getList() {
  loading.value = true;
  listDataAsync(queryParams.value).then((response) => {
    dataList.value = response.data.items;
    total.value = response.data.totalCount;
    loading.value = false;
  });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.skipCount = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 编辑页面数据----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value =t('wkorder.orderRoute.message.addTit');
}
/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const postId = row.id || ids.value;
  getData(postId).then((response) => {
    form.value = response.data;
    open.value = true;
    title.value = t('wkorder.orderRoute.message.editTit');
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const postIds = row.id || ids.value;
  proxy.$modal
    .confirm(t('wkorder.orderRoute.message.delMsg'))
    .then(function () {
      return delData(postIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess(t('wkorder.orderRoute.message.delSuc'));
    })
    .catch(() => {});
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["submitRef"].validate((valid) => {
    if (valid) {
      if (form.value.id != undefined) {
        updateData(form.value).then((response) => {
          proxy.$modal.msgSuccess(t('wkorder.orderRoute.message.editSuc'));
          open.value = false;
          getList();
        });
      } else {
        addData(form.value).then((response) => {
          proxy.$modal.msgSuccess(t('wkorder.orderRoute.message.addSuc'));
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
  };
  proxy.resetForm("submitRef");
}

/** 数据同步----------------------------------------------------------------------------*/
function handleDown() {
   if(queryParams.value.orderCode == undefined)
   {
      proxy.$modal.msgWarning(t('wkorder.orderRoute.message.syncTit01'));
      return;
   }
   if(queryParams.value.scheduleCode == undefined)
   {
      proxy.$modal.msgWarning(t('wkorder.orderRoute.message.syncTit02'));
      return;
   }
   downLoading.value = true;
   getDown(queryParams.value.orderCode,queryParams.value.scheduleCode).then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('wkorder.orderRoute.message.syncSuc')+":"+response.data);
      }, 1500);
   });
}

/** Excel导入----------------------------------------------------------------------------*/
/** 导入按钮操作 */
const handleImport = async (file,files) => {
   importLoading.value = true;
   importDisabled.value = true;
   if(files.length > 1){
      files.shift();
   };
   const data = await getXlsxData(file);
   tableData.value = translateField(data);

   // 数字转字符串
   tableData.value = JSON.parse(JSON.stringify(tableData.value, (key, value) => typeof value === 'number' ? String(value) : value));

   importAsync(tableData.value).then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         importLoading.value = false;
         importDisabled.value = false;
         proxy.$modal.msgSuccess(t('wkorder.orderRoute.message.importSuc')+":"+response.data);
      }, 1500);
   });
}
//读取表格数据
const getXlsxData = async (file) => {
   const dataBinary = await readFile(file);
   const workBook = XLSX.read(dataBinary ,{
      type: "binary",
      cellDates: true
   });
   const workSheet = workBook.Sheets[workBook.SheetNames[0]];
   const data = XLSX.utils.sheet_to_json(workSheet);
   data.forEach((item) => {
      const keys = ["StartTime","EndTime"];
      for (const key in item) {
         if (moment(item[key], 'YYYY-MM-DD', true).isValid()) {
            item[key] = moment(item[key]).format("YYYY-MM-DD HH:mm:ss")
         }
      }
   });
   return data;
}
//读取excel文件
const readFile = (file) => {
   return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file.raw)
      reader.onload = (e) => {
         resolve(e.target.result)
      }
      reader.onerror = (e) => {
         reject(e)
      }
   })
}
//映射字段
const translateField = (data) => {
  const arr = []
  const cnToEn = {
      工单编号:'OrderCode',
      排程编号:'ScheduleCode',
      工艺编码:'RouteNumber',
      工艺版本:'RouteVersion',
      工序编号:'OperationCode',
      工序名称:'OperationName',
      工序顺序:'Sort',
      前序编号:'LastOperationCode',
      后序遍号:'NextOperationCode',
      工站合集:'StationCode',
      更新时间:'EditTime',
   }
   data.forEach((item) => {
      const arrItem = {}
      Object.keys(item).forEach((key) => {
         arrItem[cnToEn[key]] = item[key]
      })
      arr.push(arrItem) 
   })
   return arr
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>