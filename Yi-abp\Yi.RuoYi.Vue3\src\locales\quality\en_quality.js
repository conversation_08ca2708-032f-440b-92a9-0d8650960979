export default {
    badCode: {
        query: {
            scheduleCode: "排程编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            snNumber: "Sn编号",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        tbCol: {
            snNumber: "Sn编号",
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            factoryCode: "工厂编号",
            wkShopCode: "车间编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            bomId: "BomId",
            bomType: "Bom类型",
            bomVersion: "Bom版本",
            routeNumber: "工艺编号",
            routeVersion: "工艺版本",
            onlineTime: "上线时间",
            offLineTime: "下线时间",
            shiftNo: "班组",
            shiftTime: "班次",
            isNeedCheck: "检验件",
            isWarning: "是否安灯",
            badData: "是否不良",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
        },
        message: {
        }
    },
    badRecord: {
    },
    uniqueRetrace: {
    }
}