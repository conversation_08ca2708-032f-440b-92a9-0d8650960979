export default {
    badCode: {
        query: {
            operationCode: "OperationCode",
            badCode: "BadCode",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        tbCol: {
            operationCode: "OperationCode",
            badCode: "BadCode",
            badName: "BadName",
            badTypeCode: "BadTypeCode",
            badTypeName: "BadTypeName",
            enableStatus: "EnableStatus",
            dataStatus: "DataStatus",
            addTime: "AddTime",
            editTime: "EditTime",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            operationCode: "OperationCode",
            badCode: "BadCode",
            badName: "BadName",
            badTypeCode: "BadTypeCode",
            badTypeName: "BadTypeName",
            enableStatus: "EnableStatus",
            dataStatus: "DataStatus",
            addTime: "AddTime",
            editTime: "EditTime",

            status: "Status",
            remark: "Remark",
            remarkTip: "Remark",
        },
        message: {
            alarm01: "Please fill in the operation code",
            alarm02: "Please fill in the bad code",
            alarm03: "Please fill in the bad name",
            alarm04: "Please fill in the bad type code",
            alarm05: "Please fill in the bad type name",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    badData: {
        query: {
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            lineCode: 'LineCode',
            operationCode: "OperationCode",
            snNumber: "SnCode",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        tbCol: {
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            snNumber: "SnCode",
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",
            lineCode: "LineCode",
            stationCode: "StationCode",
            operationCode: "OperationCode",
            badCode: "BadCode",
            badFactor: "BadFactor",
            badQty: "BadQty",
            userId: "UserId",
            editTime: "CreateTime",
            description: "Description",
            auditOpinion: "AuditOpinion",


            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            snNumber: "SnCode",
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",
            lineCode: "LineCode",
            stationCode: "StationCode",
            operationCode: "OperationCode",
            badCode: "BadCode",
            badFactor: "BadFactor",
            badQty: "BadQty",
            userId: "UserId",
            editTime: "CreateTime",
            images: "Images",
            description: "Description",
            auditOpinion: "AuditOpinion",

            status: "Status",
            remark: "Remark",
            remarkTip: "Remark",
        },
        message: {
            alarm01: "Please choice the state",
            alarm02: "Please fill in the audit opinions",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    retrace: {
        query: {
            snNumber: "SnCode",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        tbCol: {
            orderCode: "OrderCode",
            orderType: "OrderType",
            orderQty: "OrderQty",
            orderStatus: "OrderStatus",
            lineCode: "LineCode",

            scheduleCode: "ScheduleCode",
            scheduleQty: "ScheduleQty",
            scheduleStatus: "ScheduleStatus",
            planStartTime: "PlanStartTime",
            planEndTime: "PlanEndTime",

            psStation: "PassStation",
            stationCode: "StationCode",
            passBeginTime: "PassBeginTime",
            passEndTime: "PassEndTime",

            mBind: "MaterialBind",
            operationCode: "OperationCode",
            assemblyMaterialCode: "AssemblyMaterialCode",
            assemblyTime: "AssemblyTime",
            assemblyMaterialSn: "AssemblyMaterialSn",

            wkParams: "WorkParams",
            operationCode: "OperationCode",
            creationTime: "CreationTime",
            paramCode: "ParamCode",
            standardRange1: "StandardRange1",
            standardRange2: "StandardRange2",
            standardValue: "StandardValue",
            targetValue: "TargetValue",
            realValue: "RealValue",
            checkResult: "CheckResult",

            andon: "Andon",
            stationCode: "StationCode",
            unusualAlarmCode: "UnusualAlarmCode",
            lastModificationTime: "LastModificationTime",
            remark: "Remark",
            lastModifierName: "LastModifierName",

            badData: "BadData",
            operationCode: "OperationCode",
            badCode: "BadCode",
            badFactor: "BadFactor",
            description: "Description",
            lastModificationTime: "LastModificationTime",
            lastModifierName: "LastModifierName",
            auditOpinion: "AuditOpinion",
        },
        form: {},
        message: {},
    }
}