<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
         <el-form-item :label="t('basedata.material.queryField.materialCode')" prop="materialCode">
            <el-input v-model="queryParams.materialCode" :placeholder="t('basedata.material.queryField.materialCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.material.queryField.pnCode')" prop="pnCode">
            <el-input v-model="queryParams.pnCode" :placeholder="t('basedata.material.queryField.pnCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('basedata.material.button.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('basedata.material.button.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:material:add']">{{ $t('basedata.material.button.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['basic:material:down']" :loading="downLoading">{{ $t('basedata.material.button.synchronous') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['basic:material:import']" :loading="importLoading" v-if="false">{{ $t('basedata.material.button.import') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>


      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column type="selection" width="50" align="center" />
         <el-table-column :label="t('basedata.material.tbColumn.id')" v-if="false" align="center" prop="id" />
         <el-table-column :label="t('basedata.material.tbColumn.materialCode')" align="center" fixed="left" prop="materialCode" min-width="165" />
         <el-table-column :label="t('basedata.material.tbColumn.materialName')" align="center" fixed="left" prop="materialName" min-width="264" />
         <el-table-column :label="t('basedata.material.tbColumn.materialType')" align="center" prop="materialType" min-width="80" />
         <el-table-column :label="t('basedata.material.tbColumn.materialVersion')" align="center" prop="materialVersion" min-width="80" />
         <el-table-column :label="t('basedata.material.tbColumn.pnCode')" align="center" prop="pnCode" min-width="220" />
         <el-table-column :label="t('basedata.material.tbColumn.pnShortCode')" align="center" prop="pnShortCode" min-width="120" />
         <el-table-column :label="t('basedata.material.tbColumn.pnName')" align="center" prop="pnName" min-width="260" />
         <el-table-column :label="t('basedata.material.tbColumn.measureUnitCode')" align="center" prop="measureUnitCode" min-width="140" />
         <el-table-column :label="t('basedata.material.tbColumn.measureUnitName')" align="center" prop="measureUnitName" min-width="140" />
         <el-table-column :label="t('basedata.material.tbColumn.measureUnitQuantity')" align="center" prop="measureUnitQuantity" min-width="140" />
         <el-table-column :label="t('basedata.material.tbColumn.regular')" align="center" prop="regular" min-width="144" />
         <el-table-column :label="t('basedata.material.tbColumn.productModelCode')" align="center" prop="productModelCode" min-width="124" />
         <el-table-column :label="t('basedata.material.tbColumn.effactTime')" align="center" prop="effactTime" min-width="155" />
         <el-table-column :label="t('basedata.material.tbColumn.enableStatus')" align="center" prop="enableStatus" min-width="80" />
         <el-table-column :label="t('basedata.material.tbColumn.dataStatus')" align="center" prop="dataStatus" min-width="120" />
         <el-table-column :label="t('basedata.material.tbColumn.addTime')" align="center" prop="addTime" min-width="155" />
         <el-table-column :label="t('basedata.material.tbColumn.editTime')" align="center" prop="editTime" min-width="155" />
         <el-table-column :label="t('basedata.material.tbColumn.oldSortID')" align="center" prop="oldSortID" width="100" v-if="false" />
         <el-table-column :label="t('basedata.material.tbColumn.newSortID')" align="center" prop="newSortID" width="100" v-if="false" />

         <el-table-column prop="orderNum" :label="t('basedata.material.tbColumn.order')" v-if="false"></el-table-column>
         <el-table-column prop="status" :label="t('basedata.material.tbColumn.status')">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.material.tbColumn.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('basedata.material.tbColumn.creator')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('basedata.material.tbColumn.creator')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('basedata.material.tbColumn.creatTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('basedata.material.tbColumn.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('basedata.material.tbColumn.lastModifierId')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('basedata.material.tbColumn.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('basedata.material.tbColumn.ifdelete')" prop="isDeleted" v-if="false" />

         <el-table-column :label="t('basedata.material.tbColumn.operate')" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:material:edit']">{{ $t('basedata.material.button.edit') }}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:material:remove']">{{ $t('basedata.material.button.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="180px">
            <el-row>   
               <el-col :span="12">
                  <el-form-item :label="t('basedata.material.elForm.materialCode')" prop="materialCode">
                     <el-input v-model="form.materialCode" :placeholder="t('basedata.material.elForm.codeTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.materialName')" prop="materialName">
                     <el-input v-model="form.materialName" :placeholder="t('basedata.material.elForm.nameTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.materialType')" prop="materialType">
                     <el-input v-model="form.materialType" :placeholder="t('basedata.material.elForm.typeTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.materialVersion')" prop="materialVersion">
                     <el-input v-model="form.materialVersion" :placeholder="t('basedata.material.elForm.versionTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.pnCode')" prop="pnCode">
                     <el-input v-model="form.pnCode" :placeholder="t('basedata.material.elForm.pncodeTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.pnName')" prop="pnName">
                     <el-input v-model="form.pnName" :placeholder="t('basedata.material.elForm.pnNametIP')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.pnShortCode')" prop="pnShortCode">
                     <el-input v-model="form.pnShortCode" :placeholder="t('basedata.material.elForm.pnShortCodeTip')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('basedata.material.elForm.measureUnitCode')" prop="measureUnitCode">
                     <el-input v-model="form.measureUnitCode" :placeholder="t('basedata.material.elForm.measureUnitCodeTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.measureUnitName')" prop="measureUnitName">
                     <el-input v-model="form.measureUnitName" :placeholder="t('basedata.material.elForm.measureUnitNameTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.measureUnitQuantity')" prop="measureUnitQuantity">
                     <el-input-number v-model="form.measureUnitQuantity" precision="3" :min="0" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.regular')" prop="regular">
                     <el-input v-model="form.regular" :placeholder="t('basedata.material.elForm.regularTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.productModelCode')" prop="productModelCode">
                     <el-input v-model="form.productModelCode" :placeholder="t('basedata.material.elForm.productModelCodeTip')" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.effactTime')" prop="effactTime">
                     <el-date-picker v-model="form.effactTime" type="date" placeholder="Pick a day" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                  </el-form-item>
                  <el-form-item :label="t('basedata.material.elForm.state')">
                     <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in base_is_open" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
                 <el-col :span="24">
                  <el-form-item :label="t('basedata.material.elForm.remark')" prop="remark">
                     <el-input v-model="form.Remark" type="textarea" rows="3" :placeholder="t('basedata.material.elForm.remarkTip')" />
                  </el-form-item>
                 </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ $t('basedata.material.button.confirm') }}</el-button>
               <el-button @click="cancel">{{ $t('basedata.material.button.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Material">
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/basic/material";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      materialCode: [{ required: true, message: t('basedata.material.message.codeAlarm'), trigger: "blur" }],
      materialName: [{ required: true, message: t('basedata.material.message.nameAlarm'), trigger: "blur" }],
      materialType: [{ required: true, message: t('basedata.material.message.typeAlarm'), trigger: "blur" }],
      pnCode: [{ required: true, message: t('basedata.material.message.pnAlarm'), trigger: "blur" }],
      measureUnitName: [{ required: true, message: t('basedata.material.message.measureUnitNameAlarm'), trigger: "blur" }],
      measureUnitQuantity: [{ required: true, message: t('basedata.material.message.measureUnitQuantityAlarm'), trigger: "blur" }],
      effactTime: [{ required: true, message: t('basedata.material.message.timeAlarm'), trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      materialCode: undefined,
      materialName: undefined,
      materialType: undefined,
      pnCode: undefined,
      measureUnitName: undefined,
      measureUnitQuantity: undefined,
      effactTime: undefined,
      status: 1,
      remark: undefined
   };
   proxy.resetForm("submitRef");
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('basedata.material.message.ifdelete') + postIds + t('basedata.material.message.ifDAta')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('basedata.material.message.deleteAlarm'));
   }).catch(() => { });
}

/** 同步按钮操作 */
function handleDown() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('basedata.material.message.successAlarm')+response.data);
      }, 1500);
   });
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('basedata.material.message.editAlarm'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('basedata.material.message.addAlarm'));
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess(t('basedata.material.message.importAlarm'));
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>