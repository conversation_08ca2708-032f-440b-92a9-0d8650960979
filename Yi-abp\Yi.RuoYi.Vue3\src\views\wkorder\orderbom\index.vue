<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="158px">
         <el-form-item :label="t('wkorder.orderBom.query.orderCode')" prop="orderCode">
            <el-input v-model="queryParams.orderCode" :placeholder="t('wkorder.orderBom.query.orderCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('wkorder.orderBom.query.scheduleCode')" prop="scheduleCode">
            <el-input v-model="queryParams.scheduleCode" :placeholder="t('wkorder.orderBom.query.scheduleCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('wkorder.orderBom.query.materialCode')" prop="materialCode">
            <el-input v-model="queryParams.materialCode" :placeholder="t('wkorder.orderBom.query.materialCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('wkorder.orderBom.query.operationCode')" prop="operationCode">
            <el-input v-model="queryParams.operationCode" :placeholder="t('wkorder.orderBom.query.operationCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('wkorder.orderBom.query.assemblyMaterialCode')" prop="assemblyMaterialCode">
            <el-input v-model="queryParams.assemblyMaterialCode" :placeholder="t('wkorder.orderBom.query.assemblyMaterialCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('wkorder.orderBom.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('wkorder.orderBom.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['wkorder:orderbom:add']">{{t('wkorder.orderBom.button.add')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['wkorder:orderbom:down']" :loading="downLoading">{{t('wkorder.orderBom.button.sync')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-upload class="upload-demo" :disabled="importDisabled" :on-change="handleImport" accept="xls,xlsx" :auto-upload="false" :multiple="true" :limit="2" :show-file-list ="false">
               <el-button type="warning" plain icon="Download" v-hasPermi="['wkorder:orderbom:import']" :loading="importLoading">{{t('wkorder.orderBom.button.import')}}</el-button>
            </el-upload>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column align="center" label="编号" prop="id" v-if="false" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.orderCode')" prop="orderCode" min-width="138" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.orderQty')" prop="orderQty" min-width="108" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.scheduleCode')" prop="scheduleCode" min-width="138" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.scheduleQty')" prop="scheduleQty" min-width="110" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.bomId')" prop="productBomId" min-width="174" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.bomType')" prop="productBomType" min-width="100" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.bomVersion')" prop="productBomVersion" min-width="110"/>
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.lineCode')" prop="lineCode" min-width="120" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.lineName')" prop="lineName" min-width="140"/>
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.materialCode')" prop="materialCode" min-width="120" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.materialName')" prop="materialName" min-width="360" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.materialVersion')" prop="materialVersion" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.operationCode')" prop="operationCode" min-width="120" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.assemblyMaterialId')" prop="assemblyMaterialId" min-width="174" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.assemblyMaterialCode')" prop="assemblyMaterialCode" min-width="120" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.assemblyMaterialName')" prop="assemblyMaterialName" min-width="360" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.assemblyMaterialVersion')" prop="assemblyMaterialVersion" min-width="128" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.assemblyUnitCode')" prop="assemblyUnitCode" min-width="120" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.assemblyConsumption')" prop="assemblyConsumption" min-width="80" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.standWorkHours')" prop="standWorkHours" min-width="130" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.retroactive')" prop="retroactive" min-width="120" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.addTime')" prop="addTime" width="155" />
         <el-table-column align="center" :label="t('wkorder.orderBom.tbCol.editTime')" prop="editTime" width="155" />

         <el-table-column :label="t('wkorder.orderBom.tbCol.orderNum')" prop="orderNum" v-if="false"> </el-table-column>
         <el-table-column :label="t('wkorder.orderBom.tbCol.status')" prop="status" v-if="false">
            <template #default="scope">
               <dict-tag :options="base_order_state"    :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('wkorder.wkOrder.tbCol.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.creatorId')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.creatorName')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.creationTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.lastModifierId')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.ifdelete')" prop="isDeleted" v-if="false" />

         <el-table-column :label="t('wkorder.orderBom.tbCol.operation')" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['wkorder:orderbom:edit']">{{t('wkorder.orderBom.button.edit')}}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['wkorder:orderbom:remove']">{{t('wkorder.orderBom.button.delete')}}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="180px">
            <el-row>
               <el-col :span="12">
                  <el-form-item :label="t('wkorder.orderBom.form.orderCode')" prop="orderCode">
                     <el-input v-model="form.orderCode" disabled :placeholder="t('wkorder.orderBom.form.orderCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.orderQty')" prop="orderQty">
                     <!-- <el-input-number v-model="form.orderQty" disabled :min="0" /> -->
                     <el-input v-model="form.orderQty" disabled :placeholder="t('wkorder.orderBom.form.orderQty')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.scheduleCode')" prop="scheduleCode">
                     <el-input v-model="form.scheduleCode" disabled :placeholder="t('wkorder.orderBom.form.scheduleCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.scheduleQty')" prop="scheduleQty" label-width="200">
                     <!-- <el-input-number v-model="form.scheduleQty" disabled :min="0" /> -->
                     <el-input v-model="form.scheduleQty" disabled :placeholder="t('wkorder.orderBom.form.scheduleQty')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.bomId')" prop="productBomId">
                     <el-input v-model="form.productBomId" :placeholder="t('wkorder.orderBom.form.bomId')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.bomType')" prop="productBomType">
                     <el-input v-model="form.productBomType" :placeholder="t('wkorder.orderBom.form.bomType')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.bomVersion')" prop="productBomVersion">
                     <el-input v-model="form.productBomVersion" :placeholder="t('wkorder.orderBom.form.bomVersion')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.lineCode')" prop="lineCode">
                     <el-input v-model="form.lineCode" :placeholder="t('wkorder.orderBom.form.lineCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.lineName')" prop="lineName">
                     <el-input v-model="form.lineName" :placeholder="t('wkorder.orderBom.form.lineName')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.materialCode')" prop="materialCode">
                     <el-input v-model="form.materialCode" :placeholder="t('wkorder.orderBom.form.materialCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.materialName')" prop="materialName">
                     <el-input v-model="form.materialName" :placeholder="t('wkorder.orderBom.form.materialName')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.materialVersion')" prop="materialVersion">
                     <el-input v-model="form.materialVersion" :placeholder="t('wkorder.orderBom.form.materialVersion')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('wkorder.orderBom.form.operationCode')" prop="operationCode">
                     <el-input v-model="form.operationCode" :placeholder="t('wkorder.orderBom.form.operationCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.assemblyMaterialId')" prop="assemblyMaterialId">
                     <el-input v-model="form.assemblyMaterialId" :placeholder="t('wkorder.orderBom.form.assemblyMaterialId')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.assemblyMaterialCode')" prop="assemblyMaterialCode">
                     <el-input v-model="form.assemblyMaterialCode" :placeholder="t('wkorder.orderBom.form.assemblyMaterialCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.assemblyMaterialName')" prop="assemblyMaterialName">
                     <el-input v-model="form.assemblyMaterialName" :placeholder="t('wkorder.orderBom.form.assemblyMaterialName')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.assemblyMaterialVersion')" prop="assemblyMaterialVersion">
                     <el-input v-model="form.assemblyMaterialVersion" :placeholder="t('wkorder.orderBom.form.assemblyMaterialVersion')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.assemblyUnitCode')" prop="assemblyUnitCode">
                     <el-input v-model="form.assemblyUnitCode" :placeholder="t('wkorder.orderBom.form.assemblyUnitCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.assemblyConsumption')" prop="assemblyConsumption">
                     <el-input v-model="form.assemblyConsumption" :placeholder="t('wkorder.orderBom.form.assemblyConsumption')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.standWorkHours')" prop="standWorkHours">
                     <el-input  v-model="form.standWorkHours" :placeholder="t('wkorder.orderBom.form.standWorkHours')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.retroactive')" prop="retroactive">
                     <el-input v-model="form.retroactive" :placeholder="t('wkorder.orderBom.form.retroactive')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.addTime')" prop="addTime" >
                     <el-input v-model="form.addTime" :placeholder="t('wkorder.orderBom.form.addTime')" disabled/>
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.editTime')" prop="editTime" >
                     <el-input v-model="form.editTime" :placeholder="t('wkorder.orderBom.form.editTime')" disabled/>
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('wkorder.orderBom.form.status')" prop="status">
                     <el-select v-model="form.status" disabled>
                        <el-option v-for="dict in base_is_open"  :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
                     </el-select>
                  </el-form-item>
                  <el-form-item :label="t('wkorder.orderBom.form.remark')" prop="remark">
                     <el-input v-model="form.remark"  type="textarea" rows="3" :placeholder="t('wkorder.orderBom.form.remarkTip')" />
                  </el-form-item>
               </el-col>
         </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('wkorder.orderBom.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('wkorder.orderBom.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="OrderBom">
/** 引入----------------------------------------------------------------------------*/
import { listDataAsync, listData, addData, delData, getData, updateData, getDown, importAsync } from "@/api/wkorder/orderbom";
import * as XLSX from 'xlsx';  
import moment from 'moment';
import { genFileId } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { ElConfigProvider } from 'element-plus';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");


/** 定义变量----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const importDisabled = ref(false);
const tableData = ref([]);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      orderCode: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg01'), trigger: "blur" }],
      orderQty: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg02'), trigger: "blur" }],
      scheduleCode: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg03'), trigger: "blur" }],
      scheduleQty: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg04'), trigger: "blur" }],
      productBomId: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg05'), trigger: "blur" }],
      productBomType: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg06'), trigger: "blur" }],
      productBomVersion: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg07'), trigger: "blur" }],
      lineCode: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg08'), trigger: "blur" }],
      materialCode: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg09'), trigger: "blur" }],
      materialVersion: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg10'), trigger: "blur" }],
      operationCode: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg11'), trigger: "blur" }],
      assemblyMaterialId: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg12'), trigger: "blur" }],
      assemblyMaterialCode: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg13'), trigger: "blur" }],
      assemblyMaterialName: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg14'), trigger: "blur" }],
      assemblyMaterialVersion: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg15'), trigger: "blur" }],
      assemblyUnitCode: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg16'), trigger: "blur" }],
      assemblyConsumption: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg17'), trigger: "blur" }],
      standWorkHours: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg18'), trigger: "blur" }],
      retroactive: [{ required: true, message: t('wkorder.orderBom.message.alarmMsg19'), trigger: "blur" }]
   }
});
const { form, queryParams, rules } = toRefs(data);

/** 列表操作 ----------------------------------------------------------------------------*/
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置搜索条件 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 编辑页面数据----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value =t('wkorder.orderBom.message.addTit');
}
/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('wkorder.orderBom.message.editTit');
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('wkorder.orderBom.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('wkorder.orderBom.message.delSuc'));
   }).catch(() => { });
}
/** 重置提交表单 */
function reset() {
   form.value = {
      id: undefined,
   };
   proxy.resetForm("submitRef");
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess(t('wkorder.orderBom.message.editSuc'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('wkorder.orderBom.message.addSuc'));
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 数据同步----------------------------------------------------------------------------*/
/** 同步按钮操作 */
function handleDown() {
   if(queryParams.value.orderCode == undefined)
   {
      proxy.$modal.msgWarning(t('wkorder.orderBom.message.syncTit01'));
      return;
   }
   if(queryParams.value.scheduleCode == undefined)
   {
      proxy.$modal.msgWarning(t('wkorder.orderBom.message.syncTit02'));
      return;
   }
   downLoading.value = true;
   getDown(queryParams.value.orderCode,queryParams.value.scheduleCode).then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('wkorder.orderBom.message.syncSuc')+":"+response.data);
      }, 1500);
   });
}

/** Excel导入----------------------------------------------------------------------------*/
/** 导入按钮操作 */
const handleImport = async (file,files) => {
   importLoading.value = true;
   importDisabled.value = true;
   if(files.length > 1){
      files.shift();
   };
   const data = await getXlsxData(file);
   tableData.value = translateField(data);

   // 数字转字符串
   tableData.value = JSON.parse(JSON.stringify(tableData.value, (key, value) => typeof value === 'number' ? String(value) : value));

   importAsync(tableData.value).then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         importLoading.value = false;
         importDisabled.value = false;
         proxy.$modal.msgSuccess(t('wkorder.orderBom.message.importSuc')+":"+response.data);
      }, 1500);
   });
}
//读取表格数据
const getXlsxData = async (file) => {
   const dataBinary = await readFile(file);
   const workBook = XLSX.read(dataBinary ,{
      type: "binary",
      cellDates: true
   });
   const workSheet = workBook.Sheets[workBook.SheetNames[0]];
   const data = XLSX.utils.sheet_to_json(workSheet);
   data.forEach((item) => {
      const keys = ["StartTime","EndTime"];
      for (const key in item) {
         if (moment(item[key], 'YYYY-MM-DD', true).isValid()) {
            item[key] = moment(item[key]).format("YYYY-MM-DD HH:mm:ss")
         }
      }
   });
   return data;
}
//读取excel文件
const readFile = (file) => {
   return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file.raw)
      reader.onload = (e) => {
         resolve(e.target.result)
      }
      reader.onerror = (e) => {
         reject(e)
      }
   })
}
//映射字段
const translateField = (data) => {
  const arr = []
  const cnToEn = {
      工单编号:'OrderCode',
      工单数量:'OrderQty',
      排程编号:'ScheduleCode',
      排程数量:'ScheduleQty',
      BOMID:'ProductBomId',
      BOM类型:'ProductBomType',
      BOM版本:'ProductBomVersion',
      产品编号:'MaterialCode',
      产品名称:'MaterialName',
      产品版本:'MaterialVersion',
      产线编号:'LineCode',
      产线名称:'LineName',
      工序编号:'OperationCode',
      零件Id:'AssemblyMaterialId',
      零件编号:'AssemblyMaterialCode',
      零件SN号:'AssemblyMaterialSn',
      零件名称:'AssemblyMaterialName',
      零件版本号:'AssemblyMaterialVersion',
      零件用量:'AssemblyConsumption',
      零件单位:'AssemblyUnitCode',
      标准工时:'StandWorkHours',
      追溯方式:'Retroactive',
      创建时间:'AddTime',
      更新时间:'EditTime',
   }
   data.forEach((item) => {
      const arrItem = {}
      Object.keys(item).forEach((key) => {
         arrItem[cnToEn[key]] = item[key]
      })
      arr.push(arrItem) 
   })
   return arr
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>