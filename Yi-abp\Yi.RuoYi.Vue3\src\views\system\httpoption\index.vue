<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="接口实例" prop="serviceId">
            <el-input v-model="queryParams.serviceId" placeholder="接口实例" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:httpoption:add']">新增</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column label="标识" prop="name" width="255" />
         <el-table-column label="AppId" align="center" prop="appId" width="320" />
         <el-table-column label="AppKey" align="center" prop="appKey" width="320" />
         <el-table-column label="工厂编号" align="center" prop="factoryCode" width="100"  />
         <el-table-column label="接口实例" prop="serviceId" width="285"  />
         <el-table-column label="Url地址" prop="httpUrl"  width="300" />
         <el-table-column label="接口路由" prop="httpRoute"  width="600" />
         <el-table-column label="请求时间" align="center" prop="lastUpdateTime" width="255" />
         <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:httpoption:edit']">编辑</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:httpoption:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="700px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="标识" prop="name">
               <el-input v-model="form.name" placeholder="标识"/>
            </el-form-item>
            <el-form-item label="AppId" prop="appId">
               <el-input v-model="form.appId" placeholder="AppId" />
            </el-form-item>
            <el-form-item label="AppKey" prop="appKey">
               <el-input v-model="form.appKey" placeholder="AppKey" />
            </el-form-item>
            <el-form-item label="工厂编号" prop="factoryCode">
               <el-input v-model="form.factoryCode" placeholder="工厂编号" />
            </el-form-item>
            <el-form-item label="接口实例" prop="serviceId">
               <el-input v-model="form.serviceId" placeholder="接口实例" />
            </el-form-item>
            <el-form-item label="Url地址" prop="httpUrl">
               <el-input v-model="form.httpUrl" placeholder="Url地址" />
            </el-form-item>
            <el-form-item label="接口路由" prop="httpRoute">
               <el-input v-model="form.httpRoute" placeholder="接口路由" />
            </el-form-item>
            <el-form-item label="请求时间" prop="lastUpdateTime">
               <el-input v-model="form.lastUpdateTime" placeholder="请求时间" />
            </el-form-item>
  
            <el-form-item label="备注" prop="remark">
               <el-input v-model="form.Remark" type="textarea" rows="3" placeholder="请输入内容" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="HttpOption">
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/system/httpoption";
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      name: [{ required: true, message: "请填写标识", trigger: "blur" }],
      appId: [{ required: true, message: "请填写AppId", trigger: "blur" }],
      appKey: [{ required: true, message: "请填写AppKey", trigger: "blur" }],
      factoryCode: [{ required: true, message: "请填写工厂编号", trigger: "blur" }],
      serviceId: [{ required: true, message: "请填写接口实例", trigger: "blur" }],
      httpUrl: [{ required: true, message: "请填写Url地址", trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1,
      remark: undefined
   };
   proxy.resetForm("submitRef");
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm('是否删除编号为"' + postIds + '"的数据项？').then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess("编辑成功");
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

getList();
</script>