<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('system.httpOption.query.serviceId')" prop="serviceId">
            <el-input v-model="queryParams.serviceId" :placeholder="t('system.httpOption.query.serviceId')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('system.httpOption.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('system.httpOption.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:httpoption:add']">{{t('system.httpOption.button.add')}}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column :label="t('system.httpOption.tbCol.name')" prop="name" width="255" />
         <el-table-column label="AppId" align="center" prop="appId" width="320" />
         <el-table-column label="AppKey" align="center" prop="appKey" width="320" />
         <el-table-column :label="t('system.httpOption.tbCol.facCode')" align="center" prop="factoryCode" width="120"  />
         <el-table-column :label="t('system.httpOption.tbCol.serviceId')" prop="serviceId" width="285"  />
         <el-table-column :label="t('system.httpOption.tbCol.httpUrl')" prop="httpUrl"  width="300" />
         <el-table-column :label="t('system.httpOption.tbCol.httpRoute')" prop="httpRoute"  width="600" />
         <el-table-column :label="t('system.httpOption.tbCol.updateTime')" align="center" prop="lastUpdateTime" width="255" />
         <el-table-column :label="t('system.httpOption.tbCol.operation')" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:httpoption:edit']">{{t('system.httpOption.button.edit')}}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:httpoption:remove']">{{t('system.httpOption.button.delete')}}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="120px">
            <el-form-item :label="t('system.httpOption.form.name')" prop="name">
               <el-input v-model="form.name" :placeholder="t('system.httpOption.form.name')"/>
            </el-form-item>
            <el-form-item label="AppId" prop="appId">
               <el-input v-model="form.appId" placeholder="AppId" />
            </el-form-item>
            <el-form-item label="AppKey" prop="appKey">
               <el-input v-model="form.appKey" placeholder="AppKey" />
            </el-form-item>
            <el-form-item :label="t('system.httpOption.form.facCode')" prop="factoryCode">
               <el-input v-model="form.factoryCode" :placeholder="t('system.httpOption.form.facCode')" />
            </el-form-item>
            <el-form-item :label="t('system.httpOption.form.serviceId')" prop="serviceId">
               <el-input v-model="form.serviceId" :placeholder="t('system.httpOption.form.serviceId')" />
            </el-form-item>
            <el-form-item :label="t('system.httpOption.form.httpUrl')" prop="httpUrl">
               <el-input v-model="form.httpUrl" :placeholder="t('system.httpOption.form.httpUrl')" />
            </el-form-item>
            <el-form-item :label="t('system.httpOption.form.httpRoute')" prop="httpRoute">
               <el-input v-model="form.httpRoute" :placeholder="t('system.httpOption.form.httpRoute')" />
            </el-form-item>
            <el-form-item :label="t('system.httpOption.form.updateTime')" prop="lastUpdateTime">
               <el-input v-model="form.lastUpdateTime" :placeholder="t('system.httpOption.form.updateTime')" />
            </el-form-item>
  
            <el-form-item :label="t('system.httpOption.form.remark')" prop="remark">
               <el-input v-model="form.Remark" type="textarea" rows="3" :placeholder="t('system.httpOption.form.remarkTip')" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('system.httpOption.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('system.httpOption.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="HttpOption">
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/system/httpoption";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      name: [{ required: true, message: t('system.httpOption.message.alarm01'), trigger: "blur" }],
      appId: [{ required: true, message: t('system.httpOption.message.alarm02'), trigger: "blur" }],
      appKey: [{ required: true, message: t('system.httpOption.message.alarm03'), trigger: "blur" }],
      factoryCode: [{ required: true, message: t('system.httpOption.message.alarm04'), trigger: "blur" }],
      serviceId: [{ required: true, message: t('system.httpOption.message.alarm05'), trigger: "blur" }],
      httpUrl: [{ required: true, message: t('system.httpOption.message.alarm06'), trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1,
      remark: undefined
   };
   proxy.resetForm("submitRef");
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('system.httpOption.message.addTit');
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('system.httpOption.message.editTit');
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('system.httpOption.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('system.httpOption.message.delSuc'));
   }).catch(() => { });
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('system.httpOption.message.editSuc'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('system.httpOption.message.addSuc'));
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

getList();
</script>