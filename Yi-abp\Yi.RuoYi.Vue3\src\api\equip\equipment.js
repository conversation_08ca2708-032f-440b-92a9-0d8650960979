import request from '@/utils/request'

export function listDataAsync(query) {
    return request({
        url: '/equip/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/equip',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/equip/' + id,
        method: 'get'
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/equip',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(id, data) {
    return request({
        url: `/equip/` + id,
        method: 'put',
        data: data
    })
}

// 删除
export function delData(id) {
    return request({
        url: `/equip/${id}`,
        method: 'delete',
    })
}

// 同步数据
export function getDown() {
  return request({
    url: '/equip/getDown',
    method: 'get'
  })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/equip/' + roleId,
        method: 'get'
    })
}