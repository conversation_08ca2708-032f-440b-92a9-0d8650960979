<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="140px">
         <el-form-item :label="t('equip.equipList.queryField.lineCode')" prop="lineCode">
            <el-input v-model="queryParams.lineCode" :placeholder="t('equip.equipList.queryField.lineCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('equip.equipList.queryField.stationCode')" prop="stationCode">
            <el-input v-model="queryParams.stationCode" :placeholder="t('equip.equipList.queryField.stationCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('equip.equipList.queryField.machineCode')" prop="machineCode">
            <el-input v-model="queryParams.machineCode" :placeholder="t('equip.equipList.queryField.machineCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ t('equip.equipList.button.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ t('equip.equipList.button.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['process:bom:add']">{{ t('equip.equipList.button.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['process:bom:down']" :loading="downLoading" v-if="false">{{ t('equip.equipList.button.synchronous') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['process:bom:import']" :loading="importLoading" v-if="false" >{{ t('equip.equipList.button.import') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column align="center" label="" prop="id" v-if="false" />
         <el-table-column align="center" :label="t('equip.equipList.tbColumn.lineCode')" prop="lineCode"  />
         <el-table-column align="center" :label="t('equip.equipList.tbColumn.stationCode')" prop="stationCode" />
         <el-table-column align="center" :label="t('equip.equipList.tbColumn.resourceCode')" prop="resourceCode" />
         <el-table-column align="center" :label="t('equip.equipList.tbColumn.machineCode')" prop="machineCode" />
         <el-table-column align="center" :label="t('equip.equipList.tbColumn.machineName')" prop="machineName" />
         <el-table-column align="center" :label="t('equip.equipList.tbColumn.machineStatus')" prop="machineStatus" />

         <el-table-column :label="t('equip.equipList.tbColumn.orderNum')" prop="orderNum" v-if="false"> </el-table-column>
         <el-table-column :label="t('equip.equipList.tbColumn.status')" prop="status" v-if="false">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('equip.equipList.tbColumn.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('equip.equipList.tbColumn.creatorId')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('equip.equipList.tbColumn.creatorName')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('equip.equipList.tbColumn.creationTime')" prop="creationTime" v-if="false" width="155" />
         <el-table-column :label="t('equip.equipList.tbColumn.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('equip.equipList.tbColumn.lastModifierName')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('equip.equipList.tbColumn.lastModificationTime')" prop="lastModificationTime" v-if="false" width="155" />
         <el-table-column :label="t('equip.equipList.tbColumn.isDeleted')" prop="isDeleted" v-if="false" />

         <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['process:bom:edit']">{{ t('equip.equipList.button.edit') }}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['process:bom:remove']">{{ t('equip.equipList.button.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="140px">
            <el-form-item :label="t('equip.equipList.elForm.lineCode')" prop="lineCode">
               <el-input v-model="form.lineCode" :placeholder="t('equip.equipList.elForm.lineCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipList.elForm.stationCode')" prop="stationCode">
               <el-input v-model="form.stationCode" :placeholder="t('equip.equipList.elForm.stationCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipList.elForm.resourceCode')" prop="resourceCode">
               <el-input v-model="form.machineCode" :placeholder="t('equip.equipList.elForm.resourceCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipList.elForm.machineCode')" prop="machineCode">
               <el-input v-model="form.machineCode" :placeholder="t('equip.equipList.elForm.machineCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipList.elForm.machineName')" prop="machineName">
               <el-input v-model="form.machineName" :placeholder="t('equip.equipList.elForm.machineNameTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipList.elForm.machineStatus')" prop="machineStatus">
               <el-input v-model="form.machineStatus" :placeholder="t('equip.equipList.elForm.machineStatusTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipList.elForm.status')" prop="status">
               <el-select v-model="form.status">
                  <el-option v-for="dict in base_is_open"  :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
               </el-select>
            </el-form-item>
            <el-form-item :label="t('equip.equipList.elForm.remark')" prop="remark">
               <el-input v-model="form.remark"  type="textarea" rows="3" :placeholder="t('equip.equipList.elForm.remarkTip')" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ t('equip.equipList.button.confirm') }}</el-button>
               <el-button @click="cancel">{{ t('equip.equipList.button.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Equipment">
/** 引入----------------------------------------------------------------------------*/
import { listDataAsync, listData, addData, delData, getData, updateData, getDown } from "@/api/equip/equipment";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");


/** 结构定义----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      lineCode: [{ required: true, message: t('equip.equipList.message.alarmMsg01'), trigger: "blur" }],
      stationCode: [{ required: true, message: t('equip.equipList.message.alarmMsg02'), trigger: "blur" }],
      resourceCode: [{ required: true, message: t('equip.equipList.message.alarmMsg03'), trigger: "blur" }],
      machineCode: [{ required: true, message: t('equip.equipList.message.alarmMsg04'), trigger: "blur" }],
   }
});
const { form, queryParams, rules } = toRefs(data);

/** 列表----------------------------------------------------------------------------*/
/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置搜索条件 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 列表操作----------------------------------------------------------------------------*/
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 编辑页面操作----------------------------------------------------------------------------*/
/** 重置提交表单 */
function reset() {
   form.value = {
      id: undefined,
   };
   proxy.resetForm("submitRef");
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess(t('equip.equipList.message.editSuccess'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('equip.equipList.message.addSuccess'));
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 增删改按钮----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('equip.equipList.message.addTitle');
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('equip.equipList.message.editTitle');
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('equip.equipList.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('equip.equipList.message.delSuccess'));
   }).catch(() => { });
}

/** 数据同步----------------------------------------------------------------------------*/
/** 同步按钮操作 */
// function handleDown() {
//    downLoading.value = true;
//    getDown().then(response => {
//       setTimeout(function() {  // 延迟1.5秒执行
//          downLoading.value =false;
//          proxy.$modal.msgSuccess("同步成功,变更行数:"+response.data);
//       }, 1500);
//    });
// }

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess(t('equip.equipList.message.importSuccess'));
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>