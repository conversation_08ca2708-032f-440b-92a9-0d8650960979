<template>
   <el-form ref="userRef" :model="user" :rules="rules" label-width="80px">
      <el-form-item :label="$t('common.userProfile.userNameEdit')" prop="nickName">
         <el-input v-model="user.nick" maxlength="50" />
      </el-form-item>
      <el-form-item :label="$t('common.userProfile.cellPhoneEdit')" prop="phone">
         <el-input v-model="user.phone" maxlength="18" />
      </el-form-item>
      <el-form-item :label="$t('common.userProfile.emailEdit')" prop="email">
         <el-input v-model="user.email" maxlength="50" />
      </el-form-item> 
      <el-form-item>
      <el-button type="primary" @click="submit">{{ $t('common.userProfile.saveBtn') }}</el-button>
      <el-button type="danger" @click="close">{{ $t('common.userProfile.colseBtn') }}</el-button>
      </el-form-item>
   </el-form>
</template>

<script setup>
import { updateUserProfile } from "@/api/system/user";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const props = defineProps({
  user: {
    type: Object
  }
});

const { proxy } = getCurrentInstance();

const rules = ref({
  nick: [{ required: true, message: t('common.userProfile.userNameVerify'), trigger: "blur" }],
  email: [{ required: true, message: t('common.userProfile.emailVerify'), trigger: "blur" }, { type: "email", message: t('common.userProfile.emailRegex'), trigger: ["blur", "change"] }],
  phone: [{ required: true, message: t('common.userProfile.cellPhoneVerify'), trigger: "blur" }],
});

/** 提交按钮 */
function submit() {
  proxy.$refs.userRef.validate(valid => {
    if (valid) {
      updateUserProfile(props.user).then(response => {
        proxy.$modal.msgSuccess(t('common.userProfile.editMsg'));
      });
    }
  });
};
/** 关闭按钮 */
function close() {
  proxy.$tab.closePage();
};
</script>
