<template>
  <div>
    <el-dropdown trigger="click" @command="handleSetLanguage">
      <div class="size-icon--style">
        <svg-icon class-name="size-icon" icon-class="language" />
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item v-for="item of languageOptions" :key="item.value" :command="item.value">
            {{ item.label }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<script setup>
import useDictStore from '@/store/modules/dict';
import Cookies from 'js-cookie';
import i18n from '@/locales/index';
import { useI18n } from 'vue-i18n'

// const { t } = i18n.global;
const { locale } = useI18n();

const languageOptions = ref([
  { label: "中文", value: "zh" },
  { label: "English", value: "en" },
  { label: "Português", value: "pt" }
]);

const handleSetLanguage = (lang) => {
  locale.value = lang;
  Cookies.set('language', lang)
  localStorage.setItem('language', lang);
  sessionStorage.setItem('language',lang);
  i18n.global.locale.value = lang;
  useDictStore().cleanDict();
  location.reload();  
}
</script>

<style lang='scss' scoped>
.size-icon--style {
  font-size: 20px;
  line-height: 50px;
  padding-right: 7px;
}
</style>