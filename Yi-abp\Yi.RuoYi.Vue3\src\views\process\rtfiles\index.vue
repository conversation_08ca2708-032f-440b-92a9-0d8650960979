<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('process.rtfile.query.materialCode')" prop="materialCode">
            <el-input v-model="queryParams.materialCode" :placeholder="t('process.rtfile.query.materialCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('process.rtfile.query.routeCode')" prop="routeCode">
            <el-input v-model="queryParams.routeCode" :placeholder="t('process.rtfile.query.routeCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('process.rtfile.query.operationCode')" prop="operationCode">
            <el-input v-model="queryParams.operationCode" :placeholder="t('process.rtfile.query.operationCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('process.rtfile.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('process.rtfile.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['process:rtfiles:add']">{{t('process.rtfile.button.add')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['process:rtfiles:down']" :loading="downLoading" v-if="false">{{t('process.rtfile.button.synchronous')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-upload :multiple="false" :on-change="handleImport" :disabled="importDisabled" :limit="2" :auto-upload="false" :show-file-list ="false">
               <el-button type="warning" :disabled="importDisabled" v-hasPermi="['process:rtfiles:import']">{{t('process.rtfile.button.importSOP')}}</el-button>
            </el-upload>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" @cell-click="handleClick" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column label="编号" align="center" prop="id" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.materialCode')" align="center" prop="materialCode" width="120" />
         <el-table-column :label="t('process.rtfile.tbCol.materialName')" align="center" prop="materialName" />
         <el-table-column :label="t('process.rtfile.tbCol.materialVersion')" align="center" prop="materialVersion" width="120" />
         <el-table-column :label="t('process.rtfile.tbCol.routeCode')" align="center" prop="routeCode" width="120" />
         <el-table-column :label="t('process.rtfile.tbCol.routeName')" align="center" prop="routeName" />
         <el-table-column :label="t('process.rtfile.tbCol.routeVersion')" align="center" prop="routeVersion" />
         <el-table-column :label="t('process.rtfile.tbCol.operationCode')" align="center" prop="operationCode" width="120" />
         <el-table-column :label="t('process.rtfile.tbCol.fileType')" align="center" prop="fileType" />
         <el-table-column :label="t('process.rtfile.tbCol.fileName')" align="left" prop="fileName" width="220" />
         <el-table-column :label="t('process.rtfile.tbCol.fileLoad')" align="left" prop="fileLoad" :show-overflow-tooltip="true" width="255" />   

         <el-table-column prop="orderNum" :label="t('process.rtfile.tbCol.order')" v-if="false"></el-table-column>
         <el-table-column prop="status" :label="t('process.rtfile.tbCol.status')">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('process.rtfile.tbCol.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creator')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creator')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creatTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModifierId')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.ifdelete')" prop="isDeleted" v-if="false" />


         <el-table-column :label="t('process.rtfile.tbCol.operation')" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['process:rtfiles:edit']">{{t('process.rtfile.button.edit')}}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['process:rtfiles:remove']">{{t('process.rtfile.button.delete')}}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="140px">
            <el-row>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtfile.form.materialCode')" prop="materialCode">
                     <el-input v-model="form.materialCode" :placeholder="t('process.rtfile.form.materialCodeTip')" />
                  </el-form-item> 
                  <el-form-item :label="t('process.rtfile.form.materialName')" prop="materialName">
                     <el-input v-model="form.materialName" :placeholder="t('process.rtfile.form.materialNameTip')" />
                  </el-form-item>
                  <el-form-item :label="t('process.rtfile.form.materialVersion')" prop="materialVersion">
                     <el-input v-model="form.materialVersion" :placeholder="t('process.rtfile.form.materialVersionTip')" />
                  </el-form-item>
                  <el-form-item :label="t('process.rtfile.form.operationCode')" prop="operationCode">
                     <el-input v-model="form.operationCode" :placeholder="t('process.rtfile.form.operationCodeTip')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtfile.form.materialVersion')" prop="routeCode">
                     <el-input v-model="form.routeCode" :placeholder="t('process.rtfile.form.materialVersionTip')" />
                  </el-form-item>
                  <el-form-item :label="t('process.rtfile.form.routeName')" prop="routeName">
                     <el-input v-model="form.routeName" :placeholder="t('process.rtfile.form.routeNameTip')" />
                  </el-form-item>
                  <el-form-item :label="t('process.rtfile.form.routeVersion')" prop="routeVersion">
                     <el-input v-model="form.routeVersion" :placeholder="t('process.rtfile.form.routeVersionTip')" />
                  </el-form-item>
                  <el-form-item :label="t('process.rtfile.form.status')">
                     <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in base_is_open" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('process.rtfile.form.fileName')" prop="fileName">
                     <el-input v-model="form.fileName" :placeholder="t('process.rtfile.form.fileNameTip')" />
                  </el-form-item>
                  <el-form-item :label="t('process.rtfile.form.fileType')" prop="fileType">
                     <el-input v-model="form.fileType" :placeholder="t('process.rtfile.form.fileTypeTip')" />
                  </el-form-item>
                  <el-form-item :label="t('process.rtfile.form.fileLoad')" prop="fileLoad">
                     <el-input v-model="form.fileLoad" type="textarea" rows="3" :placeholder="t('process.rtfile.form.fileLoadTip')" />
                  </el-form-item>
                  <el-form-item :label="t('process.rtfile.form.remark')" prop="remark">
                     <el-input v-model="form.Remark" type="textarea" rows="3" :placeholder="t('process.rtfile.form.remarkTip')" />
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('process.rtfile.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('process.rtfile.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="RouteFiles">
/** 引入----------------------------------------------------------------------------*/
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/process/rtfiles";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

/** 定义变量----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importDisabled = ref(true);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      materialCode: [{ required: true, message: t('process.rtfile.message.alarmMsg01'), trigger: "blur" }],
      materialVersion: [{ required: true, message: t('process.rtfile.message.alarmMsg02'), trigger: "blur" }],
      routeCode: [{ required: true, message: t('process.rtfile.message.alarmMsg03'), trigger: "blur" }],
      routeVersion: [{ required: true, message: t('process.rtfile.message.alarmMsg04'), trigger: "blur" }],
      operationCode: [{ required: true, message: t('process.rtfile.message.alarmMsg05'), trigger: "blur" }],
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 列表操作 ----------------------------------------------------------------------------*/
/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length; 
   if (selection.length == 1) {
     importDisabled.value = false;
   }else{
     importDisabled.value = true;
   }
}
/** 列表文件浏览 */
function handleClick(row, column, cell, event) {
   if(column.property == "fileLoad"){
      window.open(row.fileLoad);
   }
}

/** 页面编辑操作----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('process.rtfile.message.addTitle');
}
/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('process.rtfile.message.editTitle');
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('process.rtfile.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('process.rtfile.message.delSuccess'));
   }).catch(() => { });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('process.rtfile.message.editSuccess'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('process.rtfile.message.addSuccess'));
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 提交表单重置 */
function reset() {
   form.value = {
      id: undefined,
   };
   proxy.resetForm("submitRef");
}

/** 数据同步----------------------------------------------------------------------------*/
/** 同步按钮操作 */
function handleDown() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('process.rtfile.message.syncSuccess') +response.data);
      }, 1500);
   });
}

/** 文件导入----------------------------------------------------------------------------*/
/** 导入SOP作业指导书 */
function handleImport(file) {
   importDisabled.value = true;  

   const allowedTypes = ['application/pdf'];
   const isLtSize = file.size / 1024 / 1024 < 50;

   if (!allowedTypes.includes(file.raw.type)) {
      proxy.$modal.msgError(t('process.rtfile.message.alarmMsg06'));
      importDisabled.value = false;
      return false;
   }

   if (!isLtSize) {
      proxy.$modal.msgError(t('process.rtfile.message.alarmMsg07'));
      importDisabled.value = false;
      return false;
   }

   const formFile = new FormData();
   formFile.append("file", file.raw);

   axios.post('http://localhost:19001/api/app/route-files/importAsync', formFile, {
      headers: {
         'Content-Type':'multipart/form-data'
      }
      }).then(res => {  
         proxy.$modal.msgSuccess(t('process.rtfile.message.importSuccess'));
         const postId = ids.value[0]
         getData(postId).then(response => {
            response.data.fileName = res.data.fileName;
            response.data.fileType = res.data.fileType;
            response.data.fileLoad = res.data.fileLoad;
            updateData(response.data).then(response => {
               getList();
               proxy.$modal.msgSuccess(t('process.rtfile.message.alarmMsg08'));
            });
         });
      }).catch(error => {
         alert(t('process.rtfile.message.alarmMsg09'));
      });
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>