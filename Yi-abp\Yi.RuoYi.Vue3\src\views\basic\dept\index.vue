<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item :label="t('basedata.deptManage.queryField.deptName')" prop="deptName">
            <el-input v-model="queryParams.deptName" :placeholder="t('basedata.deptManage.queryField.nameTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.deptManage.queryField.depCode')" prop="deptCode">
            <el-input v-model="queryParams.deptCode" :placeholder="t('basedata.deptManage.queryField.codeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.deptManage.queryField.state')" prop="state">
            <el-select v-model="queryParams.state" :placeholder="t('basedata.deptManage.queryField.deptState')" clearable style="width: 240px">
               <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="JSON.parse(dict.value)" />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('basedata.deptManage.button.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ $t('basedata.deptManage.button.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="toggleExpandAll">{{ $t('basedata.deptManage.button.show') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:dept:add']">{{ $t('basedata.deptManage.button.add') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-if="refreshTable" v-loading="loading" :data="deptList" row-key="id" :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
         <el-table-column prop="deptName" :label="t('basedata.deptManage.tbColumn.deptName')" width="260"></el-table-column>
         <el-table-column prop="deptCode" :label="t('basedata.deptManage.tbColumn.deptCode')" width="200"></el-table-column>
         <el-table-column prop="orderNum" :label="t('basedata.deptManage.tbColumn.deptNumber')" width="200"></el-table-column>
         <el-table-column prop="state" :label="t('basedata.deptManage.tbColumn.state')" width="100">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.state" />
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.deptManage.tbColumn.creatTime')" align="center" prop="creationTime" width="200">
            <template #default="scope">
               <span>{{ parseTime(scope.row.creationTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.deptManage.tbColumn.operate')" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['basic:dept:add']">{{ $t('basedata.deptManage.button.add') }}</el-button>
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:dept:edit']">{{ $t('basedata.deptManage.button.edit') }}</el-button>
               <el-button v-if="scope.row.parentId != 0" link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:dept:remove']">{{ $t('basedata.deptManage.button.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <!-- 添加或编辑部门对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="deptRef" :model="form" :rules="rules" label-width="200px">
            <el-row>
               <el-col :span="24" v-if="form.parentId !== 0">
                  <el-form-item :label="t('basedata.deptManage.elForm.superiorDept')" prop="parentId">
                     <el-tree-select v-model="form.parentId" :data="deptOptions" :props="{ value: 'id', label: 'deptName', children: 'children' }" value-key="id" :placeholder="t('basedata.deptManage.elForm.superiorSelect')" check-strictly />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('basedata.deptManage.elForm.deptName')" prop="deptName">
                     <el-input v-model="form.deptName" :placeholder="t('basedata.deptManage.elForm.nameTip')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('basedata.deptManage.elForm.deptNumber')" prop="deptCode">
                     <el-input v-model="form.deptCode" :placeholder="t('basedata.deptManage.elForm.numberTip')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('basedata.deptManage.elForm.order')" prop="orderNum">
                     <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('basedata.deptManage.elForm.leader')" prop="leader">
                     <el-input v-model="form.leader" :placeholder="t('basedata.deptManage.elForm.leaderTip')" maxlength="20" />
                  </el-form-item>
               </el-col>
               <!-- <el-col :span="12">
                  <el-form-item label="联系电话" prop="phone">
                     <el-input v-model="form.phone" placeholder="请输入联系电话" maxlength="11" />
                  </el-form-item>
               </el-col> -->
               <!-- <el-col :span="12">
                  <el-form-item label="邮箱" prop="email">
                     <el-input v-model="form.email" placeholder="请输入邮箱" maxlength="50" />
                  </el-form-item>
               </el-col> -->
               <el-col :span="12">
                  <el-form-item :label="t('basedata.deptManage.elForm.deptState')">
                     <el-radio-group v-model="form.state">
                        <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ $t('basedata.deptManage.button.confirm') }}</el-button>
               <el-button @click="cancel">{{ $t('basedata.deptManage.button.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Dept">
import { listDept, getDept, delDept, addDept, updateDept } from "@/api/basic/dept";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const deptList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const deptOptions = ref([]);
const isExpandAll = ref(true);
const refreshTable = ref(true);

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 0,
      maxResultCount: 9999,
      Sorting: undefined
   },
   rules: {
      //  parentId: [{ required: true, message: "上级部门不能为空", trigger: "blur" }],
      deptCode: [{ required: true, message: t('basedata.deptManage.message.alarm01'), trigger: "blur" }],
      deptName: [{ required: true, message: t('basedata.deptManage.message.alarm02'), trigger: "blur" }],
      orderNum: [{ required: true, message: t('basedata.deptManage.message.alarm03'), trigger: "blur" }],
      //  email: [{ type: "email", message: "请输入正确的邮箱地址", trigger: ["blur", "change"] }],
      //  phone: [{ pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/, message: "请输入正确的手机号码", trigger: "blur" }]
   },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询部门列表 */
function getList() {
   loading.value = true;
   listDept(queryParams.value).then(response => {
      deptList.value = proxy.handleTree(response.data.items, "id");
      loading.value = false;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1
   };
   proxy.resetForm("deptRef");
}
/** 搜索按钮操作 */
function handleQuery() {
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
   reset();
   listDept().then(response => {
      deptOptions.value = proxy.handleTree(response.data.items, "id");
   });
   if (row != undefined) {
      form.value.parentId = row.id;
   }
   open.value = true;
   title.value = t('basedata.deptManage.message.add_title');
}
/** 展开/折叠操作 */
function toggleExpandAll() {
   refreshTable.value = false;
   isExpandAll.value = !isExpandAll.value;
   nextTick(() => {
      refreshTable.value = true;
   });
}
/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();

   listDept().then(response => {
      deptOptions.value = proxy.handleTree(response.data.items, "id");
      deptOptions.value == removeNodesByCondition(deptOptions.value, row.id)
   });

   getDept(row.id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('basedata.deptManage.message.edit_title');
   });
}

// 递归移除 条件节点及其子节点
function removeNodesByCondition(tree, id) {
   for (let i = tree.length - 1; i >= 0; i--) {
      const node = tree[i]
      if (node.id == id) {
         tree.splice(i, 1) // 移除当前节点
         continue;
      }
      if (node.children) {
         removeNodesByCondition(node.children, id)
      }
  }
  return tree
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["deptRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            if (form.value.id == form.value.parentId) {
               proxy.$modal.msgWarning(t('basedata.deptManage.message.edit_msg01')); return;
            }
            updateDept(form.value).then(response => {
               proxy.$modal.msgSuccess(t('basedata.deptManage.message.editSuccess'));
               open.value = false;
               getList();
            });
         } else {
            addDept(form.value).then(response => {
               proxy.$modal.msgSuccess(t('basedata.deptManage.message.addSucess'));
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   proxy.$modal.confirm(t('basedata.deptManage.message.ifDelete') + row.deptName + t('basedata.deptManage.message.ifData')).then(function () {
      return delDept(row.id);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('basedata.deptManage.message.deleteSucess'));
   }).catch(() => { });
}

getList();
</script>
