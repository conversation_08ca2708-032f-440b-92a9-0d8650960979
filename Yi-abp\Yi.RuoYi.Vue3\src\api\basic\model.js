import request from '@/utils/request'

// 获取车间
export function getWkshopAsync(query) {
  return request({
    url: '/model/getWkshopAsync',
    method: 'get',
    params: query
  })
}

// 获取产线
export function getLineAsync(query) {
  return request({
    url: '/model/getLineAsync',
    method: 'get',
    params: query
  })
}

// 自定义分页
export function listDataAsync(query) {
  return request({
    url: '/model/getListAsync',
    method: 'get',
    params: query
  })
}

// 查询列表
export function listData(query) {
  return request({
    url: '/model',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getData(id) {
  return request({
    url: '/model/' + id,
    method: 'get'
  })
}

// 查询详细
export function getInfoAsync(modelCode) {
  return request({
    url: '/model/getInfoAsync?modelCode=' + modelCode,
    method: 'get',
  })
}

// 删除
export function delData(id) {
  return request({
    url: `/model/${id}`,
    method: 'delete',
  })
}

// 新增
export function addData(data) {
  return request({
    url: '/model',
    method: 'post',
    data: data
  })
}

// 编辑
export function updateData(data) {
  return request({
    url: `/model/${data.id}`,
    method: 'put',
    data: data
  })
}

// 同步数据
export function getDown() {
  return request({
    url: '/model/getDown',
    method: 'get'
  })
}

// 查询菜单下拉树结构
export function roleModelTreeselect(roleId) {
  return request({
    url: '/model/role-id/' + roleId,
    method: 'get'
  })
}