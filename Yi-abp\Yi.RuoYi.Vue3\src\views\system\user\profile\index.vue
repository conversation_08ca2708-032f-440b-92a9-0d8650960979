<template>
   <div class="app-container">
      <el-row :gutter="20">
         <el-col :span="6" :xs="24">
            <el-card class="box-card">
               <template v-slot:header>
                  <div class="clearfix">
                     <span> {{ $t("common.userProfile.personalInfo")}}</span>
                  </div>
               </template>
               <div>
                  <div class="text-center">
                     <userAvatar :user="state.user" />
                  </div>
                  <ul class="list-group list-group-striped">
                     <li class="list-group-item">
                        <svg-icon icon-class="user" /> {{ $t("common.userProfile.userName")}} <div class="pull-right">{{ state.user.userName }}</div>
                     </li>
                     <li class="list-group-item">
                        <svg-icon icon-class="phone" /> {{ $t("common.userProfile.cellPhone")}} <div class="pull-right">{{ state.user.phone }}</div>
                     </li>
                     <li class="list-group-item">
                        <svg-icon icon-class="email" /> {{ $t("common.userProfile.email")}} <div class="pull-right">{{ state.user.email }}</div>
                     </li>
                     <li class="list-group-item">
                        <svg-icon icon-class="tree" /> {{ $t("common.userProfile.deptName")}} <div class="pull-right" v-if="state.dept">{{ state.dept.deptName }}</div>
                     </li>
                     <li class="list-group-item">
                        <svg-icon icon-class="date" /> {{ $t("common.userProfile.createTime")}} <div class="pull-right">{{ state.user.creationTime }}</div>
                     </li>
                  </ul>
               </div>
            </el-card>
         </el-col>
         <el-col :span="18" :xs="24">
            <el-card>
               <template v-slot:header>
                  <div class="clearfix">
                     <span> {{ $t("common.userProfile.baseInfo")}} </span>
                  </div>
               </template>
               <el-tabs v-model="activeTab">
                  <el-tab-pane :label="$t('common.userProfile.baseInfo')" name="userinfo">
                     <userInfo :user="state.user" />
                  </el-tab-pane>
                  <el-tab-pane :label="$t('common.userProfile.pwdEdit')" name="resetPwd">
                     <resetPwd />
                  </el-tab-pane>
               </el-tabs>
            </el-card>
         </el-col>
      </el-row>
   </div>
</template>

<script setup name="Profile">
import userAvatar from "./userAvatar";
import userInfo from "./userInfo";
import resetPwd from "./resetPwd";
import { getUserProfile } from "@/api/system/user";

const activeTab = ref("userinfo");
const state = reactive({
   user: {},
   dept: {},
   roles: [],
   roleGroup: {},
   postGroup: {}
});

function getUser() {
   getUserProfile().then(response => {
      state.user = response.data.user;
      state.dept = response.data.dept;
      state.roles = response.data.roles;
      state.roleGroup = response.roleGroup;
      state.postGroup = response.postGroup;
   });
};

getUser();
</script>
