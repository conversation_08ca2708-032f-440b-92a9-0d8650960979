import useDictStore from '@/store/modules/dict'
import { getDicts } from '@/api/basic/dict/data'
import i18n from '@/locales/index';
const { t } = i18n.global;

/**
 * 获取字典数据 p.dictLabel  dictEngLabel dictPtLabel
 */
export function useDict(...args) {
  const res = ref({});;
  return (() => {
    args.forEach((dictType, index) => {
      res.value[dictType] = [];
      const dicts = useDictStore().getDict(dictType);
      if (dicts) {
        res.value[dictType] = dicts;
      } else {
        getDicts(dictType).then(resp => {
          res.value[dictType] = resp.data.map(p => ({ label: t('common.currentLanguage') == "zh" ? p.dictLabel : t('common.currentLanguage') == "en" ? p.dictEngLabel : p.dictPtLabel, value: p.dictValue, elTagType: p.listClass ?? "primary", elTagClass: p.cssClass }))
          useDictStore().setDict(dictType, res.value[dictType]);
        })
      }
    })
    return toRefs(res.value);
  })()
}