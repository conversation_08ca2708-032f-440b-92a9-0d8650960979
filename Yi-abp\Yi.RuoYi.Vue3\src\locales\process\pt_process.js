export default {
    bom: {
    },
    route: {
    },
    rtfile: {
        query: {
            materialCode: "Código produto",
            materialCodeTip: "Código produto",
            routeCode: "Código rota",
            routeCodeTip: "Código rota",
            operationCode: "Código operação",
            operationCodeTip: "Código operação",
        },
        tbCol: {
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",
            routeCode: "Código rota",
            routeName: "Nome rota",
            routeVersion: "Versão rota",
            operationCode: "Código operação",
            fileType: "Tipo arquivo",
            fileName: "Nome arquivo",
            fileLoad: "Caminho arquivo",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Criador Id",
            creatorName: "Criador Nome",
            creationTime: "Tempo criação",
            lastModifierId: "ID modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo Modificação",
            isDeleted: "Apagado",
            operation: "Operação",
        },
        button: {
            search: "Procurar",
            reset: "Redefinir",
            add: "Adicionar",
            synchronous: "Sincronização",
            import: "Importação",
            importSOP: "Importação SOP",
            edit: "Editar",
            delete: "Apagar",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
            materialCode: "Código produto",
            materialCodeTip: "Código produto",
            materialName: "Nome produto",
            materialNameTip: "Nome produto",
            materialVersion: "Versão produto",
            materialVersionTip: "Versão produto",
            routeCode: "Código rota",
            routeCodeTip: "Código rota",
            routeName: "Nome rota",
            routeNameTip: "Nome rota",
            routeVersion: "Versão rota",
            routeVersionTip: "Versão rota",
            operationCode: "Código operação",
            operationCodeTip: "Código operação",
            fileType: "Tipo arquivo",
            fileTypeTip: "Tipo arquivo",
            fileName: "Nome arquivo",
            fileNameTip: "Nome arquivo",
            fileLoad: "Caminho arquivo",
            fileLoadTip: "Caminho arquivo",

            status: "Estado",
            remark: "Observação",
            remarkTip: "Observação",
        },
        message: {
            alarmMsg01: "Por favor preencha o código do produto",
            alarmMsg02: "Por favor preencha a versão do produto",
            alarmMsg03: "Por favor preencha o código de processo",
            alarmMsg04: "Por favor preencha a versão do processo",
            alarmMsg05: "Por favor preencha o código de operação",
            alarmMsg06: "Falha: apenas arquivos em formato PDF podem ser carregados",
            alarmMsg07: "Falha: tamanho do arquivo não pode exceder 50MB",
            alarmMsg08: "Sucesso salvo de dados",
            alarmMsg09: "Falha na importação",
            alarmMsg10: "",

            addTit: "Adicionar",
            addSuc: "Adicionar sucesso",
            editTit: "Editar",
            editSuc: "Editar sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Apagar sucesso",
            importSuc: "Importação sucesso",
            syncSuc: "Synchronous Success",
        }
    },
    rtparam: {
        query: {
            lineCode: "Código linha",
            lineCodeTip: "Código linha",
            materialCode: "Código produto",
            materialCodeTip: "Código produto",
            operationCode: "Código operação",
            operationCodeTip: "Código operação",
            paramCode: "Código param",
            paramCodeTip: "Código param",
        },
        tbCol: {
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",
            lineCode: "Código linha",
            operationCode: "Código operação",
            machineCode: "Código produto",
            paramCode: "Código Param",
            paramName: "Nome Param",
            standardRange1: "Limite inferior",
            standardRange2: "Limite superior",
            standardValue: "Valor padrão",
            targetValue: "Valor alvo",
            addTime: "Adicionar Tempo",
            editTime: "editar Tempo",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Criador Id",
            creatorName: "Criador Nome",
            creationTime: "Tempo criação",
            lastModifierId: "Último ID modificador",
            lastModifierName: "Último Nome modificador",
            lastModificationTime: "Tempo Modificação",
            isDeleted: "Apagado",
            operation: "Operação",
        },
        button: {
            search: "Procurar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronização",
            import: "Importação",
            edit: "Editar",
            delete: "Apagar",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
            materialCode: "Código produto",
            materialCodeTip: "Código produto",
            materialName: "Nome produto",
            materialNameTip: "Nome produto",
            materialVersion: "Versão produto",
            materialVersionTip: "Versão produto",
            lineCode: "Código linha",
            lineCodeTip: "Código linha",
            operationCode: "Código operação",
            operationCodeTip: "Código operação",
            machineCode: "Código produto",
            machineCodeTip: "Código produto",
            paramCode: "Código Param",
            paramCodeTip: "Código Param",
            paramName: "Nome Param",
            paramNameTip: "Nome Param",
            standardRange1: "Limite inferior",
            standardRange1Tip: "Limite inferior",
            standardRange2: "Limite superior",
            standardRange2Tip: "Limite superior",
            standardValue: "Valor padrão",
            standardValueTip: "Valor padrão",
            targetValue: "Valor alvo",
            targetValueTip: "Valor alvo",

            status: "Estado",
            remark: "Observação",
            remarkTip: "Observação",
        },
        message: {
            alarmMsg01: "Por favor preencha o código do produto",
            alarmMsg02: "Por favor preencha a versão do produto",
            alarmMsg03: "Por favor preencha o código de linha",
            alarmMsg04: "Por favor preencha o código de processo",
            alarmMsg05: "Por favor preencha o código do parâmetro",
            alarmMsg06: "Por favor preencha o valor limite inferior",
            alarmMsg07: "Por favor preencha o valor limite superior",
            alarmMsg08: "Por favor preencha o valor padrão",
            alarmMsg09: "Por favor preencha o valor alvo",
            alarmMsg10: "Por favor preencha o código de linha",

            addTit: "Adicionar",
            addSuc: "Adicionar sucesso",
            editTit: "Editar",
            editSuc: "Editar sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Apagar sucesso",
            syncSuc: "Sincronização sucesso",
            importSuc: "Importação sucesso",
        }
    }
}