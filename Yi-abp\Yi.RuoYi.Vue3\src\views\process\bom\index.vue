<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="产品编码" prop="materialCode">
            <el-input v-model="queryParams.materialCode" placeholder="产品编码" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="产品编码" prop="assemblyMaterialCode">
            <el-input v-model="queryParams.assemblyMaterialCode" placeholder="产品编码" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="工序编码" prop="operationCode">
            <el-input v-model="queryParams.operationCode" placeholder="工序编码" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['process:bom:add']">新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['process:bom:down']" :loading="downLoading" v-if="false">同步</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['process:bom:import']" :loading="importLoading" v-if="false">导入</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column align="center" label="编号" prop="id" v-if="false" />
         <el-table-column align="center" label="产线编号" prop="lineCode" width="120" />
         <!-- <el-table-column align="center" label="产线名称" prop="lineName" /> -->
         <el-table-column align="center" label="产品编号" prop="materialCode" width="120" />
         <el-table-column align="center" label="产品名称" prop="materialName" min-width="260" />
         <el-table-column align="center" label="产品版本" prop="materialVersion" />
         <el-table-column align="center" label="BomId" prop="productBomId" width="174" />
         <el-table-column align="center" label="Bom类型" prop="productBomType" width="100" />
         <el-table-column align="center" label="Bom版本" prop="productBomVersion" />
         <el-table-column align="center" label="工序编号" prop="operationCode" width="120" />
         <el-table-column align="center" label="物料Id" prop="assemblyMaterialId" />
         <el-table-column align="center" label="物料编号" prop="assemblyMaterialCode" width="120" />
         <el-table-column align="center" label="物料名称" prop="assemblyMaterialName" width="100" />
         <el-table-column align="center" label="物料版本" prop="assemblyMaterialVersion" />
         <el-table-column align="center" label="物料单位" prop="assemblyUnitCode" />
         <el-table-column align="center" label="物料用量" prop="assemblyConsumption" width="80" />
         <el-table-column align="center" label="标准工时" prop="standWorkHours" />
         <el-table-column align="center" label="追溯方式" prop="retroactive" />
         <el-table-column align="center" label="创建时间" prop="addTime" width="155" />
         <el-table-column align="center" label="更新时间" prop="editTime" width="155" />

         <el-table-column label="排序" prop="orderNum" v-if="false"> </el-table-column>
         <el-table-column label="状态" prop="status">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="备注" prop="remark" v-if="false" />
         <el-table-column label="创建者" prop="creatorId" v-if="false" />
         <el-table-column label="创建者" prop="creatorName" v-if="false" />
         <el-table-column label="创建时间" prop="creationTime" v-if="false" width="155" />
         <el-table-column label="处理人" prop="lastModifierId" v-if="false" />
         <el-table-column label="处理人" prop="lastModifierName" v-if="false" />
         <el-table-column label="处理时间" prop="lastModificationTime" v-if="false" width="155" />
         <el-table-column label="是否删除" prop="isDeleted" v-if="false" />

         <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['process:bom:edit']">编辑</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['process:bom:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-row>
               <el-col :span="12">
                  <el-form-item label="BomId" prop="productBomId">
                     <el-input v-model="form.productBomId" placeholder="BomId" />
                  </el-form-item>
                  <el-form-item label="Bom类型" prop="productBomType">
                     <el-input v-model="form.productBomType" placeholder="Bom类型" />
                  </el-form-item>
                  <el-form-item label="Bom版本" prop="productBomVersion">
                     <el-input v-model="form.productBomVersion" placeholder="Bom版本" />
                  </el-form-item>
                  <el-form-item label="产线编号" prop="lineCode">
                     <el-input v-model="form.lineCode" placeholder="产线编号" />
                  </el-form-item>
                  <!-- <el-form-item label="产线名称" prop="lineName">
                     <el-input v-model="form.lineName" placeholder="产线名称" />
                  </el-form-item> -->
                  <el-form-item label="产品编号" prop="materialCode">
                     <el-input v-model="form.materialCode" placeholder="产品编号" />
                  </el-form-item>
                  <el-form-item  label="产品名称" prop="materialName">
                     <el-input v-model="form.materialName" placeholder="产品名称" />
                  </el-form-item>
                  <el-form-item label="产品版本" prop="materialVersion">
                     <el-input v-model="form.materialVersion" placeholder="产品版本" />
                  </el-form-item>
                  <el-form-item label="创建时间" prop="addTime" >
                     <el-input v-model="form.addTime" placeholder="创建时间" />
                  </el-form-item>
                  <el-form-item label="更新时间" prop="editTime" >
                     <el-input v-model="form.editTime" placeholder="更新时间" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="工序编号" prop="operationCode">
                     <el-input v-model="form.operationCode" placeholder="工序编号" />
                  </el-form-item>
                  <el-form-item label="物料Id" prop="assemblyMaterialId">
                     <el-input v-model="form.assemblyMaterialId" placeholder="物料Id" />
                  </el-form-item>
                  <el-form-item label="物料编号" prop="assemblyMaterialCode">
                     <el-input v-model="form.assemblyMaterialCode" placeholder="物料编号" />
                  </el-form-item>
                  <el-form-item label="物料名称" prop="assemblyMaterialName">
                     <el-input v-model="form.assemblyMaterialName" placeholder="物料名称" />
                  </el-form-item>
                  <el-form-item label="物料版本" prop="assemblyMaterialVersion">
                     <el-input v-model="form.assemblyMaterialVersion" placeholder="物料版本" />
                  </el-form-item>
                  <el-form-item label="物料单位" prop="assemblyUnitCode">
                     <el-input v-model="form.assemblyUnitCode" placeholder="物料单位" />
                  </el-form-item>
                  <el-form-item label="物料用量" prop="assemblyConsumption">
                     <el-input-number v-model="form.assemblyConsumption" precision="3" :min="0" />
                  </el-form-item>
                  <el-form-item label="标准工时" prop="standWorkHours">
                     <el-input  v-model="form.standWorkHours" placeholder="标准工时" />
                  </el-form-item>
                  <el-form-item label="追溯方式" prop="retroactive">
                     <el-input v-model="form.retroactive" placeholder="追溯方式" />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item label="状态" prop="status">
                     <el-select v-model="form.status">
                        <el-option v-for="dict in base_is_open"  :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
                     </el-select>
                  </el-form-item>
                  <el-form-item label="备注" prop="remark">
                     <el-input v-model="form.remark"  type="textarea" rows="3" placeholder="请输入内容" />
                  </el-form-item>
               </el-col>
         </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定 </el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Bom">
/** 引入----------------------------------------------------------------------------*/
import { listDataAsync, listData, addData, delData, getData, updateData, getDown } from "@/api/process/bom";

const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");


/** 结构定义----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      orderCode: [{ required: true, message: "请填写工单编号", trigger: "blur" }],
      orderQty: [{ required: true, message: "请填写工单数量", trigger: "blur" }],
      scheduleCode: [{ required: true, message: "请填写排程编号", trigger: "blur" }],
      scheduleQty: [{ required: true, message: "请填写排程数量", trigger: "blur" }],
      productBomId: [{ required: true, message: "请填写BomId", trigger: "blur" }],
      productBomType: [{ required: true, message: "请填写Bom类型", trigger: "blur" }],
      productBomVersion: [{ required: true, message: "请填写Bom版本", trigger: "blur" }],
      lineCode: [{ required: true, message: "请填写产线编码", trigger: "blur" }],
      lineName: [{ required: true, message: "请填写产线名称", trigger: "blur" }],
      materialCode: [{ required: true, message: "请填写产品编号", trigger: "blur" }],
      materialName: [{ required: true, message: "请填产品名称", trigger: "blur" }],
      materialVersion: [{ required: true, message: "请填写产品版本", trigger: "blur" }],
      operationCode: [{ required: true, message: "请填工序编号", trigger: "blur" }],
      assemblyMaterialId: [{ required: true, message: "请填物料Id", trigger: "blur" }],
      assemblyMaterialCode: [{ required: true, message: "请填物料编号", trigger: "blur" }],
      assemblyMaterialName: [{ required: true, message: "请填物料名称", trigger: "blur" }],
      assemblyMaterialVersion: [{ required: true, message: "请填物料版本", trigger: "blur" }],
      assemblyUnitCode: [{ required: true, message: "请填写物料单位", trigger: "blur" }],
      assemblyConsumption: [{ required: true, message: "请填物料户量", trigger: "blur" }],
      standWorkHours: [{ required: true, message: "请填标准工时", trigger: "blur" }],
      retroactive: [{ required: true, message: "请填追溯方式", trigger: "blur" }]
   }
});
const { form, queryParams, rules } = toRefs(data);

/** 列表----------------------------------------------------------------------------*/
/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置搜索条件 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 列表操作----------------------------------------------------------------------------*/
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 编辑页面操作----------------------------------------------------------------------------*/
/** 重置提交表单 */
function reset() {
   form.value = {
      id: undefined,
   };
   proxy.resetForm("submitRef");
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess("编辑成功");
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 增删改按钮----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm('是否删除编号为"' + postIds + '"的数据项？').then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

/** 数据同步----------------------------------------------------------------------------*/
/** 同步按钮操作 */
function handleDown() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess("同步成功,变更行数:"+response.data);
      }, 1500);
   });
}

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess("导入成功");
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>