export default {
    wkQueue: {
        query: {
            scheduleCode: "排程编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            snNumber: "Sn编号",
        },
        tbCol: {
            snNumber: "Sn编号",
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            factoryCode: "工厂编号",
            wkShopCode: "车间编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            bomId: "BomId",
            bomType: "Bom类型",
            bomVersion: "Bom版本",
            routeNumber: "工艺编号",
            routeVersion: "工艺版本",
            onlineTime: "上线时间",
            offLineTime: "下线时间",
            shiftNo: "班组",
            shiftTime: "班次",
            isNeedCheck: "检验件",
            isWarning: "是否安灯",
            badData: "是否不良",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
        },
        message: {
        }
    },
    psStation: {
        query: {
            scheduleCode: "排程编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            snNumber: "Sn编号",
        },
        tbCol: {
            snNumber: "Sn编号",
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            factoryCode: "工厂编号",
            wkShopCode: "车间编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            bomId: "BomId",
            bomType: "Bom类型",
            bomVersion: "Bom版本",
            routeNumber: "工艺编号",
            routeVersion: "工艺版本",

            trayNumber: "托盘编号",
            stationCode: "工位编号",
            operationCode: "工序编号",
            isFirstOrEnd: "进站/出站",
            isPassBegin: "进站",
            passBeginNum: "进站数量",
            passBeginTime: "进站时间",
            isPassEnd: "末序下站",
            passEndNum: "出站数量",
            passEndTime: "出站时间",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
        },
        message: {
        }
    },
    mBind: {
        query: {
            scheduleCode: "排程编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            snNumber: "Sn编号",
            assemblyMaterialCode: "材料编号",
            assemblyMaterialSn: "材料Sn",
        },
        tbCol: {
            snNumber: "Sn编号",
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            factoryCode: "工厂编号",
            wkShopCode: "车间编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            stationCode: "工位编号",
            operationCode: "工序编号",

            assemblyMaterialCode: "物料编号",
            assemblyMaterialName: "物料名称",
            assemblyMaterialVersion: "物料版本",
            assemblyMaterialQty: "物料数量",
            assemblySort: "装配顺序",
            assemblyTime: "装配时间",
            assemblyMaterialSn: "物料Sn",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
        },
        message: {
        }
    },
    wkParam: {
        query: {
            scheduleCode: "排程编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            operationCode: "工序编号",
            materialCode: "产品编号",
            snNumber: "Sn编号",
        },
        tbCol: {
            snNumber: "Sn编号",
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            materialCode: "产品编号",
            materialVersion: "产品版本",
            routeNumber: "工艺编号",
            routeVersion: "工艺版本",
            bomId: "BomId",
            bomType: "Bom类型",
            bomVersion: "Bom版本",
            factoryCode: "工厂编号",
            wkShopCode: "车间编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            operationCode: "工序编号",
            lastOperationCode: "前工序",
            nextOperationCode: "后工序",

            paramCode: "参数编号",
            paramName: "参数名称",

            standardRange1: "下限",
            standardRange2: "上限",
            standardValue: "标准值",
            targetValue: "目标值",
            realValue: "实际值",
            checkResult: "是否合格",
            redoCount: "重做次数",
            isWarning: "是否安灯",
            IsBad: "是否不良",
            isSkip: "是否跳过",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            start: "开始",
            stop: "结束",
            restore: "恢复",
            cancle: "取消",
            close: "关闭",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
        },
        message: {
        }
    },
    wkFeeding: {},
    wkTask: {},
}