<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('process.rtparam.query.lineCode')" prop="lineCode">
            <el-input v-model="queryParams.lineCode" :placeholder="t('process.rtparam.query.lineCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('process.rtparam.query.materialCode')" prop="materialCode">
            <el-input v-model="queryParams.materialCode" :placeholder="t('process.rtparam.query.materialCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('process.rtparam.query.operationCode')" prop="operationCode">
            <el-input v-model="queryParams.operationCode" :placeholder="t('process.rtparam.query.operationCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('process.rtparam.query.paramCode')" prop="paramCode">
            <el-input v-model="queryParams.paramCode" :placeholder="t('process.rtparam.query.paramCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('process.rtparam.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('process.rtparam.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['process:rtparams:add']">{{t('process.rtparam.button.add')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['process:rtparams:down']" :loading="downLoading">{{t('process.rtparam.button.sync')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-upload class="upload-demo" :disabled="importDisabled" :on-change="handleImport" accept="xls,xlsx" :auto-upload="false" :multiple="true" :limit="2" :show-file-list ="false">
               <el-button type="warning" plain icon="Download" v-hasPermi="['process:rtparams:import']" :loading="importLoading">{{t('process.rtparam.button.import')}}</el-button>
            </el-upload>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column :label="t('process.rtparam.tbCol.materialCode')" align="center" prop="materialCode" width="120" />
         <el-table-column :label="t('process.rtparam.tbCol.materialName')" align="center" prop="materialName"  min-width="260" />
         <el-table-column :label="t('process.rtparam.tbCol.materialVersion')" align="center" prop="materialVersion" width="80" />
         <el-table-column :label="t('process.rtparam.tbCol.lineCode')" align="center" prop="lineCode" width="120" />
         <el-table-column :label="t('process.rtparam.tbCol.operationCode')" align="center" prop="operationCode" width="120" />
         <el-table-column :label="t('process.rtparam.tbCol.machineCode')" align="center" prop="machineCode" width="120" />
         <el-table-column :label="t('process.rtparam.tbCol.paramCode')" align="center" prop="paramCode" width="120" />
         <el-table-column :label="t('process.rtparam.tbCol.paramName')" align="center" prop="paramName" width="180" />
         <el-table-column :label="t('process.rtparam.tbCol.standardRange1')" align="center" prop="standardRange1"  />
         <el-table-column :label="t('process.rtparam.tbCol.standardRange2')" align="center" prop="standardRange2"  />
         <el-table-column :label="t('process.rtparam.tbCol.standardValue')" align="center" prop="standardValue" />
         <el-table-column :label="t('process.rtparam.tbCol.targetValue')" align="center" prop="targetValue" />
         <el-table-column :label="t('process.rtparam.tbCol.addTime')" align="center" prop="addTime" width="155" />
         <el-table-column :label="t('process.rtparam.tbCol.editTime')" align="center" prop="editTime" width="155" />

         <el-table-column prop="orderNum" :label="t('process.rtparam.tbCol.order')" v-if="false"></el-table-column>
         <el-table-column prop="status" :label="t('process.rtparam.tbCol.status')">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('process.rtparam.tbCol.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('process.rtparam.tbCol.creator')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('process.rtparam.tbCol.creator')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('process.rtparam.tbCol.creatTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('process.rtparam.tbCol.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('process.rtparam.tbCol.lastModifierId')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('process.rtparam.tbCol.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('process.rtparam.tbCol.ifdelete')" prop="isDeleted" v-if="false" />

         <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['process:rtparams:edit']">{{t('process.rtparam.button.edit')}}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['process:rtparams:remove']">{{t('process.rtparam.button.delete')}}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="140px">
            <el-row>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.materialCode')" prop="materialCode">
                     <el-input v-model="form.materialCode" :placeholder="t('process.rtparam.form.materialCode')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.materialName')" prop="materialName">
                     <el-input v-model="form.materialName" :placeholder="t('process.rtparam.form.materialName')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.materialVersion')" prop="materialVersion">
                     <el-input v-model="form.materialVersion" :placeholder="t('process.rtparam.form.materialVersion')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.lineCode')" prop="lineCode">
                     <el-input v-model="form.lineCode" :placeholder="t('process.rtparam.form.lineCode')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.operationCode')" prop="operationCode">
                     <el-input v-model="form.operationCode" :placeholder="t('process.rtparam.form.operationCode')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.machineCode')" prop="machineCode">
                     <el-input v-model="form.machineCode" :placeholder="t('process.rtparam.form.machineCode')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.paramCode')" prop="paramCode">
                     <el-input v-model="form.paramCode" :placeholder="t('process.rtparam.form.paramCode')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.paramName')" prop="paramName">
                     <el-input v-model="form.paramName" :placeholder="t('process.rtparam.form.paramName')" />
                  </el-form-item>
               </el-col>

               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.standardRange1')" prop="standardRange1">
                     <el-input-number v-model="form.standardRange1" precision="3" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.standardRange2')" prop="standardRange2">
                     <el-input-number v-model="form.standardRange2" precision="3" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.standardValue')" prop="standardValue">
                    <el-input-number v-model="form.standardValue" precision="3" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('process.rtparam.form.targetValue')" prop="targetValue">
                    <el-input-number v-model="form.targetValue" precision="3" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('process.rtparam.form.status')">
                     <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in base_is_open" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('process.rtparam.form.remark')" prop="remark">
                     <el-input v-model="form.Remark" type="textarea" rows="3" :placeholder="t('process.rtparam.form.remarkTip')" />
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="RtParams">
/** 引入----------------------------------------------------------------------------*/
import { listDataAsync, listData, getData, delData, addData, updateData, getDown, importAsync } from "@/api/process/rtparams";
import * as XLSX from 'xlsx';  
import moment from 'moment';
import { genFileId } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { ElConfigProvider } from 'element-plus';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

/** 定义变量----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const importDisabled = ref(false);
const tableData = ref([]);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      materialCode: [{ required: true, message: t('process.rtparam.message.alarmMsg01'), trigger: "blur" }],
      materialVersion: [{ required: true, message: t('process.rtparam.message.alarmMsg02'), trigger: "blur" }],      
      lineCode: [{ required: true, message: t('process.rtparam.message.alarmMsg03'), trigger: "blur" }],
      operationCode: [{ required: true, message: t('process.rtparam.message.alarmMsg04') , trigger: "blur" }],
      paramCode: [{ required: true, message: t('process.rtparam.message.alarmMsg05'), trigger: "blur" }],
      standardRange1: [{ required: true, message: t('process.rtparam.message.alarmMsg06'), trigger: "blur" }],
      standardRange2: [{ required: true, message: t('process.rtparam.message.alarmMsg07'), trigger: "blur" }],
      standardValue: [{ required: true, message: t('process.rtparam.message.alarmMsg08'), trigger: "blur" }],
      targetValue: [{ required: true, message: t('process.rtparam.message.alarmMsg09'), trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 列表操作 ----------------------------------------------------------------------------*/
/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 编辑页面数据----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('process.rtparam.message.addTit');
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('process.rtparam.message.editTit');
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('process.rtparam.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('process.rtparam.message.delSuc'));
   }).catch(() => { });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('process.rtparam.message.editSuc'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('process.rtparam.message.addSuc'));
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1,
   };
   proxy.resetForm("submitRef");
}

/** 数据同步----------------------------------------------------------------------------*/
function handleDown() {
   if(queryParams.value.lineCode == undefined)
   {
      proxy.$modal.msgWarning(t('process.rtparam.message.alarmMsg10'));
      return;
   }
   downLoading.value = true;
   getDown(queryParams.value.lineCode).then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('process.rtparam.message.syncSuc') + response.data);
      }, 1500);
   });
}

/** Excel导入----------------------------------------------------------------------------*/
/** 导入按钮操作 */
const handleImport = async (file,files) => {
   importLoading.value = true;
   importDisabled.value = true;
   if(files.length > 1){
      files.shift();
   };
   const data = await getXlsxData(file);
   tableData.value = translateField(data);

   // 数字转字符串
   tableData.value = JSON.parse(JSON.stringify(tableData.value, (key, value) => typeof value === 'number' ? String(value) : value));

   importAsync(tableData.value).then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         importLoading.value = false;
         importDisabled.value = false;
         proxy.$modal.msgSuccess(t('process.rtparam.message.importSuc') + response.data);
      }, 1500);
   });
}
//读取表格数据
const getXlsxData = async (file) => {
   const dataBinary = await readFile(file);
   const workBook = XLSX.read(dataBinary ,{
      type: "binary",
      cellDates: true
   });
   const workSheet = workBook.Sheets[workBook.SheetNames[0]];
   const data = XLSX.utils.sheet_to_json(workSheet);
   data.forEach((item) => {
      const keys = ["StartTime","EndTime"];
      for (const key in item) {
         if (moment(item[key], 'YYYY-MM-DD', true).isValid()) {
            item[key] = moment(item[key]).format("YYYY-MM-DD HH:mm:ss")
         }
      }
   });
   return data;
}
//读取excel文件
const readFile = (file) => {
   return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file.raw)
      reader.onload = (e) => {
         resolve(e.target.result)
      }
      reader.onerror = (e) => {
         reject(e)
      }
   })
}
//映射字段
const translateField = (data) => {
  const arr = []
  const cnToEn = {
      产线编号:'LineCode',
      工序编号:'OperationCode',
      产品编号:'MaterialCode',
      产品名称:'MaterialName',
      产品版本:'MaterialVersion',
      机台编号:'MachineCode',
      参数编号:'ParamCode',
      参数名称:'ParamName',
      下限值:'StandardRange1',
      上限值:'StandardRange2',
      标准值:'StandardValue',
      目标值:'TargetValue',
      添加时间:'AddTime',
      更新时间:'EditTime',      
   }
   data.forEach((item) => {
      const arrItem = {}
      Object.keys(item).forEach((key) => {
         arrItem[cnToEn[key]] = item[key]
      })
      arr.push(arrItem) 
   })
   return arr
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>