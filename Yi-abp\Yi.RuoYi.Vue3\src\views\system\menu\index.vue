<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
         <el-form-item :label="t('system.menu.query.menuName')" prop="menuName">
            <el-input v-model="queryParams.menuName" :placeholder="t('system.menu.query.menuName')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('system.menu.query.status')" prop="state">
            <el-select v-model="queryParams.state" :placeholder="t('system.menu.query.status')" clearable style="width: 140px">
               <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('system.menu.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('system.menu.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:menu:add']">{{t('system.menu.button.add')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="info" plain icon="Sort" @click="toggleExpandAll">{{t('system.menu.button.toggleExpand')}}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-if="refreshTable" v-loading="loading" :data="menuList" row-key="id" :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
         <el-table-column align="center" type="selection" width="30" v-if="false" />
         <el-table-column prop="menuName" :label="t('system.menu.tbCol.menuName')" :show-overflow-tooltip="true" width="160" />
         <el-table-column prop="menuEngName" :label="t('system.menu.tbCol.engName')" :show-overflow-tooltip="true" width="160" />
         <el-table-column prop="menuPtName" :label="t('system.menu.tbCol.ptName')" :show-overflow-tooltip="true" width="240" />
         <el-table-column prop="menuIcon" :label="t('system.menu.tbCol.menuIcon')" align="center" width="100">
            <template #default="scope">
               <svg-icon :icon-class="scope.row.menuIcon + ''" />
            </template>
         </el-table-column>
         <el-table-column prop="orderNum" :label="t('system.menu.tbCol.orderNum')" width="120"></el-table-column>
         <el-table-column prop="permissionCode" :label="t('system.menu.tbCol.permissionCode')" :show-overflow-tooltip="true" min-width="155"></el-table-column>
         <el-table-column prop="router" :label="t('system.menu.tbCol.router')" :show-overflow-tooltip="true"></el-table-column>
         <el-table-column prop="component" :label="t('system.menu.tbCol.component')" :show-overflow-tooltip="true" min-width="120"></el-table-column>
         <el-table-column prop="state" :label="t('system.menu.tbCol.status')" width="100">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.state" />
            </template>
         </el-table-column>
         <el-table-column :label="t('system.menu.tbCol.creationTime')" align="center" prop="creationTime" min-width="155">
            <template #default="scope">
               <span>{{ parseTime(scope.row.creationTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="t('system.menu.tbCol.operation')" align="center" width="200" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:menu:edit']">{{t('system.menu.button.edit')}}</el-button>
               <el-button auto-insert-space link icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:menu:add']">{{t('system.menu.button.add')}}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:menu:remove']">{{t('system.menu.button.delete')}}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <!-- 添加或编辑菜单对话框 -->
      <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
         <el-form ref="menuRef" :model="form" :rules="rules" label-width="150px">
            <el-row>
               <el-col :span="24">
                  <el-form-item :label="t('system.menu.form.lastMenu')">
                     <el-tree-select v-model="form.parentId" :data="menuOptions" :props="{ value: 'id', label: 'menuName', children: 'children' }" value-key="id" placeholder="选择上级菜单" check-strictly />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('system.menu.form.menuType')" prop="menuType">
                     <el-radio-group v-model="form.menuType">
                        <el-radio value="Catalogue">{{t('system.menu.form.catalogue')}}</el-radio>
                        <el-radio value="Menu">{{t('system.menu.form.menu')}}</el-radio>
                        <el-radio value="Component">{{t('system.menu.form.button')}}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="24" v-if="form.menuType != 'Component'">
                  <el-form-item :label="t('system.menu.form.menuIcon')" prop="menuIcon">
                     <el-popover placement="bottom-start" :width="540" trigger="click">
                        <template #reference>
                           <el-input v-model="form.menuIcon" :placeholder="t('system.menu.form.choiceIcon')" readonly>
                              <template #prefix>
                                 <svg-icon v-if="form.menuIcon" :icon-class="form.menuIcon + ''" class="el-input__icon" style="height: 32px;width: 16px;" />
                                 <el-icon v-else style="height: 32px;width: 16px;">
                                    <search />
                                 </el-icon>
                              </template>
                           </el-input>
                        </template>
                        <icon-select ref="iconSelectRef" @selected="selected" />
                     </el-popover>
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('system.menu.form.menuName')" prop="menuName">
                     <el-input v-model="form.menuName" :placeholder="t('system.menu.form.menuName')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('system.menu.form.engName')" prop="menuEngName">
                     <el-input v-model="form.menuEngName" :placeholder="t('system.menu.form.engName')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('system.menu.form.ptName')" prop="menuPtName">
                     <el-input v-model="form.menuPtName" :placeholder="t('system.menu.form.ptName')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('system.menu.form.orderNum')" prop="orderNum">
                     <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'Component'">
                  <el-form-item>
                     <template #label>
                        <span>
                           <el-tooltip :content="t('system.menu.form.linkTip')" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>{{t('system.menu.form.outLink')}}
                        </span>
                     </template>
                     <el-radio-group v-model="form.isLink">
                        <el-radio :value=true>{{t('system.menu.form.yes')}}</el-radio>
                        <el-radio :value=false>{{t('system.menu.form.no')}}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>

               <el-col :span="12" v-if="form.menuType != 'Component'">
                  <el-form-item prop="router">
                     <template #label>
                        <span>
                           <el-tooltip :content="t('system.menu.form.routerContent')" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>{{t('system.menu.form.router')}}
                        </span>
                     </template>
                     <el-input v-model="form.router" :placeholder="t('system.menu.form.router')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType == 'Menu'">
                  <el-form-item prop="component">
                     <template #label>
                        <span>
                           <el-tooltip :content="t('system.menu.form.componentContent')" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>{{t('system.menu.form.component')}}
                        </span>
                     </template>
                     <el-input v-model="form.component" :placeholder="t('system.menu.form.component')" />
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'Catalogue'">
                  <el-form-item>
                     <el-input v-model="form.permissionCode" :placeholder="t('system.menu.form.permission')" maxlength="150" />
                     <template #label>
                        <span>
                           <el-tooltip :content="t('system.menu.form.permissionContent')" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>{{t('system.menu.form.permission')}}
                           </span>
                     </template>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType == 'Menu'">
                  <el-form-item>
                     <el-input v-model="form.query" :placeholder="t('system.menu.form.routeParam')" maxlength="255" />
                     <template #label>
                        <span>
                           <el-tooltip :content="t('system.menu.form.paramContent')" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>{{t('system.menu.form.routeParam')}}
                        </span>
                     </template>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType == 'Menu'">
                  <el-form-item>
                     <template #label>
                        <span>
                           <el-tooltip :content="t('system.menu.form.cacheContent')" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>{{t('system.menu.form.cache')}}
                        </span>
                     </template>
                     <el-radio-group v-model="form.isCache">
                        <el-radio :value=true>{{t('system.menu.form.cacheYes')}}</el-radio>
                        <el-radio :value=false>{{t('system.menu.form.cacheNo')}}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'Component'">
                  <el-form-item>
                     <template #label>
                        <span>
                           <el-tooltip :content="t('system.menu.form.showContent')" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>{{t('system.menu.form.show')}}
                        </span>
                     </template>
                     <el-radio-group v-model="form.isShow">
                        <el-radio v-for="dict in sys_show_hide" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="12" v-if="form.menuType != 'Component'">
                  <el-form-item>
                     <template #label>
                        <span>
                           <el-tooltip :content="t('system.menu.form.menuStateContent')" placement="top">
                              <el-icon><question-filled /></el-icon>
                           </el-tooltip>{{t('system.menu.form.menuState')}}
                        </span>
                     </template>
                     <el-radio-group v-model="form.state">
                        <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
            </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('system.menu.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('system.menu.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Menu">
import { addMenu, delMenu, getMenu, listMenu, updateMenu } from "@/api/system/menu";
import SvgIcon from "@/components/SvgIcon";
import IconSelect from "@/components/IconSelect";
import { ClickOutside as vClickOutside } from 'element-plus'
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { sys_show_hide, sys_normal_disable } = proxy.useDict("sys_show_hide", "sys_normal_disable");

const menuList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const title = ref("");
const menuOptions = ref([]);
const isExpandAll = ref(false);
const refreshTable = ref(true);
const iconSelectRef = ref(null);

const guidEmpty = "00000000-0000-0000-0000-000000000000";
const data = reactive({
   form: {
   },
   queryParams: {
      menuName: undefined,
      visible: true
   },
   rules: {
      menuName: [{ required: true, message: t('system.menu.message.alarm01'), trigger: "blur" }],
      menuEngName: [{ required: true, message: t('system.menu.message.alarm02'), trigger: "blur" }],
      menuPtName: [{ required: true, message: t('system.menu.message.alarm03'), trigger: "blur" }],
      orderNum: [{ required: true, message: t('system.menu.message.alarm04'), trigger: "blur" }],
      router: [{ required: true, message: t('system.menu.message.alarm05'), trigger: "blur" }]
   },
});

const { queryParams, form, rules } = toRefs(data);

/** 查询菜单列表 */
function getList() {
   loading.value = true;
   console.log(queryParams.value, "queryParams.value");
   listMenu(queryParams.value).then(response => {
      menuList.value = proxy.handleTree(response.data.items, "id");
      loading.value = false;
   });
}
/** 查询菜单下拉树结构 */
function getTreeselect() {
   menuOptions.value = [];
   listMenu().then(response => {
      const menu = { id: guidEmpty, menuName:t('system.menu.message.mainClass'), children: [] };
      menu.children = proxy.handleTree(response.data.items, "id");
      menuOptions.value.push(menu);
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      parentId: guidEmpty,
      menuName: undefined,
      menuIcon: undefined,
      menuType: 0,
      orderNum: 0,
      isLink: false,
      isCache: false,
      isShow: true,
      state: true
   };
   proxy.resetForm("menuRef");
}

/** 选择图标 */
function selected(name) {
   form.value.menuIcon = name;
}

/** 搜索按钮操作 */
function handleQuery() {
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
   reset();
   getTreeselect();
   if (row != null && row.id) {
      form.value.parentId = row.id;
   } else {
      form.value.parentId = "00000000-0000-0000-0000-000000000000";
   }
   open.value = true;
   title.value =  t('system.menu.message.addTit');
}
/** 展开/折叠操作 */
function toggleExpandAll() {
   refreshTable.value = false;
   isExpandAll.value = !isExpandAll.value;
   nextTick(() => {
      refreshTable.value = true;
   });
}
/** 编辑按钮操作 */
async function handleUpdate(row) {
   reset();
   await getTreeselect();
   getMenu(row.id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('system.menu.message.editTit');
   });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["menuRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateMenu(form.value).then(response => {
               proxy.$modal.msgSuccess(t('system.menu.message.editSuc'));
               open.value = false;
               getList();
            });
         } else {
            addMenu(form.value).then(response => {
               proxy.$modal.msgSuccess(t('system.menu.message.addSuc'));;
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   proxy.$modal.confirm(t('system.menu.message.delMsg')).then(function () {
      return delMenu(row.id);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('system.menu.message.delSuc'));;
   }).catch(() => { });
}

getList();
</script>
