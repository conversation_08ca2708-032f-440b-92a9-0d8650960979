worker_processes 1;

events {
    worker_connections 1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    server {
        listen 80;

        server_name _;

        root /usr/share/nginx/html;
        index index.html index.htm;
  
        location /prod-api/ {
            # 替换成自己的后端服务地址
            proxy_pass http://localhost:19001/api/app/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header Authorization $http_authorization;
            proxy_set_header Cookie $http_cookie;
        }

        location /prod-ws/ {
            # 替换成自己的后端服务地址
            proxy_pass http://localhost:19001/hub/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection 'upgrade';
            # rewrite ^/prod-ws(/.*)$ $1 break;
        }

        location / {
            try_files $uri $uri/ /index.html;
        }


        error_page 404 /404.html;
        location = /404.html {
            root /usr/share/nginx/html;
        }

        location ~* \.(jpg|jpeg|png|gif|ico|css|js)$ {
            expires 1y;
            add_header Cache-Control "public, no-transform";
        }
    }
}
