import request from '@/utils/request'

export function listDataAsync(query) {
    return request({
        url: '/wk-queue/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/wk-queue',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/wk-queue/' + id,
        method: 'get'
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/wk-queue',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(data) {
    return request({
        url: `/wk-queue/` + data.id,
        method: 'put',
        data: data
    })
}

// 删除
export function delData(id) {
    return request({
        url: `/wk-queue/${id}`,
        method: 'delete',
    })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/wk-queue/' + roleId,
        method: 'get'
    })
}