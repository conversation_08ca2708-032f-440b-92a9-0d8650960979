<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="排程编号" prop="scheduleCode">
            <el-input v-model="queryParams.scheduleCode" placeholder="排程编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="产线编号" prop="lineCode">
            <el-input v-model="queryParams.lineCode" placeholder="产线编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="产品编号" prop="materialCode">
            <el-input v-model="queryParams.materialCode" placeholder="产品编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="SN号" prop="snNumber">
            <el-input v-model="queryParams.snNumber" placeholder="SN号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>
      
      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="30" align="center" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column align="center" label="工单号" prop="orderCode" min-width="180" />
         <el-table-column align="center" label="排程编号" prop="scheduleCode" min-width="120" />
         <el-table-column align="center" label="工厂编号" prop="factoryCode" min-width="120" />
         <el-table-column align="center" label="车间编号" prop="workshopCode" min-width="120"/>
         <el-table-column align="center" label="产线编号" prop="lineCode" min-width="120" />
         <el-table-column align="center" label="产品编号" prop="materialCode" min-width="120" />
         <el-table-column align="center" label="产品名称" prop="materialName"  min-width="100"/>
         <el-table-column align="center" label="产品版本" prop="materialVersion"  min-width="100"/>
         <el-table-column align="center" label="BomId" prop="productBomId" min-width="120"/>
         <el-table-column align="center" label="Bom类型" prop="productBomType" min-width="100"/>
         <el-table-column align="center" label="Bom版本" prop="productBomVersion" min-width="100"/>    
         <el-table-column align="center" label="工艺编码" prop="routeNumber" min-width="120" />
         <el-table-column align="center" label="工艺版本" prop="routeVersion" />
         <el-table-column align="center" label="SN号" prop="snNumber" min-width="120" />
         <el-table-column align="center" label="托盘号" prop="trayNumber" min-width="120" />
         <el-table-column align="center" label="工位编号" prop="stationCode" min-width="120" />
         <el-table-column align="center" label="工序编号" prop="operationCode" min-width="120" />
         <el-table-column align="center" label="是否首末" prop="isFirstOrEnd" min-width="80" />
         <el-table-column align="center" label="是否进站" prop="isPassBegin" min-width="80" />
         <el-table-column align="center" label="进站数量" prop="passBeginNum" />
         <el-table-column align="center" label="进站时间" prop="passBeginTime" min-width="155" />
         <el-table-column align="center" label="是否出站" prop="isPassEnd" />
         <el-table-column align="center" label="出站数量" prop="passEndNum" />
         <el-table-column align="center" label="出站时间" prop="passEndTime" min-width="155" />
      
         <el-table-column label="排序" prop="orderNum" v-if="false"> </el-table-column>
         <el-table-column label="操作状态" prop="status" v-if="false">
            <template #default="scope">
               <dict-tag :options="base_order_state" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="备注" prop="remark" v-if="false" />
         <el-table-column label="创建者" prop="creatorId" v-if="false" />
         <el-table-column label="创建者" prop="creatorName" v-if="false" />
         <el-table-column label="创建时间" prop="creationTime" v-if="false" width="155" />
         <el-table-column label="处理人" prop="lastModifierId" v-if="false" />
         <el-table-column label="处理人" prop="lastModifierName" v-if="false" />
         <el-table-column label="处理时间" prop="lastModificationTime" v-if="false" width="155" />
         <el-table-column label="是否删除" prop="isDeleted" v-if="false" />

         <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" v-if="false">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['production:psstation:edit']">编辑</el-button>
               <el-button v-if="scope.row.parentId != 0" link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['production:psstation:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="工单号" prop="orderCode">
               <el-input v-model="form.orderCode" placeholder="工单号" />
            </el-form-item>
            <el-form-item label="排程编号" prop="scheduleCode">
               <el-input v-model="form.scheduleCode" placeholder="排程编号" />
            </el-form-item>
            <el-form-item label="工厂编号" prop="factoryCode">
               <el-input v-model="form.factoryCode" placeholder="工厂编号" />
            </el-form-item>
            <el-form-item label="车间编号" prop="workshopCode">
               <el-input v-model="form.workshopCode" placeholder="工厂编号" />
            </el-form-item>
            <el-form-item label="产线编号" prop="lineCode">
               <el-input v-model="form.lineCode" placeholder="产线编号" />
            </el-form-item>
            <el-form-item label="产品编号" prop="materialCode">
               <el-input v-model="form.materialCode" placeholder="产品编号" />
            </el-form-item>
            <el-form-item  label="产品名称" prop="materialName">
               <el-input v-model="form.materialName" placeholder="产品名称" />
            </el-form-item>
            <el-form-item label="产品版本" prop="materialVersion">
               <el-input v-model="form.materialVersion" placeholder="产品版本" />
            </el-form-item>
            <el-form-item label="Sn号" prop="snNumber">
               <el-input v-model="form.snNumber" placeholder="Sn号" />
            </el-form-item>            
            <el-form-item label="BomId" prop="productBomId">
               <el-input v-model="form.productBomId" placeholder="BomId" />
            </el-form-item>
            <el-form-item label="Bom类型" prop="productBomType">
               <el-input v-model="form.productBomType" placeholder="Bom类型" />
            </el-form-item>
            <el-form-item label="Bom版本" prop="productBomVersion">
               <el-input v-model="form.productBomVersion" placeholder="Bom版本" />
            </el-form-item>
            <el-form-item label="工艺编码" prop="routeNumber">
               <el-input v-model="form.routeNumber" placeholder="工艺编码" />
            </el-form-item>
            <el-form-item label="工艺版本" prop="routeVersion">
               <el-input v-model="form.routeVersion" placeholder="工艺版本" />
            </el-form-item> 
            <el-form-item label="上线时间" prop="onlineTime">
               <el-input v-model="form.onlineTime" placeholder="上线时间" />
            </el-form-item>
            <el-form-item label="下线时间" prop="offLineTime">
               <el-input v-model="form.offLineTime" placeholder="下线时间" />
            </el-form-item>
            <el-form-item label="生产班组" prop="shiftNo">
               <el-input v-model="form.shiftNo" placeholder="生产班组" />
            </el-form-item>
            <el-form-item label="生产班次" prop="shiftTime">
               <el-input v-model="form.shiftTime" placeholder="生产班次" />
            </el-form-item>
            <el-form-item label="是否检验件" prop="isNeedCheck">
               <el-input v-model="form.isNeedCheck" placeholder="是否检验件" />
            </el-form-item>
            <el-form-item label="是否安灯" prop="isWarning">
               <el-input v-model="form.isWarning" placeholder="是否安灯" />
            </el-form-item>
            <el-form-item label="是否不良" prop="badData">
               <el-input v-model="form.badData" placeholder="是否不良" />
            </el-form-item>
            <el-form-item label="生产状态" prop="status">
               <el-select v-model="form.status" disabled>
                  <el-option v-for="dict in base_is_open"  :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
               </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
               <el-input v-model="form.remark"  type="textarea" rows="3" placeholder="请输入内容" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定 </el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="PsStation">
/** 引入----------------------------------------------------------------------------*/
import { listDataAsync, listData, addData, delData, getData, updateData } from "@/api/production/psstation";

const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

/** 结构定义----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const typeOptions = ref([]);
const tpForm = ref({});

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      // orderCode: [{ required: true, message: "请填写工单编号", trigger: "blur" }],
      // scheduleCode: [{ required: true, message: "请填写排程编号", trigger: "blur" }],
      // factoryCode: [{ required: true, message: "请填写工厂编号", trigger: "blur" }],
      // workshopCode: [{ required: true, message: "请填车间编号", trigger: "blur" }],
      // lineCode: [{ required: true, message: "请填写产线编号", trigger: "blur" }],
      // materialCode: [{ required: true, message: "请填写产品编号", trigger: "blur" }],
      // materialName: [{ required: true, message: "请填产品名称", trigger: "blur" }],
      // materialVersion: [{ required: true, message: "请填写产品版本", trigger: "blur" }],
      // Sn号: [{ required: true, message: "请填Sn号", trigger: "blur" }],
      // productBomId: [{ required: true, message: "请填写BomId", trigger: "blur" }],
      // productBomType: [{ required: true, message: "请填写Bom类型", trigger: "blur" }], 
      // productBomVersion: [{ required: true, message: "请填写Bom版本", trigger: "blur" }],
      // routeNumber: [{ required: true, message: "请填写工艺编号", trigger: "blur" }],
      // routeVersion: [{ required: true, message: "请填工艺版本", trigger: "blur" }],
      // onlineTime: [{ required: true, message: "请填上线时间", trigger: "blur" }],
      // offLineTime: [{ required: true, message: "请填下线时间", trigger: "blur" }],
      // shiftNo: [{ required: true, message: "请填班组", trigger: "blur" }],
      // shiftTime: [{ required: true, message: "请填班次", trigger: "blur" }],
      // isNeedCheck: [{ required: true, message: "是否检验件", trigger: "blur" }],
      // isWarning: [{ required: true, message: "是否触发预警", trigger: "blur" }],
      // badData: [{ required: true, message: "请填触发不良", trigger: "blur" }],
      status: [{ required: true, message: "请填状态", trigger: "blur" }]
   }
});
const { form, queryParams, rules } = toRefs(data);

/** 列表----------------------------------------------------------------------------*/
/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置搜索条件 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 列表操作----------------------------------------------------------------------------*/
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 编辑页面操作----------------------------------------------------------------------------*/
/** 重置提交表单 */
function reset() {
   form.value = {
      id: undefined,
   };
   proxy.resetForm("submitRef");
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess("编辑成功");
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 增删改按钮----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm('是否删除编号为"' + postIds + '"的数据项？').then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

/** 数据同步----------------------------------------------------------------------------*/
/** 同步按钮操作 */
function handleDown() {
   proxy.download("andon/type/export", {
      ...queryParams.value
   }, `post_${new Date().getTime()}.xlsx`);
}

/** 导入按钮操作 */
function handleImport() {
   proxy.download("andon/type/export", {
      ...queryParams.value
   }, `post_${new Date().getTime()}.xlsx`);
}

getList();
</script>