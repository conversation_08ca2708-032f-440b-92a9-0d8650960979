import request from '@/utils/request'

export function listDataAsync(query) {
    return request({
        url: '/http-option/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/http-option',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/http-option/' + id,
        method: 'get'
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/http-option',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(data) {
    return request({
        url: `/http-option/` + data.id,
        method: 'put',
        data: data
    })
}

// 删除
export function delData(modelId) {
    return request({
        url: `/http-option`,
        method: 'delete',
        params: { id: modelId }
    })
}

// 同步数据
export function getDown() {
  return request({
    url: '/http-option/getDown',
    method: 'get'
  })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/http-option/' + roleId,
        method: 'get'
    })
}