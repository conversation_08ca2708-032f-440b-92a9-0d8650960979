import request from '@/utils/request'

export function listDataAsync(query) {
    return request({
        url: '/pass-station/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/pass-station',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/pass-station/' + id,
        method: 'get'
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/pass-station',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(data) {
    return request({
        url: `/pass-station/` + data.id,
        method: 'put',
        data: data
    })
}

// 删除
export function delData(id) {
    return request({
        url: `/pass-station/${id}`,
        method: 'delete'
    })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/pass-station/' + roleId,
        method: 'get'
    })
}