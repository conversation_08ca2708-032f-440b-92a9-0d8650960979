<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="工单编号" prop="orderCode">
            <el-input v-model="queryParams.orderCode" placeholder="工单编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="排程编号" prop="scheduleCode">
            <el-input v-model="queryParams.scheduleCode" placeholder="排程编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="产线编号" prop="lineCode">
            <el-input v-model="queryParams.lineCode" placeholder="产线编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="工序编号" prop="operationCode">
            <el-input v-model="queryParams.operationCode" placeholder="工序编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="Sn编号" prop="snNumber">
            <el-input v-model="queryParams.snNumber" placeholder="SN编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column label="工单号" align="center" prop="orderCode" />
         <el-table-column label="排程编号" align="center" prop="scheduleCode" />
         <el-table-column label="产品编码" align="center" prop="materialCode" />
         <el-table-column label="产品版本" align="center" prop="materialVersion" />
         <el-table-column label="产线编码" align="center" prop="lineCode" />
         <el-table-column label="工位编号" align="center" prop="stationCode" />
         <el-table-column label="工序编码" align="center" prop="operationCode" />
         <el-table-column label="SN编码" align="center" prop="snNumber" />
         <el-table-column label="用户ID" align="center" prop="userId" />
         <el-table-column label="不良代码" align="center" prop="badCode" />
         <el-table-column label="不良因素" align="center" prop="badFactor" />
         <el-table-column label="不良数量" align="center" prop="badQty" />
         <el-table-column label="发生时间" align="center" prop="editTime" width="155" />
         <el-table-column label="不良描述" align="center" prop="description" show-overflow-tooltip="true" />
         <!-- <el-table-column label="不良图片" align="center" prop="images" show-overflow-tooltip="true" />-->
         <el-table-column label="处理意见" align="center" prop="auditOpinion" show-overflow-tooltip="true" />

         <el-table-column prop="orderNum" label="排序" v-if="false"></el-table-column>
         <el-table-column prop="status" label="状态">
            <template #default="scope">
               <dict-tag :options="sys_manage_state" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="备注" prop="remark" v-if="false" />
         <el-table-column label="创建者" prop="creatorId" v-if="false" />
         <el-table-column label="创建者" prop="creatorName" v-if="false" />
         <el-table-column label="创建时间" prop="creationTime" width="155" v-if="false" />
         <el-table-column label="处理人" prop="lastModifierId" v-if="false" />
         <el-table-column label="处理人" prop="lastModifierName" v-if="false" />
         <el-table-column label="处理时间" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column label="是否删除" prop="isDeleted" v-if="false" />

         <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['quality:baddata:edit']">编辑</el-button>
               <!-- <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['quality:baddata:remove']">删除</el-button> -->
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-row>
               <el-col :span="12">
                  <el-form-item label="SN编码" prop="snNumber">
                     <el-input v-model="form.snNumber" placeholder="SN编码" disabled />
                  </el-form-item>
                  <el-form-item label="工序编码" prop="operationCode">
                     <el-input v-model="form.operationCode" placeholder="工序编码" disabled />
                  </el-form-item>
                  <el-form-item label="用户ID" prop="userId">
                     <el-input v-model="form.userId" placeholder="用户ID" disabled />
                  </el-form-item>
                  <el-form-item label="发生时间" prop="editTime">
                     <el-input v-model="form.editTime" placeholder="发生时间" disabled />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="不良代码" prop="badCode">
                     <el-input v-model="form.badCode" placeholder="不良代码" />
                  </el-form-item>
                  <el-form-item label="不良因素" prop="badFactor">
                     <el-input v-model="form.badFactor" placeholder="不良因素" />
                  </el-form-item>
                  <el-form-item label="不良数量" prop="badQty">
                     <el-input v-model="form.badQty" placeholder="不良数量" />
                  </el-form-item>
                  <el-form-item label="状态">
                     <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in sys_manage_state" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item label="不良图片" prop="images">
                     <div class="demo-image block">
                        <!-- <el-image v-for="url in urls" :key="url" :src="url" lazy /> -->
                        <el-image style="width: 50px; height: 50px" :src="form.images" fit="scale-down" :preview-src-list=[form.images] lazy />
                     </div>
                  </el-form-item>
                  <el-form-item label="不良描述" prop="description">
                     <el-input v-model="form.description" type="textarea" disabled placeholder="请输入内容" />
                  </el-form-item>
                  <el-form-item label="处理建议" prop="auditOpinion">
                     <el-input v-model="form.auditOpinion" type="textarea" rows="5" placeholder="请输入内容" />
                  </el-form-item>
               </el-col>
            </el-row>

         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Material">
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/quality/baddata";
const { proxy } = getCurrentInstance();
const { sys_manage_state } = proxy.useDict("sys_manage_state");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading = ref(false);
const importLoading = ref(false);
const tableRowClassName = ({ row, rowIndex }) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime);
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   }
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      operationCode: [{ required: true, message: "请填写工序编号", trigger: "blur" }],
      badCode: [{ required: true, message: "请填写不良代码", trigger: "blur" }],
      badName: [{ required: true, message: "请填写不良名称", trigger: "blur" }],
      badTypeCode: [{ required: true, message: "请填写类型编码", trigger: "blur" }],
      badTypeName: [{ required: true, message: "请填写类型名称", trigger: "blur" }],
      status: [{ required: true, message: "请填写状态", trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1,
      remark: undefined
   };
   proxy.resetForm("submitRef");
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm('是否删除编号为"' + postIds + '"的数据项？').then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess("导入成功");
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess("编辑成功");
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

getList();
</script>

<style scoped>
.demo-image .block {
   padding: 30px 0;
   text-align: center;
   border-right: solid 1px var(--el-border-color);
   display: inline-block;
   width: 20%;
   box-sizing: border-box;
   vertical-align: top;
}

.demo-image .block:last-child {
   border-right: none;
}
</style>