<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('quality.badData.query.orderCode')" prop="orderCode">
            <el-input v-model="queryParams.orderCode" :placeholder="t('quality.badData.query.orderCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('quality.badData.query.scheduleCode')" prop="scheduleCode" label-width="158px">
            <el-input v-model="queryParams.scheduleCode" :placeholder="t('quality.badData.query.scheduleCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('quality.badData.query.lineCode')" prop="lineCode">
            <el-input v-model="queryParams.lineCode" :placeholder="t('quality.badData.query.lineCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('quality.badData.query.operationCode')" prop="operationCode">
            <el-input v-model="queryParams.operationCode" :placeholder="t('quality.badData.query.operationCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('quality.badData.query.snNumber')" prop="snNumber">
            <el-input v-model="queryParams.snNumber" :placeholder="t('quality.badData.query.snNumber')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('quality.badData.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('quality.badData.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column :label="t('quality.badData.tbCol.orderCode')" align="center" prop="orderCode" min-width="120px" />
         <el-table-column :label="t('quality.badData.tbCol.scheduleCode')" align="center" prop="scheduleCode" min-width="158px" />
         <el-table-column :label="t('quality.badData.tbCol.materialCode')" align="center" prop="materialCode" min-width="130px" />
         <el-table-column :label="t('quality.badData.tbCol.materialVersion')" align="center" prop="materialVersion" min-width="120px" />
         <el-table-column :label="t('quality.badData.tbCol.lineCode')" align="center" prop="lineCode" min-width="120px" />
         <el-table-column :label="t('quality.badData.tbCol.stationCode')" align="center" prop="stationCode" min-width="128px" />
         <el-table-column :label="t('quality.badData.tbCol.operationCode')" align="center" prop="operationCode" min-width="140px" />
         <el-table-column :label="t('quality.badData.tbCol.snNumber')" align="center" prop="snNumber" min-width="120px" />
         <el-table-column :label="t('quality.badData.tbCol.userId')" align="center" prop="userId" min-width="100px" />  
         <el-table-column :label="t('quality.badData.tbCol.badCode')" align="center" prop="badCode" min-width="120px" />
         <el-table-column :label="t('quality.badData.tbCol.badFactor')" align="center" prop="badFactor" min-width="120px" />
         <el-table-column :label="t('quality.badData.tbCol.badQty')" align="center" prop="badQty" min-width="150px" />
         <el-table-column :label="t('quality.badData.tbCol.editTime')" align="center" prop="editTime" width="155" />
         <!-- <el-table-column :label="t('quality.badData.tbCol.images')" align="center" prop="images" show-overflow-tooltip="true" />-->

         <el-table-column prop="orderNum" :label="t('process.rtfile.tbCol.order')" v-if="false"></el-table-column>
         <el-table-column prop="status" :label="t('process.rtfile.tbCol.status')" min-width="120px">
            <template #default="scope">
               <dict-tag :options="sys_manage_state" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('process.rtfile.tbCol.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creator')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creator')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creatTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModifierId')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.ifdelete')" prop="isDeleted" v-if="false" />

         <el-table-column :label="t('quality.badData.tbCol.description')" align="center" prop="description" :show-overflow-tooltip="true" min-width="100px" />
         <el-table-column :label="t('quality.badData.tbCol.auditOpinion')" align="center" prop="auditOpinion" :show-overflow-tooltip="true" min-width="140px" />

         <el-table-column :label="t('quality.badData.tbCol.operation')" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['quality:baddata:edit']">{{t('quality.badData.button.edit')}}</el-button>
               <!-- <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['quality:baddata:remove']">删除</el-button> -->
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="140px">
            <el-row>
               <el-col :span="12">
                  <el-form-item :label="t('quality.badData.form.snNumber')" prop="snNumber">
                     <el-input v-model="form.snNumber" :placeholder="t('quality.badData.form.snNumber')" disabled />
                  </el-form-item>
                  <el-form-item :label="t('quality.badData.form.operationCode')" prop="operationCode">
                     <el-input v-model="form.operationCode" :placeholder="t('quality.badData.form.operationCode')" disabled />
                  </el-form-item>
                  <el-form-item :label="t('quality.badData.form.userId')" prop="userId">
                     <el-input v-model="form.userId" :placeholder="t('quality.badData.form.userId')" disabled />
                  </el-form-item>
                  <el-form-item :label="t('quality.badData.form.editTime')" prop="editTime">
                     <el-input v-model="form.editTime" :placeholder="t('quality.badData.form.editTime')" disabled />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item :label="t('quality.badData.form.badCode')" prop="badCode">
                     <el-input v-model="form.badCode" :placeholder="t('quality.badData.form.badCode')" disabled />
                  </el-form-item>
                  <el-form-item :label="t('quality.badData.form.badFactor')" prop="badFactor">
                     <el-input v-model="form.badFactor" :placeholder="t('quality.badData.form.badFactor')" disabled />
                  </el-form-item>
                  <el-form-item :label="t('quality.badData.form.badQty')" prop="badQty">
                     <el-input v-model="form.badQty" :placeholder="t('quality.badData.form.badQty')" disabled />
                  </el-form-item>
                  <el-form-item :label="t('quality.badData.form.status')">
                     <el-radio-group v-model="form.status">
                        <el-radio v-for="dict in sys_manage_state" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
                     </el-radio-group>
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item :label="t('quality.badData.form.images')" prop="images">
                     <div class="demo-image block">
                        <!-- <el-image v-for="url in urls" :key="url" :src="url" lazy /> -->
                        <el-image style="width: 50px; height: 50px" :src="form.images" fit="scale-down" :preview-src-list=[form.images] lazy />
                     </div>
                  </el-form-item>
                  <el-form-item :label="t('quality.badData.form.description')" prop="description">
                     <el-input v-model="form.description" type="textarea" disabled :placeholder="t('quality.badData.form.description')" />
                  </el-form-item>
                  <el-form-item :label="t('quality.badData.form.auditOpinion')" prop="auditOpinion">
                     <el-input v-model="form.auditOpinion" type="textarea" rows="5" :placeholder="t('quality.badData.form.auditOpinion')" />
                  </el-form-item>
               </el-col>
            </el-row>

         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('quality.badData.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('quality.badData.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Material">
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/quality/baddata";
import  { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { sys_manage_state } = proxy.useDict("sys_manage_state");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading = ref(false);
const importLoading = ref(false);
const tableRowClassName = ({ row, rowIndex }) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime);
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   }
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      status: [{ required: true, message: t('quality.badData.message.alarm01'), trigger: "blur" }],
      auditOpinion: [{ required: true, message: t('quality.badData.message.alarm02'), trigger: "blur" }]

   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1,
      remark: undefined
   };
   proxy.resetForm("submitRef");
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('quality.badData.message.addTit');
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('quality.badData.message.editTit');
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('quality.badData.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('quality.badData.message.delSuc'));
   }).catch(() => { });
}

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess(t('quality.badData.message.importSuc'));
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('quality.badData.message.editSuc'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('quality.badData.message.addSuc'));
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

getList();
</script>

<style scoped>
.demo-image .block {
   padding: 30px 0;
   text-align: center;
   border-right: solid 1px var(--el-border-color);
   display: inline-block;
   width: 20%;
   box-sizing: border-box;
   vertical-align: top;
}

.demo-image .block:last-child {
   border-right: none;
}
</style>