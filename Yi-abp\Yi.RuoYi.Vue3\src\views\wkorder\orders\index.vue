<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
         <el-form-item :label="t('wkorder.wkOrder.query.status')" prop="status">
            <el-select v-model="queryParams.status" :placeholder="t('wkorder.wkOrder.query.status')" clearable @keyup.enter="handleQuery" style="width: 180px">
               <el-option v-for="dict in base_order_state" :key="JSON.parse(dict.value)" :label="dict.label" :value="JSON.parse(dict.value)" />
            </el-select>
         </el-form-item>
         <el-form-item :label="t('wkorder.wkOrder.query.orderCode')" prop="orderCode">
            <el-input v-model="queryParams.orderCode" :placeholder="t('wkorder.wkOrder.query.orderCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('wkorder.wkOrder.query.scheduleCode')" prop="scheduleCode"  label-width="160px" >
            <el-input v-model="queryParams.scheduleCode" :placeholder="t('wkorder.wkOrder.query.scheduleCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('wkorder.wkOrder.query.lineCode')" prop="lineCode">
            <el-input v-model="queryParams.lineCode" :placeholder="t('wkorder.wkOrder.query.lineCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('wkorder.wkOrder.query.materialCode')" prop="materialCode">
            <el-input v-model="queryParams.materialCode" :placeholder="t('wkorder.wkOrder.query.materialCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('wkorder.wkOrder.query.dateRangeProduct')" style="width:300px" label-width="100">
            <el-date-picker v-model="dateRangeProduct" clearable @keyup.enter="handleQuery" :unlink-panels=true value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="t('wkorder.wkOrder.query.dateRangeProductTip1')" :end-placeholder="t('wkorder.wkOrder.query.dateRangeProductTip2')"></el-date-picker>
         </el-form-item>
         <el-form-item :label="t('wkorder.wkOrder.query.dateRangeBegin')" style="width:300px" label-width="100">
            <el-date-picker v-model="dateRangeBegin" clearable @keyup.enter="handleQuery" :unlink-panels=true value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="t('wkorder.wkOrder.query.dateRangeBeginTip1')" :end-placeholder="t('wkorder.wkOrder.query.dateRangeBeginTip2')"></el-date-picker>
         </el-form-item>
         <el-form-item :label="t('wkorder.wkOrder.query.dateRangeEnd')" style="width:300px" label-width="100">
            <el-date-picker v-model="dateRangeEnd" clearable @keyup.enter="handleQuery" :unlink-panels=true value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="t('wkorder.wkOrder.query.dateRangeEndTip1')" :end-placeholder="t('wkorder.wkOrder.query.dateRangeEndTip2')"></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('wkorder.wkOrder.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('wkorder.wkOrder.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['wkorder:orders:add']">{{t('wkorder.wkOrder.button.add')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Unlock" @click="handleStart" v-hasPermi="['wkorder:orders:start']" :disabled="btnStart">{{t('wkorder.wkOrder.button.start')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Lock" @click="handleStop" v-hasPermi="['wkorder:orders:stop']" :disabled="btnStop">{{t('wkorder.wkOrder.button.stop')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Unlock" @click="handleRestore" v-hasPermi="['wkorder:orders:restore']" :disabled="btnRestore">{{t('wkorder.wkOrder.button.restore')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="danger" plain icon="RefreshLeft" @click="handleCancle" v-hasPermi="['wkorder:orders:cancle']" :disabled="btnCancle">{{t('wkorder.wkOrder.button.cancle')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="info" plain icon="Close" @click="handleClose" v-hasPermi="['wkorder:orders:close']" :disabled="btnClose">{{t('wkorder.wkOrder.button.close')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Refresh" @click="handleDwon" v-hasPermi="['wkorder:orders:down']" :disabled="btnDown" :loading="downLoading">{{t('wkorder.wkOrder.button.sync')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-upload class="upload-demo" :disabled="importDisabled" :on-change="handleImport" accept="xls,xlsx" :auto-upload="false" :multiple="true" :limit="2" :show-file-list ="false">
               <el-button type="warning" plain icon="Download" v-hasPermi="['wkorder:orders:import']" :loading="importLoading">{{t('wkorder.wkOrder.button.import')}}</el-button>
            </el-upload>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column type="selection" width="30" fixed="left" align="center" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.scheduleCode')" align="center" prop="scheduleCode" min-width="140" />
         <el-table-column prop="status" :label="t('wkorder.wkOrder.tbCol.status')">
            <template #default="scope">
               <dict-tag :options="base_order_state" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('wkorder.wkOrder.tbCol.planTime')" align="center" prop="planTime" width="154" >
            <template #default="scope">
               <el-date-picker v-model="scope.row.planTime" @change="planTimeChange(scope.row)" type="date" placeholder="Pick a day" />
            </template>
         </el-table-column>
         <el-table-column :label="t('wkorder.wkOrder.tbCol.orderNum')" align="center" prop="orderNum" width="174" >
            <template #default="scope">
               <el-input-number v-model="scope.row.orderNum" @change="orderNumChange(scope.row)" controls-position="right" class="mx-4" :min="0" :step="100"/>
            </template>
         </el-table-column>
         <el-table-column :label="t('wkorder.wkOrder.tbCol.planStartTime')" align="center" prop="planStartTime" min-width="155" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.planEndTime')" align="center" prop="planEndTime" min-width="155" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.materialCode')" align="center" prop="materialCode" min-width="120" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.materialName')" align="center" prop="materialName" min-width="360" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.lineCode')" align="center" prop="lineCode" min-width="120" />
         <!-- <el-table-column label="排程编号" align="center" prop="scheduleCode" min-width="140" /> -->
         <el-table-column prop="scheduleStatus" :label="t('wkorder.wkOrder.tbCol.scheduleStatus')" min-width="110">
            <template #default="scope">
               <dict-tag :options="base_schedule_state" :value="scope.row.scheduleStatus" />
            </template>
         </el-table-column>
         <el-table-column :label="t('wkorder.wkOrder.tbCol.scheduleQty')" align="center" prop="scheduleQty" min-width="130" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.orderCode')" align="center" prop="orderCode" min-width="140" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.orderType')" align="center" prop="orderType" />
         <el-table-column prop="orderStatus" :label="t('wkorder.wkOrder.tbCol.orderStatus')">
            <template #default="scope">
               <dict-tag :options="factory_order_state" :value="scope.row.orderStatus" />
            </template>
         </el-table-column>
         <el-table-column :label="t('wkorder.wkOrder.tbCol.orderQty')" align="center" prop="orderQty" min-width="140" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.onlineQty')" align="center" prop="onlineQty" min-width="100" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.completedQty')" align="center" prop="completedQty" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.scheduleEditTime')" align="center" prop="scheduleEditTime" min-width="140"/>         
         <el-table-column :label="t('wkorder.wkOrder.tbCol.workshopCode')" align="center" prop="workshopCode" min-width="100" /> 
         <el-table-column :label="t('wkorder.wkOrder.tbCol.materialVersion')" align="center" prop="materialVersion" />

         <el-table-column :label="t('wkorder.wkOrder.tbCol.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.creatorId')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.creatorName')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.creationTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.lastModifierId')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('wkorder.wkOrder.tbCol.ifdelete')" prop="isDeleted" v-if="false" />

         <el-table-column :label="t('wkorder.wkOrder.tbCol.operation')" align="left" fixed="right" width="200">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['wkorder:orders:edit']">{{t('wkorder.wkOrder.button.edit')}}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['wkorder:orders:remove']">{{t('wkorder.wkOrder.button.delete')}}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="1000px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="200px">
            <el-row>
               <el-col :span="12">
                  <el-form-item :label="t('wkorder.wkOrder.form.orderCode')" prop="orderCode">
                     <el-input v-model="form.orderCode" disabled :placeholder="t('wkorder.wkOrder.form.orderCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.orderType')" prop="orderType">
                     <el-input v-model="form.orderType" :placeholder="t('wkorder.wkOrder.form.orderType')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.orderQty')" prop="orderQty">
                     <el-input v-model="form.orderQty" :placeholder="t('wkorder.wkOrder.form.orderQty')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.orderStatus')" prop="orderStatus">
                   <el-select v-model="form.orderStatus">
                        <el-option v-for="dict in factory_order_state" :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
                     </el-select>
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.scheduleCode')" prop="scheduleCode">
                     <el-input v-model="form.scheduleCode" disabled :placeholder="t('wkorder.wkOrder.form.scheduleCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.scheduleQty')" prop="scheduleQty">
                     <el-input v-model="form.scheduleQty" :placeholder="t('wkorder.wkOrder.form.scheduleQty')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.onlineQty')" prop="onlineQty">
                     <el-input v-model="form.onlineQty" :placeholder="t('wkorder.wkOrder.form.onlineQty')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.completedQty')" prop="completedQty">
                     <el-input v-model="form.completedQty" :placeholder="t('wkorder.wkOrder.form.completedQty')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.scheduleStatus')" prop="scheduleStatus">
                     <el-select v-model="form.scheduleStatus">
                        <el-option v-for="dict in base_schedule_state" :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
                     </el-select>
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.scheduleEditTime')" prop="scheduleEditTime">
                     <el-date-picker v-model="form.scheduleEditTime" type="date" placeholder="Pick a day" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
                  </el-form-item>
               </el-col>

               <el-col :span="12">
                  <el-form-item :label="t('wkorder.wkOrder.form.workshopCode')" prop="workshopCode">
                     <el-input v-model="form.workshopCode" :placeholder="t('wkorder.wkOrder.form.workshopCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.lineCode')" prop="lineCode">
                     <el-input v-model="form.lineCode" :placeholder="t('wkorder.wkOrder.form.lineCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.materialCode')" prop="materialCode">
                     <el-input v-model="form.materialCode" :placeholder="t('wkorder.wkOrder.form.materialCode')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.materialName')" prop="materialName">
                     <el-input v-model="form.materialName" :placeholder="t('wkorder.wkOrder.form.materialName')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.materialVersion')" prop="materialVersion">
                     <el-input v-model="form.materialVersion" :placeholder="t('wkorder.wkOrder.form.materialVersion')" />
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.planStartTime')" prop="planStartTime">
                     <el-date-picker v-model="form.planStartTime" type="date" placeholder="Pick a day"/>
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.planEndTime')" prop="planEndTime">
                     <el-date-picker v-model="form.planEndTime" type="date" placeholder="Pick a day"/>
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.planTime')" prop="planTime">
                     <el-date-picker v-model="form.planTime" type="date" placeholder="Pick a day"/>
                  </el-form-item>
               </el-col>

               <el-col :span="24">
                  <el-form-item :label="t('wkorder.wkOrder.form.status')" prop="status">
                     <el-select v-model="form.status">
                        <el-option v-for="dict in base_order_state" :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
                     </el-select>
                  </el-form-item>
                  <el-form-item :label="t('wkorder.wkOrder.form.remark')" prop="remark">
                     <el-input v-model="form.remark" type="textarea" rows="5" :placeholder="t('wkorder.wkOrder.form.remarkTip')" />
                  </el-form-item>
               </el-col>
            </el-row>       
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('wkorder.wkOrder.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('wkorder.wkOrder.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="WkOrder">
import { listDataAsync, listData, getData, addData, updateData, delData, getDown, importAsync } from "@/api/wkorder/orders";
import * as XLSX from 'xlsx';  
import moment from 'moment';
import { genFileId } from 'element-plus';
import { UploadFilled } from '@element-plus/icons-vue';
import { ElConfigProvider } from 'element-plus';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_order_state } = proxy.useDict("base_order_state");
const { base_schedule_state } = proxy.useDict("base_schedule_state");
const { factory_order_state } = proxy.useDict("factory_order_state");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const importDisabled = ref(false);
const dateRangeProduct = ref([]);
const dateRangeBegin = ref([]); 
const dateRangeEnd= ref([]);
const btnStart = ref(true);
const btnStop = ref(true);
const btnRestore = ref(true);
const btnCancle = ref(true);
const btnClose = ref(true);
const btnDown = ref(true);
const tableData = ref([]);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}
const data = reactive({

   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   form: {},
   rules: {
      orderCode: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg01'), trigger: "blur" }],
      orderType: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg02'), trigger: "blur" }],
      orderQty: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg03'), trigger: "blur" }],
      scheduleCode: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg04'), trigger: "blur" }],
      scheduleQty: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg05'), trigger: "blur" }],
      scheduleEditTime: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg06'), trigger: "blur" }],
      workshopCode: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg07'), trigger: "blur" }],
      lineCode: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg08'), trigger: "blur" }],
      materialCode: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg09'), trigger: "blur" }],
      materialName: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg10'), trigger: "blur" }],
      materialVersion: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg11'), trigger: "blur" }],
      planStartTime: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg12'), trigger: "blur" }],
      planEndTime: [{ required: true, message: t('wkorder.wkOrder.message.alarmMsg13'), trigger: "blur" }]      
   },
   btnForm: {}
});

const { queryParams, form, rules, btnForm } = toRefs(data);



/** 查询列表 */
function getList() {
   loading.value = true;

   proxy.addDateRange(queryParams.value, dateRangeProduct.value, "productTime");
   proxy.addDateRange(queryParams.value, dateRangeBegin.value, "BeginTime");
   proxy.addDateRange(queryParams.value, dateRangeEnd.value, "EndTime");

   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置查询按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 调整生产时间 */
function planTimeChange(row) {
   const postId = row.id || ids.value;
   updateData(postId, row).then(response => {
      proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.editPlanTime'));
      getList();
   });
}

/** 调整生产顺序*/
function orderNumChange(row){
   const postId = row.id || ids.value;
   updateData(postId, row).then(response => {
      proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.editOrderNum'));
      getList();
   });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;

   btnForm.value = {};
   btnStart.value = true;
   btnStop.value = true;
   btnRestore.value = true;
   btnCancle.value = true;
   btnClose.value = true;
   btnDown.value = true;
   if (selection.length == 1) {
      btnForm.value = selection[0];
      switch (btnForm.value.status) {
         case 0:
            btnCancle.value = false;
            btnClose.value = false;
            btnDown.value = false;
            break;
         case 10:
            btnStart.value = false;
            btnCancle.value = false;
            btnClose.value = false;
            break;
         case 20:
            btnStop.value = false;
            break;
         case 70:
            btnRestore.value = false;
            btnCancle.value = false;
            break;
         case 80:
            btnRestore.value = false;
            btnClose.value = false;
            break;
         case 90:
            btnClose.value = false;
            break;
         default:
            break;
      }
   }
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.editSuc'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.addSuc'));
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 重置提交表单 */
function reset() {
   form.value = {
      id: undefined
   };
   proxy.resetForm("submitRef");
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('wkorder.wkOrder.message.addTit');
}
/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('wkorder.wkOrder.message.editTit');
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('wkorder.wkOrder.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.delSuc'));
   }).catch(() => { });
}

/** 同步按钮操作 */
function handleDwon() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.downSuc')+":"+response.data);
      }, 1500);
   });
}

/** 导入按钮操作 */
const handleImport = async (file,files) => {
   importLoading.value = true;
   importDisabled.value = true;
   if(files.length > 1){
      files.shift();
   };
   const data = await getXlsxData(file);
   tableData.value = translateField(data);

   // 数字转字符串
   tableData.value = JSON.parse(JSON.stringify(tableData.value, (key, value) => typeof value === 'number' ? String(value) : value));

   importAsync(tableData.value).then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         importLoading.value = false;
         importDisabled.value = false;
         proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.importSuc')+":"+response.data);
      }, 1500);
   });
}
//读取表格数据
const getXlsxData = async (file) => {
   const dataBinary = await readFile(file);
   const workBook = XLSX.read(dataBinary ,{
      type: "binary",
      cellDates: true
   });
   const workSheet = workBook.Sheets[workBook.SheetNames[0]];
   const data = XLSX.utils.sheet_to_json(workSheet);
   data.forEach((item) => {
      const keys = ["StartTime","EndTime"];
      for (const key in item) {
         if (moment(item[key], 'YYYY-MM-DD', true).isValid()) {
            item[key] = moment(item[key]).format("YYYY-MM-DD HH:mm:ss")
         }
      }
   });
   return data;
}
//读取excel文件
const readFile = (file) => {
   return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file.raw)
      reader.onload = (e) => {
         resolve(e.target.result)
      }
      reader.onerror = (e) => {
         reject(e)
      }
   })
}
//映射字段
const translateField = (data) => {
  const arr = []
  const cnToEn = {
      工单编号: 'orderCode',
      工单类型: 'orderType',
      工单数量: 'orderQty',
      工单状态: 'orderStatus',
      排程编号: 'scheduleCode',
      排程数量: 'scheduleQty',
      排程状态: 'scheduleStatus',
      排程时间: 'scheduleEditTime',
      工厂编号: 'factoryCode',
      车间编号: 'workshopCode',
      产线编号: 'lineCode',
      产品编号: 'productCode',
      产品名称: 'productName',
      产品版本: 'productVersion',
      计划开始时间: 'startTime',
      计划结束时间: 'endTime'
   }
   data.forEach((item) => {
      const arrItem = {}
      Object.keys(item).forEach((key) => {
         arrItem[cnToEn[key]] = item[key]
      })
      arr.push(arrItem) 
   })
   return arr
}

/** 开产按钮操作 */
function handleStart() {
   proxy.$modal.confirm(t('wkorder.wkOrder.message.statusTitle01') + ':' + btnForm.value.orderCode+ t('wkorder.wkOrder.message.statusMiddleTitle') + btnForm.value.scheduleCode + '?').then(function () {
      btnForm.value.status = 20;
      return updateData(btnForm.value.id, btnForm.value);
   }).then(() => {
      proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.editSuc'));
      getList();
   }).catch(() => {
   
   });
}

/** 暂停按钮操作 */
function handleStop() {
   proxy.$modal.confirm(t('wkorder.wkOrder.message.statusTitle02') + ':' + btnForm.value.orderCode+ t('wkorder.wkOrder.message.statusMiddleTitle') + btnForm.value.scheduleCode + '?').then(function () {
      btnForm.value.status = 70;
      return updateData(btnForm.value.id, btnForm.value);
   }).then(() => {  
      proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.editSuc'));
      getList();
   }).catch(() => {
   
   });
}

/** 复产按钮操作 */
function handleRestore() {
   proxy.$modal.confirm(t('wkorder.wkOrder.message.statusTitle03') + ':' + btnForm.value.orderCode+ t('wkorder.wkOrder.message.statusMiddleTitle') + btnForm.value.scheduleCode + '?').then(function () {
      btnForm.value.status = 20;
      return updateData(btnForm.value.id, btnForm.value);
   }).then(() => {  
      proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.editSuc'));
      getList();
   }).catch(() => {
   
   });
}
/** 取消按钮操作 */
function handleCancle() {
   proxy.$modal.confirm(t('wkorder.wkOrder.message.statusTitle04') + ':' + btnForm.value.orderCode+ t('wkorder.wkOrder.message.statusMiddleTitle') + btnForm.value.scheduleCode + '?').then(function () {
      btnForm.value.status = 80;
      return updateData(btnForm.value.id, btnForm.value);
   }).then(() => {  
      proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.editSuc'));
      getList();
   }).catch(() => {
   
   });
}
/** 关闭按钮操作 */
function handleClose() {
   proxy.$modal.confirm(t('wkorder.wkOrder.message.statusTitle05') + ':' + btnForm.value.orderCode+ t('wkorder.wkOrder.message.statusMiddleTitle') + btnForm.value.scheduleCode + '?').then(function () {
      btnForm.value.status = 100;
      return updateData(btnForm.value.id, btnForm.value);
   }).then(() => {  
      proxy.$modal.msgSuccess(t('wkorder.wkOrder.message.editSuc'));
      getList();
   }).catch(() => {
   
   });
}
getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>