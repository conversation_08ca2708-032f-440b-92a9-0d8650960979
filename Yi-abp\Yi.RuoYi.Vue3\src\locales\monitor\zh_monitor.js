export default {
    login: {
        query: {
            ipAddress: "IP地址",
            userName: "用户名称",
            onlineStatus: "在线状态",
            logTime: "登陆时间",
            startTime: "开始时间",
            endTime: "结束时间",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
            clean: "清除",
            unlock: "解锁"
        },
        tbCol: {
            userName: "用户名称",
            ipAddress: "IP地址",
            location: "在线状态",
            system: "操作系统",
            browser: "浏览器",
            msg: "描述",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            cleanMsg: "是否要清除所有数据项？",
            cleanSuc: "清洁成功？",
            unlockMsg: "您想解锁此用户的数据项吗？",
            unlockSuc: "解锁成功？",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
    operation: {
        query: {
            optTime: "操作时间",
            module: "系统模块",
            optType: "操作类型",
            optUser: "操作用户",

            startTime: "开始时间",
            endTime: "结束时间",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            export: "导出",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
            clean: "清除",
            Close: "关闭",

            details: "详情",
        },
        tbCol: {
            module: "系统模块",
            optType: "操作类型",
            requestMethod: "请求方式",
            optUser: "操作用户",
            ipAds: "IP地址",
            optTime: "操作时间",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            module: "系统模块",
            userInfo: "用户信息",
            creationTime: "创建时间",
            routePath: "路由路径",
            optData: "操作数据",
            errMsg: "异常信息",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",
            alarm11: "",
            alarm12: "",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            cleanMsg: "是否要清除所有数据项？",
            cleanSuc: "清洁成功？",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
    apiLog: {
        query: {
            serviceId: "接口实例",
            status: "状态",
            creationTime: "创建时间",

            startTime: "开始时间",
            endTime: "结束时间",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            export: "导出",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
            clean: "清除",
            Close: "关闭",
        },
        tbCol: {
            httpCount: "Http请求次数",
            UniqueCode: "唯一码",
            businessCode: "业务编码",
            businessName: "业务名称",
            appId: "AppId",
            appKey: "AppKey",
            processData: "处理数据",
            sendData: "发送数据",
            saveData: "保存数据",
            httpPath: "Http路径",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
    online: {
    }
}