<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="Sn号" prop="snNumber">
            <el-input v-model="queryParams.snNumber" placeholder="物料编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="12">
         <el-col :span="12">
            <el-text tag="b" size="large">工单编号: {{ wkOrder.orderCode}}</el-text> 
            <hr />
            <el-card shadow="always">
               <el-space alignment="normal" direction="vertical">
                  <el-text tag="b"> 工单类型: {{ wkOrder.orderType}}</el-text>
                  <el-text tag="b"> 工单数量: {{ wkOrder.orderQty}}</el-text>
                  <el-text tag="b"> 工单状态: {{ wkOrder.orderStatus}}</el-text>
                  <el-text tag="b"> 生产产线: {{ wkOrder.lineCode}}</el-text>
               </el-space>
            </el-card>
         </el-col>
         <el-col :span="12">
            <el-text tag="b" size="large">排程编号: {{ wkOrder.scheduleCode}}</el-text> 
            <hr />
            <el-card shadow="always">
               <el-space alignment="normal" direction="vertical">
                  <el-text tag="b"> 排程数量: {{ wkOrder.scheduleQty}}</el-text>
                  <el-text tag="b"> 排程状态: {{ wkOrder.scheduleStatus}}</el-text>
                  <el-text tag="b"> 计划开始时间: {{ wkOrder.planStartTime}}</el-text>
                  <el-text tag="b"> 计划结束时间: {{ wkOrder.planEndTime}}</el-text>
               </el-space>
            </el-card>
         </el-col>
      </el-row>

      <el-tabs v-model="activeName" @tab-click="handleClick">
         <el-tab-pane label="过站记录">
            <el-timeline>
               <el-timeline-item v-for="(pass, index) in passStation" :key="index" :timestamp="pass.creationTime" placement="top">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b">工位编号: {{ pass.stationCode}}</el-text> 
                        <hr />
                        <el-text> 进站时间: {{ pass.passBeginTime}}</el-text>
                        <el-text> 出站时间: {{ pass.passEndTime}}</el-text>
                        <el-text> 操作员: {{ pass.creatorName}}</el-text>
                     </el-space>
                  </el-card>
               </el-timeline-item>
            </el-timeline>
         </el-tab-pane>

         <el-tab-pane label="物料绑定">
            <el-row :gutter="12" wrap>
               <el-col :span="6" v-for="(bindm, index) in bindMaterial" :key="index" style="margin-top: 10px">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b">工序编号: {{ bindm.operationCode}}</el-text>
                        <el-text tag="b">操作员: {{ bindm.creatorName}}</el-text>
                        <el-space alignment="normal" direction="vertical" v-for="(subm, idex) in bindm.subBinds" :key="idex">
                           <hr />
                           <el-text>物料编号: {{ subm.assemblyMaterialCode}}</el-text>
                           <el-text>装配时间: {{ subm.assemblyTime}}</el-text>
                           <el-text>物料Sn号: {{ subm.assemblyMaterialSn}}</el-text>
                        </el-space>                 
                     </el-space>
                  </el-card>
               </el-col>
            </el-row>
         </el-tab-pane>

         <el-tab-pane label="生产参数">
            <el-row :gutter="12" style="flex-wrap: nowrap; overflow-x: auto;">
               <el-col :span="6" v-for="(ope, index) in wkParam" :key="index">
                        <el-card shadow="hover">
                           <el-space alignment="normal" direction="vertical">
                              <el-text tag="b"> 工序编号: {{ope.operationCode}}</el-text>
                              <el-text tag="b"> 执行时间: {{ope.operationCode}}</el-text>
                              <el-text tag="b"> 操作员: {{ope.operationCode}}</el-text>
                              <el-space alignment="normal" direction="vertical" v-for="(subp, idex) in ope.subParams" :key="idex">
                                 <hr />
                                 <el-text> 参数编号: {{subp.paramCode}} </el-text>
                                 <el-text> 上限: {{subp.standardRange1}} </el-text>
                                 <el-text> 下限: {{subp.standardRange2}} </el-text>
                                 <el-text> 标准值: {{subp.standardValue}} </el-text>
                                 <el-text> 目标值: {{subp.targetValue}} </el-text>
                                 <el-text> 实测值: {{subp.realValue}} </el-text>
                                 <el-text> 判断结果: {{subp.checkResult}} </el-text>
                              </el-space>   
                           </el-space>   
                        </el-card>
               </el-col>
            </el-row>
         </el-tab-pane>

         <el-tab-pane label="安灯记录">
            <el-timeline>
               <el-timeline-item v-for="(ad, index) in adData" :key="index" :timestamp="ad.creationTime" placement="top">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b">工位编号: {{ ad.stationCode }}</el-text> 
                        <hr />
                        <el-text> 预警编码: {{ ad.unusualAlarmCode }} </el-text>
                        <el-text> 处理时间: {{ ad.lastModificationTime }} </el-text>
                        <el-text> 处理意见: {{ ad.remark }} </el-text>
                        <el-text> 处理人: {{ ad.lastModifierName }} </el-text>
                     </el-space>
                  </el-card>
               </el-timeline-item>
            </el-timeline>
         </el-tab-pane>
         
         <el-tab-pane label="不良记录">
            <el-timeline>
               <el-timeline-item v-for="(bd, index) in badData" :key="index" :timestamp="bd.creationTime" placement="top">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b">工序编码: {{ bd.operationCode }}</el-text> 
                        <hr />
                        <el-text> 不良代码: {{ bd.badCode }} </el-text>
                        <el-text> 不良因素: {{ bd.badFactor }} </el-text>
                        <el-text> 不良描述: {{ bd.description }} </el-text>
                        <!-- <el-text> 不良图片: {{ bd.images }} </el-text> -->
                        <div class="demo-image block">
                        <!-- <el-image v-for="url in urls" :key="url" :src="url" lazy /> -->
                           <el-image style="height: 50px" :src="bd.images" fit="scale-down" :preview-src-list=[bd.images] lazy />
                        </div>
                        <el-text> 处理时间: {{ bd.lastModificationTime }} </el-text>
                        <el-text> 处理人: {{ bd.lastModifierName }} </el-text>
                        <el-text> 处理意见: {{ bd.auditOpinion }} </el-text>
                     </el-space>
                  </el-card>
               </el-timeline-item>
            </el-timeline>
         </el-tab-pane>
      </el-tabs>
   </div>
</template>

<script setup name="ProductRetrace">
import { listDataAsync } from "@/api/quality/productretrace";

const loading = ref(true);
const showSearch = ref(true);
const wkOrder = ref({});
const wkQueue = ref({});
const wkParam = ref([]);
const passStation = ref([]);
const bindMaterial = ref([]);
const adData = ref([]);
const badData = ref([]);

const data = reactive({
   queryParams: {
   }
});

const { queryParams } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      if(response.data.wkOrder != null)
      {
         wkOrder.value = response.data.wkOrder;
      }
      if(response.data.wkQueue != null)
      {
         wkQueue.value = response.data.wkQueue;
      }
      if(response.data.wkParam != null)
      {
         wkParam.value = response.data.wkParam;
      }
      if(response.data.passStation != null)
      {
         passStation.value = response.data.passStation;
      }
      if(response.data.bindMaterial != null)
      {
         bindMaterial.value = response.data.bindMaterial;
      }
      if(response.data.adData != null)
      {
         adData.value = response.data.adData;
      }
      if(response.data.badData != null)
      {
         badData.value = response.data.badData;
      }
      loading.value = false;
   });
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

getList();
</script>

<style scoped>
.demo-image .block {
   padding: 30px 0;
   text-align: center;
   border-right: solid 1px var(--el-border-color);
   display: inline-block;
   width: 20%;
   box-sizing: border-box;
   vertical-align: top;
}

.demo-image .block:last-child {
   border-right: none;
}
</style>