<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('quality.retrace.query.snNumber')" prop="snNumber">
            <el-input v-model="queryParams.snNumber" :placeholder="t('quality.retrace.query.snNumber')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('quality.retrace.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('quality.retrace.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="12">
         <el-col :span="12">
            <el-text tag="b" size="large">{{t('quality.retrace.tbCol.orderCode')}}: {{ wkOrder.orderCode}}</el-text> 
            <hr />
            <el-card shadow="always">
               <el-space alignment="normal" direction="vertical">
                  <el-text tag="b">{{t('quality.retrace.tbCol.orderType')}}: {{ wkOrder.orderType}}</el-text>
                  <el-text tag="b">{{t('quality.retrace.tbCol.orderQty')}}: {{ wkOrder.orderQty}}</el-text>
                  <el-text tag="b">{{t('quality.retrace.tbCol.orderStatus')}}: {{ wkOrder.orderStatus}}</el-text>
                  <el-text tag="b">{{t('quality.retrace.tbCol.lineCode')}}: {{ wkOrder.lineCode}}</el-text>
               </el-space>
            </el-card>
         </el-col>
         <el-col :span="12">
            <el-text tag="b" size="large">{{t('quality.retrace.tbCol.scheduleCode')}}: {{ wkOrder.scheduleCode}}</el-text> 
            <hr />
            <el-card shadow="always">
               <el-space alignment="normal" direction="vertical">
                  <el-text tag="b"> {{t('quality.retrace.tbCol.scheduleQty')}}: {{ wkOrder.scheduleQty}}</el-text>
                  <el-text tag="b"> {{t('quality.retrace.tbCol.scheduleStatus')}}: {{ wkOrder.scheduleStatus}}</el-text>
                  <el-text tag="b"> {{t('quality.retrace.tbCol.planStartTime')}}: {{ wkOrder.planStartTime}}</el-text>
                  <el-text tag="b"> {{t('quality.retrace.tbCol.planEndTime')}}: {{ wkOrder.planEndTime}}</el-text>
               </el-space>
            </el-card>
         </el-col>
      </el-row>

      <!-- <el-tabs v-model="activeName" @tab-click="handleClick"> -->
      <el-tabs>
         <el-tab-pane :label="t('quality.retrace.tbCol.psStation')">
            <el-timeline>
               <el-timeline-item v-for="(pass, index) in passStation" :key="index" :timestamp="pass.creationTime" placement="top">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b">{{t('quality.retrace.tbCol.stationCode')}}: {{ pass.stationCode}}</el-text> 
                        <hr />
                        <el-text>{{t('quality.retrace.tbCol.creatorName')}}: {{ pass.creatorName}}</el-text>
                        <el-text>{{t('quality.retrace.tbCol.passBeginTime')}}: {{ pass.passBeginTime}}</el-text>
                        <el-text>{{t('quality.retrace.tbCol.passEndTime')}}: {{ pass.passEndTime}}</el-text>
                     </el-space>
                  </el-card>
               </el-timeline-item>
            </el-timeline>
         </el-tab-pane>

         <el-tab-pane :label="t('quality.retrace.tbCol.mBind')">
            <el-row :gutter="12" wrap>
               <el-col :span="6" v-for="(bindm, index) in bindMaterial" :key="index" style="margin-top: 10px">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b">{{t('quality.retrace.tbCol.operationCode')}}: {{ bindm.operationCode}}</el-text>
                        <el-text tag="b">{{t('quality.retrace.tbCol.creatorName')}}: {{ bindm.creatorName}}</el-text>
                        <el-space alignment="normal" direction="vertical" v-for="(subm, idex) in bindm.subBinds" :key="idex">
                           <hr />
                           <el-text>{{t('quality.retrace.tbCol.assemblyMaterialCode')}}: {{ subm.assemblyMaterialCode}}</el-text>
                           <el-text>{{t('quality.retrace.tbCol.assemblyTime')}}: {{ subm.assemblyTime}}</el-text>
                           <el-text>{{t('quality.retrace.tbCol.assemblyMaterialSn')}}: {{ subm.assemblyMaterialSn}}</el-text>
                        </el-space>                 
                     </el-space>
                  </el-card>
               </el-col>
            </el-row>
         </el-tab-pane>

         <el-tab-pane :label="t('quality.retrace.tbCol.wkParams')">
            <el-row :gutter="12" style="flex-wrap: nowrap; overflow-x: auto;">
               <el-col :span="6" v-for="(ope, index) in wkParam" :key="index">
                        <el-card shadow="hover">
                           <el-space alignment="normal" direction="vertical">
                              <el-text tag="b">{{t('quality.retrace.tbCol.operationCode')}}: {{ope.operationCode}}</el-text>
                              <el-text tag="b">{{t('quality.retrace.tbCol.creationTime')}}: {{ope.creationTime}}</el-text>
                              <el-text tag="b">{{t('quality.retrace.tbCol.creatorName')}}: {{ope.creatorName}}</el-text>
                              <el-space alignment="normal" direction="vertical" v-for="(subp, idex) in ope.subParams" :key="idex">
                                 <hr />
                                 <el-text>{{t('quality.retrace.tbCol.paramCode')}}: {{subp.paramCode}} </el-text>
                                 <el-text>{{t('quality.retrace.tbCol.standardRange1')}}: {{subp.standardRange1}} </el-text>
                                 <el-text>{{t('quality.retrace.tbCol.standardRange2')}}: {{subp.standardRange2}} </el-text>
                                 <el-text>{{t('quality.retrace.tbCol.standardValue')}}: {{subp.standardValue}} </el-text>
                                 <el-text>{{t('quality.retrace.tbCol.targetValue')}}: {{subp.targetValue}} </el-text>
                                 <el-text>{{t('quality.retrace.tbCol.realValue')}}: {{subp.realValue}} </el-text>
                                 <el-text>{{t('quality.retrace.tbCol.checkResult')}}: {{subp.checkResult}} </el-text>
                              </el-space>   
                           </el-space>   
                        </el-card>
               </el-col>
            </el-row>
         </el-tab-pane>

         <el-tab-pane :label="t('quality.retrace.tbCol.andon')">
            <el-timeline>
               <el-timeline-item v-for="(ad, index) in adData" :key="index" :timestamp="ad.creationTime" placement="top">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b">{{t('quality.retrace.tbCol.stationCode')}}: {{ ad.stationCode }}</el-text> 
                        <hr />
                        <el-text>{{t('quality.retrace.tbCol.unusualAlarmCode')}}: {{ ad.unusualAlarmCode }} </el-text>
                        <el-text>{{t('quality.retrace.tbCol.lastModificationTime')}}: {{ ad.lastModificationTime }} </el-text>
                        <el-text>{{t('quality.retrace.tbCol.remark')}}: {{ ad.remark }} </el-text>
                        <el-text>{{t('quality.retrace.tbCol.lastModifierName')}}: {{ ad.lastModifierName }} </el-text>
                     </el-space>
                  </el-card>
               </el-timeline-item>
            </el-timeline>
         </el-tab-pane>
         
         <el-tab-pane :label="t('quality.retrace.tbCol.badData')">
            <el-timeline>
               <el-timeline-item v-for="(bd, index) in badData" :key="index" :timestamp="bd.creationTime" placement="top">
                  <el-card shadow="hover">
                     <el-space alignment="normal" direction="vertical">
                        <el-text tag="b">{{t('quality.retrace.tbCol.operationCode')}}: {{ bd.operationCode }}</el-text> 
                        <hr />
                        <el-text>{{t('quality.retrace.tbCol.badCode')}}: {{ bd.badCode }} </el-text>
                        <el-text>{{t('quality.retrace.tbCol.badFactor')}}: {{ bd.badFactor }} </el-text>
                        <el-text>{{t('quality.retrace.tbCol.description')}}: {{ bd.description }} </el-text>
                        <!-- <el-text> 不良图片: {{ bd.images }} </el-text> -->
                        <div class="demo-image block">
                        <!-- <el-image v-for="url in urls" :key="url" :src="url" lazy /> -->
                           <el-image style="height: 50px" :src="bd.images" fit="scale-down" :preview-src-list=[bd.images] lazy />
                        </div>
                        <el-text>{{t('quality.retrace.tbCol.lastModificationTime')}}: {{ bd.lastModificationTime }} </el-text>
                        <el-text>{{t('quality.retrace.tbCol.lastModifierName')}}: {{ bd.lastModifierName }} </el-text>
                        <el-text>{{t('quality.retrace.tbCol.auditOpinion')}}: {{ bd.auditOpinion }} </el-text>
                     </el-space>
                  </el-card>
               </el-timeline-item>
            </el-timeline>
         </el-tab-pane>
      </el-tabs>
   </div>
</template>

<script setup name="ProductRetrace">
import { listDataAsync } from "@/api/quality/productretrace";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const loading = ref(true);
const showSearch = ref(true);
const wkOrder = ref({});
const wkQueue = ref({});
const wkParam = ref([]);
const passStation = ref([]);
const bindMaterial = ref([]);
const adData = ref([]);
const badData = ref([]);

const data = reactive({
   queryParams: {
   }
});

const { queryParams } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      if(response.data.wkOrder != null)
      {
         wkOrder.value = response.data.wkOrder;
      }
      if(response.data.wkQueue != null)
      {
         wkQueue.value = response.data.wkQueue;
      }
      if(response.data.wkParam != null)
      {
         wkParam.value = response.data.wkParam;
      }
      if(response.data.passStation != null)
      {
         passStation.value = response.data.passStation;
      }
      if(response.data.bindMaterial != null)
      {
         bindMaterial.value = response.data.bindMaterial;
      }
      if(response.data.adData != null)
      {
         adData.value = response.data.adData;
      }
      if(response.data.badData != null)
      {
         badData.value = response.data.badData;
      }
      loading.value = false;
   });
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

getList();
</script>

<style scoped>
.demo-image .block {
   padding: 30px 0;
   text-align: center;
   border-right: solid 1px var(--el-border-color);
   display: inline-block;
   width: 20%;
   box-sizing: border-box;
   vertical-align: top;
}

.demo-image .block:last-child {
   border-right: none;
}
</style>