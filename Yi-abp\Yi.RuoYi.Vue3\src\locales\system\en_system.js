export default {
    user: {
        query: {
            deptName: "DeptName",
            userName: "UserName",
            phone: "Phone",
            userState: "UserState",
            creationTime: "CreationTime",

            startTime: "StartTime",
            endTime: "EndTime",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
            resetPwd: "ResetPassword",
        },
        tbCol: {
            userId: "UserId",
            userName: "UserName",
            nickName: "NickName",
            gender: "Gender",
            deptName: "DeptName",
            phone: "Phone",
            userState: "UserState",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            nickName: "NickName",
            deptName: "DeptName",
            phone: "Phone",
            e_mail: "E-mail",
            userName: "UserName",
            password: "Password",
            gender: "Gender",
            userState: "UserState",
            posts: "Post",
            roles: "Role",
            creationTime: "CreationTime",
            plsChoice: "Please Choice",
            status: "Status",
            remark: "Remark",
            remarkTip: "Please fill in contents",

            dragFile: "Drag the file here, or",
            clickUpload: "Click Upload",
            onlyAllowImport: "Only allow import xls、xlsx format files",
            downloadTemplate: "Download Template",
        },
        message: {
            alarm01: "User name cannot be empty",
            alarm02: "User name length must be between 2 and 20",
            alarm03: "User nickname cannot be empty",
            alarm04: "Please enter the correct email address",
            alarm05: "Please enter your phone number",

            enable: "Enable",
            disable: "Disable",
            useTips1: "Are you sure to ",
            useTips2: " users?",
            useTips3: "Success",

            pwdTips1: "Please enter the new password:",
            pwdTips2: "Prompt",
            pwdTips3: "OK",
            pwdTips4: "Cancel",
            pwdTips5: "User password length must be between 5 and 20",
            pwdTips6: "Edited successfully, new password is:",

            importTips: "User Import",
            importTips1: "Import Results",

            addTit: "Add User",
            addSuc: "Add Success",
            editTit: "Edit User",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    authRole: {
        basicInfo: "BasicInfo",
        nickName: "NickName",
        userName: "UserAccount",
        roleInfo: "RoleInfo",
        roleId: "RoleId",
        roleName: "RoleName",
        roleKey: "RoleKey",
        createTime: "CreationTime",
        submit: "Submit",
        goBack: "GoBack",
        grantAccess: "GrantAccess",
    },
    role: {
        query: {
            roleName: "RoleName",
            roleCode: "RoleCode",
            state: "RoleState",
            creationTime: "CreationTime",
            startTime: "StartTime",
            endTime: "EndTime",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",

            dataAuth: "DataAuth",
            userAuth: "UserAuth",
        },
        tbCol: {
            roleCode: "RoleCode",
            roleName: "RoleName",
            roleKey: "RoleKey",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            roleName: "RoleName",
            roleCode: "RoleCode",
            roleKey: "RoleKey",
            roleCodeTip: "The role key defined in the controller, such as",
            roleCodeTip1: "RoleKey",
            roleCodeTip2: "Please fill in RoleKey",
            menuAuth: "RoleAuth",
            menuAuth1: "Toggle/Expand",
            menuAuth2: "All/Cancle All",
            menuAuth3: "Parent/Child",
            menuAuth4: "Loading...",
            authScope: "Authorization Scope",
            dataAuth: "DataAuth",
            dataAuth1: "Toggle/Expand",
            dataAuth2: "All/Cancle All",
            dataAuth3: "Parent/Child",
            dataAuth4: "Loading...",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            remarkTip: "Please fill in contents",
        },
        message: {
            alarm01: "all data authorization",
            alarm02: "customed data authorization",
            alarm03: "department data authorization",
            alarm04: "department and below data authorization",
            alarm05: "current user authorization",
            alarm06: "The RoleName can not be empty",
            alarm07: "The RoleKey can not be empty",
            alarm08: "The OrderNum cannot be empty",
            alarm09: "",
            alarm10: "",
            alarm11: "",
            alarm12: "",

            enabled: "enabled",
            disabled: "disabled",
            useTips1: "Are you sure to ",
            useTips2: " this roles?",
            useTips3: "Success",
            dataAuth: "Data authorization",

            addTit: "Add Role",
            addSuc: "Add Success",
            editTit: "Edit Role",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    authUser: {
        userName: "UserName",
        nickName: "NickName",
        email: "Email",
        phone: "Phone",
        status: "State",
        creationTime: "CreationTime",

        search: "Search",
        reset: "Reset",
        add: "Add",
        cancleAuth: "CancleAuth",
        close: "Close",

        cancleTip: "Do you want to cancle selected auth?",
        cancleAcc: "CancleAuth Success",
        operation: "Operation",
    },
    sltUser: {
        choice: "ChoiceUser",
        userName: "UserName",
        phone: "Phone",
        nickName: "NickName",
        email: "Email",
        status: "State",
        creationTime: "CreationTime",
        search: "Search",
        reset: "Reset",
        confirm: "Confirm",
        cancle: "Cancle",
        choiceUser: "Please Choice User",
        success: "Success",
    },
    menu: {
        query: {
            menuName: "MenuName",
            status: "Status",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",

            toggleExpand: "Toggle/Expand",
        },
        tbCol: {
            menuName: "MenuName",
            engName: "EnglishName",
            ptName: "PortugueseName",
            menuIcon: "MenuIcon",
            permissionCode: "PermissionCode",
            router: "Router",
            component: "Component",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            lastMenu: "LastMenu",
            menuType: "MenuType",
            catalogue: "Catalogue",
            menu: "Menu",
            button: "Button",
            menuIcon: "MenuIcon",
            choiceIcon: "ChoiceIcon",
            menuName: "MenuName",
            engName: "EnglishName",
            ptName: "PortugueseName",
            orderNum: "OrderNum",
            outLink: "OutLink",
            linkTip: "Please fill in the router address, such as `http(s)://`",
            yes: "Yes",
            no: "No",
            router: "RoutePath",
            routerContent: "The routing path,Such as:`user`,If the external network address requires internal link access, start with 'http'",
            component: "Component Path",
            componentContent: "The routing path,Such as:'system/user/index',Default directory 'views'",
            permission: "Permission Bytes",
            permissionContent: "The permission bytes defined in the controller, such as @PreAuthorize(`@ss.hasPermit('system:user:list')  `)",
            routeParam: "RouteParam",
            paramContent: "Accessing the default passed parameters for RouteParams, such as:{\"id\": 1, \"name\": \"ry\"}`",
            cache: "IsCache",
            cacheYes: "Cached",
            cacheNo: "NoCached",
            cacheContent: "It will be cached by 'keep-alive' after choose 'yes', Need Match the 'name' and 'address' of the component to ensure consistency",
            show: "ShowState",
            showContent: "The router will not appear in the sidebar after choose 'hide', but it will still be accessible",
            menuState: "MenuState",
            menuStateContent: "The router will not appear in the sidebar after choose 'disable', and it will not be accessible",

            status: "Status",
            remark: "Remark",
            remarkTip: "Please fill in contents",
        },
        message: {
            alarm01: "MenuName cannot be empty",
            alarm02: "EnglishName cannot be empty",
            alarm03: "PortugueseName cannot be empty",
            alarm04: "The menu order cannot be empty",
            alarm05: "The routing path cannot be empty",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            mainClass: "Main category",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    httpOption: {
        query: {
            serviceId: "Service Instance",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",

            toggleExpand: "Toggle/Expand",
        },
        tbCol: {
            name: "Name",
            appId: "AppId",
            appKey: "AppKey",
            facCode: "FactoryCode",
            serviceId: "ServiceId",
            httpUrl: "HttpUrl",
            httpRoute: "HttpRoute",
            updateTime: "UpdateTime",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "ModifierId",
            lastModifierName: "ModifierName",
            lastModificationTime: "ModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            name: "Name",
            appId: "AppId",
            appKey: "AppKey",
            facCode: "FactoryCode",
            serviceId: "ServiceId",
            httpUrl: "HttpUrl",
            httpRoute: "HttpRoute",
            updateTime: "UpdateTime",

            status: "Status",
            remark: "Remark",
            remarkTip: "Please fill in contents",
        },
        message: {
            alarm01: "Please fill in the name",
            alarm02: "Please fill in the AppId",
            alarm03: "Please fill in the AppKey",
            alarm04: "Please fill in the factory code",
            alarm05: "Please fill in the service instance",
            alarm06: "Please fill in the url address",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
}