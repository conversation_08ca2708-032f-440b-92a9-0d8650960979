import request from '@/utils/request'
import { FailedToStartTransportError } from '@microsoft/signalr/dist/esm/Errors'


export function listDataAsync(query) {
    return request({
        url: '/route-files/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/route-files',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/route-files/' + id,
        method: 'get'
    })
}

// 删除
export function delData(id) {
    return request({
        url: `/route-files/${id}`,
        method: 'delete',
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/route-files',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(data) {
    return request({
        url: `/route-files/${data.id}`,
        method: 'put',
        data: data
    })
}

// 同步数据
export function getDown(orderCode) {
    return request({
        url: '/route-files/getDown?orderCode=' + orderCode,
        method: 'get'
    })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/route-files/' + roleId,
        method: 'get'
    })
}