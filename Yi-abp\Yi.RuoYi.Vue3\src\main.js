import { createApp } from 'vue'

import Cookies from 'js-cookie'

// Element_Plus
import ElementPlus from 'element-plus'
import lang_zh from "element-plus/dist/locale/zh-cn.mjs"; // 中文语言
import lang_en from "element-plus/dist/locale/en.mjs"; // 英文语言
import lang_pt from "element-plus/dist/locale/pt.mjs"; // 葡萄牙语

// 全局CSS
import '@/assets/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'
import directive from './directive' // directive
import VForm3 from 'vform3-builds'
import 'vform3-builds/dist/designer.style.css'  //引入VForm3样式

// i18n
import i18n from './locales/index'

// 注册指令
import plugins from './plugins' // plugins
import { download } from '@/utils/ruoyi.js'

// svg图标
import 'virtual:svg-icons-register'
import SvgIcon from '@/components/SvgIcon'
import elementIcons from '@/components/SvgIcon/svgicon'

import './permission' // permission control

import { useDict } from '@/utils/dict'
import { parseTime, resetForm, addDateRange, handleTree, selectDictLabel, selectDictLabels } from '@/utils/ruoyi'

// 分页组件
import Pagination from '@/components/Pagination'
// 自定义表格工具组件
import RightToolbar from '@/components/RightToolbar'
// 文件上传组件
import FileUpload from "@/components/FileUpload"
// 图片上传组件
import ImageUpload from "@/components/ImageUpload"
// 图片预览组件
import ImagePreview from "@/components/ImagePreview"
// 自定义树选择组件
import TreeSelect from '@/components/TreeSelect'
// 字典标签组件
import DictTag from '@/components/DictTag'

const app = createApp(App)

// 全局方法挂载
app.config.globalProperties.useDict = useDict
app.config.globalProperties.download = download
app.config.globalProperties.parseTime = parseTime
app.config.globalProperties.resetForm = resetForm
app.config.globalProperties.handleTree = handleTree
app.config.globalProperties.addDateRange = addDateRange
app.config.globalProperties.selectDictLabel = selectDictLabel
app.config.globalProperties.selectDictLabels = selectDictLabels

// 全局组件挂载
app.component('DictTag', DictTag)
app.component('Pagination', Pagination)
app.component('TreeSelect', TreeSelect)
app.component('FileUpload', FileUpload)
app.component('ImageUpload', ImageUpload)
app.component('ImagePreview', ImagePreview)
app.component('RightToolbar', RightToolbar)
app.component('svg-icon', SvgIcon)

// 注册组件
app.use(i18n)
app.use(router)
app.use(store)
app.use(plugins)
app.use(elementIcons)
app.use(ElementPlus, {
  locale: Cookies.get('language') == "zh" ? lang_zh : Cookies.get('language') == "en" ? lang_en : lang_pt || lang_zh, // 设置Element-Plus组件语言
  size: Cookies.get('size') || 'default'   // 设置全局的大小 支持 large、default、small
})
app.use(VForm3)

// 全局注册自定义指令
directive(app)

app.mount('#app')

