export default {
    // 安灯记录
    record: {
        query: {
            lineCode: "LineCode",
            andonType: "AndonType",
            andonStatus: "AndonStatus",
            dateRange: "SearchRange",
            startTime: "StartTime",
            endTime: "SndTime",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        tbCol: {
            workShopCode: "WorkShopCode",
            lineCode: "LineCode",
            stationCode: "StationCode",
            machineCode: "MachineCode",
            snNumber: "SnCode",
            andonType: "AndonType",
            andonLevel: "AlarmLevel",
            unusualAlarmCode: "UnusualAlarmCode",
            andonStatus: "AndonStatus",
            alarmMessage: "AlarmMessage",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            workShopCode: "WorkShopCode",
            lineCode: "LineCode",
            stationCode: "StationCode",
            machineCode: "MachineCode",
            snNumber: "SnCode",
            andonType: "AndonType",
            andonLevel: "AlarmLevel",
            unusualAlarmCode: "UnusualAlarmCode",
            andonStatus: "AndonStatus",
            alarmMessage: "AlarmMessage",

            status: "Status",
            remark: "Remark",
            remarkTip: "Remark",
        },
        message: {
            alarm01: "Please confirm to perform this operation?",
            alarm02: "Do you want to delete this record?",
            alarm03: "Do you want to edit this record?",
            alarm04: "Please fill in the bad type code",
            alarm05: "Please fill in the bad type name",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    // 未处理安灯记录
    untreated: {
        query: {
            lineCode: "LineCode",
            andonType: "AndonType",
            andonStatus: "AndonStatus",
            dateRange: "SearchRange",
            startTime: "StartTime",
            endTime: "SndTime",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        tbCol: {
            workShopCode: "WorkShopCode",
            lineCode: "LineCode",
            stationCode: "StationCode",
            machineCode: "MachineCode",
            snNumber: "SnCode",
            andonType: "AndonType",
            andonLevel: "AlarmLevel",
            unusualAlarmCode: "UnusualAlarmCode",
            andonStatus: "AndonStatus",
            alarmMessage: "AlarmMessage",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            workShopCode: "WorkShopCode",
            lineCode: "LineCode",
            stationCode: "StationCode",
            machineCode: "MachineCode",
            snNumber: "SnCode",
            andonType: "AndonType",
            andonLevel: "AlarmLevel",
            unusualAlarmCode: "UnusualAlarmCode",
            andonStatus: "AndonStatus",
            alarmMessage: "AlarmMessage",

            status: "Status",
            remark: "Remark",
            remarkTip: "Remark",
        },
        message: {
            alarm01: "Please select the type of Andon",
            alarm02: "Please select the state of Andon",
            alarm03: "Please fill in the warning level",
            alarm04: "",
            alarm05: "",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
}