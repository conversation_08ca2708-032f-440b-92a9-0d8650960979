export default {
    // 安灯记录
    record: {
        queryField: {
            lineCode: "LineCode",
            andonType: "AndonType",
            andonStatus: "AndonStatus",
            dateRange: "SearchRange",
            startTime: "StartTime",
            endTime: "SndTime",
        },
        tbColumn: {
            Id: "Id",
            workShopCode: "WorkShopCode",
            lineCode: "LineCode",
            stationCode: "StationCode",
            machineCode: "MachineCode",
            andonType: "AndonType",
            andonLevel: "AlarmLevel",
            unusualAlarmCode: "UnusualAlarmCode",
            andonStatus: "AndonStatus",
            alarmMessage: "AlarmMessage",
            remark: "AuditMessage",
            creationTime: "CreationTime",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime"
        },
        button: {
            searchBtn: "Search",
            resetBtn: "Reset",

            importBtn: "Import",
            exportBtn: "Export",

            addBtn: "Add",
            removeBtn: "Delete",
            editBtn: "Edit",

            confirmBtn: "Confirm",
            cancelBtn: "Cancel",
        },
        message: {
            confirmMsg: "Please confirm to perform this operation?",
            deleteMsg: "Do you want to delete this record?",
            editMsg: "Do you want to edit this record?",
        }
    },
    // 未处理安灯记录
    untreated: {
    }
}