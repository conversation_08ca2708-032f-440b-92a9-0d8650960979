<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="接口实例" prop="serviceId">
            <el-select v-model="queryParams.serviceId" placeholder="接口实例" clearable style="width: 160px">
               <el-option v-for="dict in base_Interface_list" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
         </el-form-item>
         <el-form-item label="接口状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="接口状态" clearable style="width: 160px">
               <el-option v-for="dict in base_interface_state" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
         </el-form-item>
         <el-form-item label="触发时间" style="width: 308px">
            <el-date-picker v-model="dateRange" :unlink-panels=true value-format="YYYY-MM-DD" type="daterange" range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column label="请求次数" align="center" prop="httpCount" width="80"/>
         <el-table-column label="请求标识" align="center" prop="uniqueCode" width="165" />
         <el-table-column label="接口编号" align="center" prop="businessCode" width="155"/>
         <el-table-column label="接口名称" align="center" prop="businessName" width="155"/>

         <el-table-column label="AppId" align="center" prop="appId" width="120" />
         <el-table-column label="AppKey" align="center" prop="appKey" width="120" />
         <el-table-column label="过程数据" align="center" type="expand" width="80" >
            <template #default="props" >
               <div m="4">
                  <h2>请求数据:</h2>
                  <p m="t-0 b-2"> {{ props.row.sendData }}</p>
                  <hr />
                  <h2>接收数据:</h2>
                  <p m="t-0 b-2"> {{ props.row.saveData }}</p>
               </div>
            </template>
         </el-table-column>
         <el-table-column label="请求地址" align="center" prop="httpPath" />

         <el-table-column prop="orderNum" label="排序" v-if="false"></el-table-column>
         <el-table-column prop="status" label="状态" width="70" fixed="left">
            <template #default="scope">
               <dict-tag :options="base_interface_state" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="备注" prop="remark" v-if="false" />
         <el-table-column label="请求时间" prop="creationTime" width="155" />
         <el-table-column label="最后处理时间" prop="lastModificationTime" width="155"/>
      </el-table>
      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />
   </div>
</template>

<script setup name="Material">
import { listDataAsync, listData, getData, delData, addData, updateData } from "@/api/monitor/interface";
const { proxy } = getCurrentInstance();
const { base_interface_state } = proxy.useDict("base_interface_state"); 
const { base_Interface_list } = proxy.useDict("base_Interface_list"); 

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const dateRange = ref([]);
const ids = ref([]);

const data = reactive({
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   }
});

const { queryParams } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

getList();
</script>