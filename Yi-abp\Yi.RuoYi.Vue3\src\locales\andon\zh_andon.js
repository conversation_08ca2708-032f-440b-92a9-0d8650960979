export default {
    // 安灯记录
    record: {
        queryField: {
            lineCode: "产线编号",
            andonType: "安灯类型",
            andonStatus: "安灯状态",
            dateRange: "搜索时间",
            startTime: "开始时间",
            endTime: "结束时间",
        },
        tbColumn: {
            Id: "编号",
            workShopCode: "车间编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            machineCode: "机台编号",
            andonType: "安灯类型",
            andonLevel: "异常等级",
            unusualAlarmCode: "异常预警时间编码",
            andonStatus: "状态",
            alarmMessage: "异常信息",
            remark: "处理建议",
            creationTime: "创建时间",
            lastModifierName: "处理人",
            lastModificationTime: "处理时间"
        },
        button: {
            searchBtn: "搜索",
            resetBtn: "重置",

            importBtn: "导入",
            exportBtn: "导出",

            addBtn: "新增",
            removeBtn: "删除",
            editBtn: "编辑",

            confirmBtn: "确认",
            cancelBtn: "取消",
        },
        message: {
            confirmMsg: "请确认执行此操作?",
            deleteMsg: "你是否想删除这条记录?",
            editMsg: "你是否想编辑这条数据?",
        }
    },
    // 未处理安灯记录
    untreated: {
    }
}