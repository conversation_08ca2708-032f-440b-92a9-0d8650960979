export default {
    // 安灯记录
    record: {
        query: {
            lineCode: "产线编号",
            andonType: "安灯类型",
            andonStatus: "安灯状态",
            dateRange: "搜索时间",
            startTime: "开始时间",
            endTime: "结束时间",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        tbCol: {
            workShopCode: "车间编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            machineCode: "机台编号",
            snNumber: "Sn编号",
            andonType: "安灯类型",
            andonLevel: "异常等级",
            unusualAlarmCode: "异常预警时间编码",
            andonStatus: "状态",
            alarmMessage: "异常信息",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            workShopCode: "车间编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            machineCode: "机台编号",
            snNumber: "Sn编号",
            andonType: "安灯类型",
            andonLevel: "异常等级",
            unusualAlarmCode: "异常预警时间编码",
            andonStatus: "状态",
            alarmMessage: "异常信息",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "请确认执行此操作?",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    // 未处理安灯记录
    untreated: {
        query: {
            lineCode: "产线编号",
            andonType: "安灯类型",
            andonStatus: "安灯状态",
            dateRange: "搜索时间",
            startTime: "开始时间",
            endTime: "结束时间",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            export: "导出",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        tbCol: {
            workShopCode: "车间编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            machineCode: "机台编号",
            snNumber: "Sn编号",
            andonType: "安灯类型",
            andonLevel: "异常等级",
            unusualAlarmCode: "异常预警时间编码",
            andonStatus: "状态",
            alarmMessage: "异常信息",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            workShopCode: "车间编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            machineCode: "机台编号",
            snNumber: "Sn编号",
            andonType: "安灯类型",
            andonLevel: "异常等级",
            unusualAlarmCode: "异常预警时间编码",
            andonStatus: "状态",
            alarmMessage: "异常信息",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "请选择安灯类型",
            alarm02: "请选择安灯状态",
            alarm03: "请填写警示等级",
            alarm04: "",
            alarm05: "",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    }
}