export default {
    wkQueue: {
        query: {
            scheduleCode: "Código agenda",
            lineCode: "Código linha",
            materialCode: "Código produto",
            snNumber: "Código Sn",
        },
        tbCol: {
            snNumber: "Código Sn",
            orderCode: "Código pedido",
            scheduleCode: "Código agenda",
            factoryCode: "Código fábrica",
            wkShopCode: "Código oficina",
            lineCode: "Código linha",
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",
            bomId: "BomId",
            bomType: "Tipo Bom",
            bomVersion: "Versão Bom",
            routeNumber: "Código processo",
            routeVersion: "Versão processo",
            onlineTime: "Tempo início",
            offLineTime: "Tempo fim",
            shiftNo: "Grupo",
            shiftTime: "Tempo grupo",
            isNeedCheck: "Verificação",
            isWarning: "Andon",
            badData: "Não qualificado",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
        },
        message: {
        }
    },
    psStation: {
        query: {
            scheduleCode: "Código agenda",
            lineCode: "Código linha",
            materialCode: "Código produto",
            snNumber: "Código Sn",
        },
        tbCol: {
            snNumber: "Código Sn",
            orderCode: "Código pedido",
            scheduleCode: "Código agenda",
            factoryCode: "Código fábrica",
            wkShopCode: "Código oficina",
            lineCode: "Código linha",
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",
            bomId: "BomId",
            bomType: "Tipo Bom",
            bomVersion: "Versão Bom",
            routeNumber: "Código processo",
            routeVersion: "Versão processo",

            trayNumber: "Código pallet",
            stationCode: "Código estação",
            operationCode: "Código operação",
            isFirstOrEnd: "Início/Fim",
            isPassBegin: "Estação em",
            passBeginNum: "Número em", 
            passBeginTime: "Tempo em",
            isPassEnd: "Estação fora",
            passEndNum: "Número fora",
            passEndTime: "Tempo fora",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
        },
        message: {
        }
    },
    mBind: {
        query: {
            scheduleCode: "Código agenda",
            lineCode: "Código linha",
            materialCode: "Código produto",
            snNumber: "Código Sn",
            assemblyMaterialCode: "Código material",
            assemblyMaterialSn: "Código material Sn",
        },
        tbCol: {
            snNumber: "Código Sn",
            orderCode: "Código pedido",
            scheduleCode: "Código agenda",
            factoryCode: "Código fábrica",
            wkShopCode: "Código oficina",
            lineCode: "Código linha",
            materialCode: "Código produto",
            materialName: "Nome produto",
            materialVersion: "Versão produto",
            stationCode: "Código estação",
            operationCode: "Código operação",

            assemblyMaterialCode: "Código material",
            assemblyMaterialName: "Nome material",
            assemblyMaterialVersion: "Versão material",
            assemblyMaterialQty: "Quantidade material",
            assemblySort: "Ordem montagem",
            assemblyTime: "Tempo montagem",
            assemblyMaterialSn: "Código material Sn",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
        },
        message: {
        }
    },
    wkParam: {
        query: {
            scheduleCode: "Código agenda",
            lineCode: "Código linha",
            stationCode: "Código estação",
            operationCode: "Código operação",
            materialCode: "Código produto",
            snNumber: "Código Sn",
        },
        tbCol: {
            snNumber: "Código Sn",
            orderCode: "Código pedido",
            scheduleCode: "Código agenda",
            materialCode: "Código produto",
            materialVersion: "Versão produto",
            routeNumber: "Código processo",
            routeVersion: "Versão processo",
            bomId: "BomId",
            bomType: "Tipo Bom",
            bomVersion: "Versão Bom",
            factoryCode: "Código fábrica",
            wkShopCode: "Código oficina",
            lineCode: "Código linha",
            stationCode: "Código estação",
            machineCode: "Código máquina",
            operationCode: "Código operação",
            lastOperationCode: "Operação anterior",
            nextOperationCode: "Próxima operação",

            paramCode: "Código parâmetro",
            paramName: "Nome parâmetro",

            standardRange1: "Limite inferior",
            standardRange2: "Limite superior",
            standardValue: "Valor padrão",
            targetValue: "Valor alvo",
            realValue: "Valor real",
            checkResult: "Aprovado",
            redoCount: "Repetição",
            isWarning: "Andon",
            IsBad: "Não aprovado",
            isSkip: "Ignorar",

            orderNum: "Ordem",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Nome criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Nome modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        form: {
        },
        message: {
        }
    },
    wkFeeding: {},
    wkTask: {},
}