export default {
    wkQueue: {
        query: {
            scheduleCode: "ScheduleCode",
            lineCode: "LineCode",
            materialCode: "ProductCode",
            snNumber: "SnCode",
        },
        tbCol: {
            snNumber: "SnCode",
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            factoryCode: "FactoryCode",
            wkShopCode: "WorkshopCode",
            lineCode: "LineCode",
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",
            bomId: "BomId",
            bomType: "BomType",
            bomVersion: "BomVersion",
            routeNumber: "ProcessCode",
            routeVersion: "ProcessVersion",
            onlineTime: "OnlineTime",
            offLineTime: "OffLineTime",
            shiftNo: "ShiftNo",
            shiftTime: "ShiftTime",
            isNeedCheck: "IsNeedCheck",
            isWarning: "IsWarning",
            badData: "IsBad",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            start: "Start",
            stop: "Stop",
            restore: "Restore",
            cancle: "Cancle",
            close: "Close",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
        },
        message: {
        }
    },
    psStation: {
        query: {
            scheduleCode: "ScheduleCode",
            lineCode: "LineCode",
            materialCode: "ProductCode",
            snNumber: "SnCode",
        },
        tbCol: {
            snNumber: "SnCode",
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            factoryCode: "FactoryCode",
            wkShopCode: "WorkshopCode",
            lineCode: "LineCode",
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",
            bomId: "BomId",
            bomType: "BomType",
            bomVersion: "BomVersion",
            routeNumber: "ProcessCode",
            routeVersion: "ProcessVersion",

            trayNumber: "TrayNumber",
            stationCode: "StationCode",
            operationCode: "OperationCode",
            isFirstOrEnd: "IsFirstOrEnd",
            isPassBegin: "InStation",
            passBeginNum: "InQty",
            passBeginTime: "InTime",
            isPassEnd: "OutStation",
            passEndNum: "OutQty",
            passEndTime: "OutTime",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            start: "Start",
            stop: "Stop",
            restore: "Restore",
            cancle: "Cancle",
            close: "Close",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
        },
        message: {
        }
    },
    mBind: {
        query: {
            scheduleCode: "ScheduleCode",
            lineCode: "LineCode",
            materialCode: "ProductCode",
            snNumber: "SnCode",
            assemblyMaterialCode: "MaterialCode",
            assemblyMaterialSn: "MaterialSn",
        },
        tbCol: {
            snNumber: "SnCode",
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            factoryCode: "FactoryCode",
            wkShopCode: "WorkshopCode",
            lineCode: "LineCode",
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",
            stationCode: "StationCode",
            operationCode: "OperationCode",

            assemblyMaterialCode: "MaterialCode",
            assemblyMaterialName: "MaterialName",
            assemblyMaterialVersion: "MaterialVersion",
            assemblyMaterialQty: "MaterialQty",
            assemblySort: "AssemblySort",
            assemblyTime: "AssemblyTime",
            assemblyMaterialSn: "MaterialSn",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            start: "Start",
            stop: "Stop",
            restore: "Restore",
            cancle: "Cancle",
            close: "Close",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
        },
        message: {
        }
    },
    wkParam: {
        query: {
            scheduleCode: "ScheduleCode",
            lineCode: "LineCode",
            stationCode: "StationCode",
            operationCode: "OperationCode",
            materialCode: "ProductCode",
            snNumber: "SnCode",
        },
        tbCol: {
            snNumber: "SnCode",
            orderCode: "OrderCode",
            scheduleCode: "ScheduleCode",
            materialCode: "ProductCode",
            materialVersion: "ProductVersion",
            routeNumber: "ProcessCode",
            routeVersion: "ProcessVersion",
            bomId: "BomId",
            bomType: "BomType",
            bomVersion: "BomVersion",
            factoryCode: "FactoryCode",
            wkShopCode: "WorkshopCode",
            lineCode: "LineCode",
            stationCode: "StationCode",
            operationCode: "OperationCode",
            lastOperationCode: "LastOperation",
            nextOperationCode: "NextOperation",

            paramCode: "ParamCode",
            paramName: "ParamName",

            standardRange1: "LowerLimit",
            standardRange2: "UpperLimit",
            standardValue: "StandardValue",
            targetValue: "TargetValue",
            realValue: "RealValue",
            checkResult: "IsPass",
            redoCount: "RedoCount",
            isWarning: "IsWarning",
            IsBad: "IsBad",
            isSkip: "IsSkip",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            start: "Start",
            stop: "Stop",
            restore: "Restore",
            cancle: "Cancle",
            close: "Close",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
        },
        message: {
        }
    },
    wkFeeding: {},
    wkTask: {},
}