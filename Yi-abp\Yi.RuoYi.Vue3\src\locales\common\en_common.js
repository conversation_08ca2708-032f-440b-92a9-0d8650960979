export default {
    currentLanguage: "en",
    title: "BYD MES",
    routerIndex: {
        firstPage: "Home Page",
        personalCenter: "PersonalCenter",
        userAuth: "UserAuth",
        roleAuth: "RoleAuth",
        dictData: "DictData",
        jobLog: "JobLog",
        genEdit: "GenSettingEdit",
    },
    navbar: {
        personalCenter: "PersonalCenter",
        layout: "Layout",
        logout: "Logout"
    },
    tagsView:{
        refresh:"Refresh",
        closeNow:"Close Now",
        closeElse:"Close Else",
        closeLeft:"Close Left",
        closeRight:"Close Right",
        closeAll:"Close All"        
    },
    layoutFormat: {
        layoutTitle: "Layout Style",
        colorText: "Layout Color",
        subTitle: "System Layout",
        showTopNav: "Open TopNav",
        showTagsViews: "Open Tags-Views",
        fixedHeader: "Fixed Header",
        showLogo: "Show Logo",
        dynamicTitle: "Dynamic Title",
        saveBtn: "Save",
        resetBtn: "Reset",
        //commponents sizeSelect
        larger: "Larger",
        default: "Default",
        smaller: "Smaller",
        szieSwitchMsg: "Setting layout size, Please wait.."
    },
    userProfile: {
        profilePicture: "SetProfilePicture",
        pictureTips: "ClickUpload",
        select: "Choice",
        upload: "Upload",
        uploadfailMsg: "Image recognition failed, Please upload a file of image type, Like JPG, PNG",

        personalInfo: "Personal Info",
        userName: "User Name",
        cellPhone: "Cell Phone",
        email: "User Email",
        deptName: "Dept Name",
        createTime: "Creation Time",

        baseInfo: "Basic Info",
        userNameEdit: "UserName",
        userNameVerify: "User nickname cannot be empty",
        cellPhoneEdit: "CellPhone",
        cellPhoneVerify: "Phone number cannot be empty",
        emailEdit: "UserEmail",
        emailVerify: "Email address cannot be empty",
        emailRegex: "Please enter the correct email address",

        pwdEdit: "Edit Password",
        oldPwd: "OldPwd",
        oldPwdTip: "Please enter old password",
        oldPwdVerify: "Old password cannot be empty",
        newPwd: "NewPwd",
        newPwdTip: "Please enter a new password",
        newPwdVerify: "The new password cannot be empty",
        newPwdRegex: "Length ranging from 6 to 20 characters",
        confirmPwd: "Confirm",
        confirmPwdTip: "Please confirm the new password",
        confirmPwdVerify: "Confirm password cannot be empty",
        pwdComparisonMsg: "Entered passwords differ!",

        saveBtn: "Save",
        colseBtn: "Close",

        editMsg: "Edited Success",
    }
}