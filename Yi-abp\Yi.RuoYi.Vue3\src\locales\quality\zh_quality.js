export default {
    badCode: {
        query: {
            operationCode: "工序编号",
            badCode: "不良代号",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        tbCol: {
            operationCode: "工序编号",
            badCode: "不良代号",
            badName: "不良名称",
            badTypeCode: "不良类型编号",
            badTypeName: "不良类型名称",
            enableStatus: "是否启用",
            dataStatus: "是否删除",
            addTime: "添加时间",
            editTime: "编辑时间",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            operationCode: "工序编号",
            badCode: "不良代号",
            badName: "不良名称",
            badTypeCode: "不良类型编号",
            badTypeName: "不良类型名称",
            enableStatus: "是否启用",
            dataStatus: "是否删除",
            addTime: "添加时间",
            editTime: "编辑时间",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "请填写工序编号",
            alarm02: "请填写不良编号",
            alarm03: "请填写不良名称",
            alarm04: "请填写不良类型编号",
            alarm05: "请填写不良类型名称",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
    badData: {
        query: {
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            lineCode: '产线编号',
            operationCode: "工序编号",
            snNumber: "SN号",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        tbCol: {
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            snNumber: "SN号",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            lineCode: "产线编号",
            stationCode: "工位编号",
            operationCode: "工序编号",
            badCode: "不良代号",
            badFactor: "不良因素",
            badQty: "不良数量",
            userId: "用户Id",
            editTime: "创建时间",
            description: "描述",
            auditOpinion: "审核意见",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            snNumber: "SN号",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            lineCode: "产线编号",
            stationCode: "工位编号",
            operationCode: "工序编号",
            badCode: "不良代号",
            badFactor: "不良因素",
            badQty: "不良数量",
            userId: "用户Id",
            editTime: "创建时间",
            images:"图片",
            description: "描述",
            auditOpinion: "审核意见",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "请选择中Aug那台",
            alarm02: "请填写评审建议",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
    retrace: {
        query: {
            snNumber: "Sn编号",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        tbCol: {
            orderCode: "工单编号",
            orderType: "工单类型",
            orderQty: "工单数量",
            orderStatus: "工单状态",
            lineCode: "产线编号",

            scheduleCode: "排程编号",
            scheduleQty: "排程数量",
            scheduleStatus: "排程状态",
            planStartTime: "计划开始时间",
            planEndTime: "计划结束时间",

            psStation: "工位过站",
            stationCode: "工位编号",
            passBeginTime: "过站开始时间",
            passEndTime: "过站结束时间",

            mBind: "物料绑定",
            operationCode: "工序编号",
            assemblyMaterialCode: "组装物料编号",
            assemblyTime: "组装时间",
            assemblyMaterialSn: "组装物料SN号",

            wkParams: "工艺参数",
            operationCode: "工序编号",
            creationTime: "创建时间",
            paramCode: "参数编号",
            standardRange1: "值下限",
            standardRange2: "值上限",
            standardValue: "标准值",
            targetValue: "目标值",
            realValue: "实际值",
            checkResult: "检查结果",

            andon: "安灯",
            stationCode: "工位编号",
            unusualAlarmCode: "异常报警编号",
            lastModificationTime: "最后修改时间",
            remark: "备注",
            lastModifierName: "修改人账号",

            badData: "不良数据",
            operationCode: "工序编号",
            badCode: "不良代号",
            badFactor: "不良因素",
            description: "描述",
            lastModificationTime: "最后修改时间",
            lastModifierName: "修改人账号",
            auditOpinion: "审核意见",
        },
        form: {},
        message: {},
    }
}