<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true">
         <el-form-item :label="t('andon.record.query.lineCode')" prop="lineCode">
            <el-input v-model="queryParams.lineCode" :placeholder="t('andon.record.query.lineCode')" clearable @keyup.enter="handleQuery" style="width: 230px;" />
         </el-form-item>
         <el-form-item :label="t('andon.record.query.andonType')" prop="andonType" style="width: 260px">
            <el-select v-model="queryParams.andonType" :placeholder="t('andon.record.query.andonType')" clearable>
               <el-option v-for="dict in andon_type" :key="JSON.parse(dict.value)" :label="dict.label" :value="JSON.parse(dict.value)" />
            </el-select>
         </el-form-item>
         <el-form-item :label="t('andon.record.query.andonStatus')" prop="andonStatus">
            <el-select v-model="queryParams.andonStatus" :placeholder="t('andon.record.query.andonStatus')" clearable style="width:200px">
               <el-option v-for="dict in sys_manage_state" :key="JSON.parse(dict.value)" :label="dict.label" :value="JSON.parse(dict.value)" />
            </el-select>
         </el-form-item>
         <el-form-item :label="t('andon.record.query.dateRange')" style="width:300px">
            <el-date-picker v-model="dateRange" :unlink-panels=true value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="t('andon.record.query.startTime')" :end-placeholder="t('andon.record.query.endTime')"></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ t('andon.record.button.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ t('andon.record.button.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="dataList">
         <el-table-column type="selection" width="30" align="center" />
         <el-table-column label="编号" align="center" prop="id" v-if="false" />
         <el-table-column :label="t('andon.record.tbCol.workShopCode')" align="center" prop="workShopCode" />
         <el-table-column :label="t('andon.record.tbCol.lineCode')" align="center" prop="lineCode" />
         <el-table-column :label="t('andon.record.tbCol.stationCode')" align="center" prop="stationCode" />
         <el-table-column :label="t('andon.record.tbCol.machineCode')" align="center" prop="machineCode" />
         <el-table-column :label="t('andon.record.tbCol.snNumber')" align="center" prop="SnNumber" />
         <el-table-column :label="t('andon.record.tbCol.andonType')" align="center" prop="andonType" />
         <el-table-column :label="t('andon.record.tbCol.andonLevel')" align="center" prop="andonLevel" />
         <el-table-column :label="t('andon.record.tbCol.unusualAlarmCode')" align="center" prop="unusualAlarmCode" />
         <el-table-column :label="t('andon.record.tbCol.andonStatus')" align="center" prop="andonStatus">
            <template #default="scope">
               <el-select size="small" v-model="scope.row.andonStatus" :disabled=true>
                  <el-option v-for="item in sys_manage_state" :key="JSON.parse(item.value)" :value="JSON.parse(item.value)" :label="item.label">
                  </el-option>
               </el-select>
            </template>
         </el-table-column>
         <el-table-column :label="t('andon.record.tbCol.alarmMessage')" align="center" prop="alarmMessage" :show-overflow-tooltip=true width="120" />
         <el-table-column :label="t('andon.record.tbCol.remark')" align="center" prop="remark" :show-overflow-tooltip=true width="120" />
         <el-table-column :label="t('andon.record.tbCol.creationTime')" align="center" prop="creationTime" width="155" />
         <el-table-column :label="t('andon.record.tbCol.lastModifierName')" align="center" prop="lastModifierName" />
         <el-table-column :label="t('andon.record.tbCol.lastModificationTime')" align="center" prop="lastModificationTime" width="155" />
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />
   </div>
</template>

<script setup name="AndonRecord">
import { listData } from "@/api/andon/record";
import { useI18n } from 'vue-i18n'

const { proxy } = getCurrentInstance();
const { andon_type } = proxy.useDict("andon_type");
const { sys_manage_state } = proxy.useDict("sys_manage_state");
const { t } = useI18n()

const total = ref(0);
const loading = ref(true);
const dataList = ref([]);
const dateRange = ref([]);
const data = reactive({
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   }
});
const { queryParams } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listData(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 重置按钮操作 */
function resetQuery() {
   dateRange.value = [];
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}

/** 导入按钮操作 */
const handleImport = async (file,files) => {
   importLoading.value = true;
   importDisabled.value = true;
   if(files.length > 1){
      files.shift();
   };
   const data = await getXlsxData(file);
   tableData.value = translateField(data);

   // 数字转字符串
   tableData.value = JSON.parse(JSON.stringify(tableData.value, (key, value) => typeof value === 'number' ? String(value) : value));

   importAsync(tableData.value).then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         importLoading.value = false;
         importDisabled.value = false;
         proxy.$modal.msgSuccess("导入成功,变更行数:"+response.data);
      }, 1500);
   });
}
//读取表格数据
const getXlsxData = async (file) => {
   const dataBinary = await readFile(file);
   const workBook = XLSX.read(dataBinary ,{
      type: "binary",
      cellDates: true
   });
   const workSheet = workBook.Sheets[workBook.SheetNames[0]];
   const data = XLSX.utils.sheet_to_json(workSheet);
   data.forEach((item) => {
      const keys = ["StartTime","EndTime"];
      for (const key in item) {
         if (moment(item[key], 'YYYY-MM-DD', true).isValid()) {
            item[key] = moment(item[key]).format("YYYY-MM-DD HH:mm:ss")
         }
      }
   });
   return data;
}
//读取excel文件
const readFile = (file) => {
   return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsArrayBuffer(file.raw)
      reader.onload = (e) => {
         resolve(e.target.result)
      }
      reader.onerror = (e) => {
         reject(e)
      }
   })
}
//映射字段
const translateField = (data) => {
  const arr = []
  const cnToEn = {
      工单编号:'OrderCode',
      排程编号:'ScheduleCode',
      工艺编码:'RouteNumber',
      工艺版本:'RouteVersion',
      工序编号:'OperationCode',
      工序名称:'OperationName',
      工序顺序:'Sort',
      前序编号:'LastOperationCode',
      后序遍号:'NextOperationCode',
      工站合集:'StationCode',
      更新时间:'EditTime',
   }
   data.forEach((item) => {
      const arrItem = {}
      Object.keys(item).forEach((key) => {
         arrItem[cnToEn[key]] = item[key]
      })
      arr.push(arrItem) 
   })
   return arr
}


getList();
</script>
