export default {
    dictionary: {
        queryField: {
            dictName: "Dict Name",
            dictype: "Data Type",
            state: "State",
            creattime: "Creation Time",
            search: "Search",
            tipContent: "Please enter a dictionary name",
            tipType: "Please enter a dictionary type",
            tipState: "Dictionary status",
            startTime: "StartTime",
            endTime: "EndTime",
        },
        tbColumn: {
            dictName: "Chinese Name",
            dicEngName: "English Name",
            dicPtName: "Portuguese Name",
            dicttype: "Dict Type",
            state: "State",
            remark: "Remark",
            creationTime: "Creation Time",
            operate: "Operate",
            edit: "Edit",
            delete: "Delete",
        },
        button: {
            search: "Search",
            reset: "Reset",
            increase: "Increase",
            edit: "Edit",
            delete: "Delete",
        },
        elForm: {
            dictName: "Chi Dict Name",
            chinaTips: "Tip:Please enter a dictionary name",
            dictEngName: "Eng Dict Name",
            engTips: "Tip:Please enter an English name",
            dictPtName: "Pt Dict Name",
            ptTips: "Tip:Please enter an  Portuguese name",
            dictType: "Dict Type",
            tipType: "Tip:Please enter a dictionary type",
            state: "State",
            remark: "Remark",
            tipContent: "Tip:Please enter your content",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        message: {
            addTitle: "Add Dictionary Type",
            addSuccess: "Add Success",
            editTitle: "Edit Dictionary Type",
            editSuccess: "Edit Success",
            delMsg: "Are you sure to delete the selected item?",
            delSuccess: "Del Success",
            refreshSuccess: "Refresh Success",
            alarm01: "The dictionary name cannot be empty",
            alarm02: "The dictionary type cannot be empty",
        },
    },
    dictionaryData: {
        queryField: {
            dictType: "Dict Type",
            dictLabel: "Dict Tag",
            labelTip: "Please enter a dictionary tag",
            state: "State",
        },
        tbColumn: {
            chiDictags: "Chinese Dict Tag",
            engDictags: "English Dic Tag",
            ptDictags: "Pt Dict Tag",
            dicKey: "Dict Key",
            dicSort: "Dict Sorting",
            state: "State",
            remark: "Remark",
            creaTime: "CreationTime",
            operate: "Operate",
            edit: "Edit",
            delete: "Delete",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            edit: "Edit",
            delete: "Delete",
            download: "Download",
            close: "Close",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            dicType: "Dictionary Type",
            chineseLabels: "Chinese Tag",
            englishLabels: "English Tag",
            labelPortuguese: "Portuguese Tag",
            keyValue: "Key Values",
            styleAttributes: "Style Attributes",
            displaySorting: "Display Sorting",
            echoStyle: "Echo Style",
            state: "State",
            remark: "Remark",
            chinaTip: "Please enter a Chinese Tag",
            engTip: "Please enter the English Tag",
            ptTip: "Please enter the Portuguese Tag",
            keyTip: "Please enter a data key",
            attriTip: "Please enter style attributes",
            contentTip: "Please enter your content",
        },
        message: {
            alarm01: "The data label cannot be empty",
            alarm02: "The data key cannot be empty",
            alarm03: "The data order cannot be empty",
            add_title: "Add Dictionary Data",
            add_msg: "Add Success",
            edit_title: "Edit Dictionary Data",
            edit_msg: "Edit Success",
            del_tip: "Are you sure you want to delete the selected data item?",
            del_msg: "Delete Success",
        },
    },
    factoryModel: {
        queryField: {
            modelNumber: "Model Number",
            modelType: " Model Type",
        },
        tbColumn: {
            parentModel: "Parent Model",
            modelName: "Model Name",
            modelNumber: "Model Number",
            order: "Order",
            modelType: "Model Type",
            modelState: "Model State",
            nameTip: "Please enter a name for the model",
            numberTip: "Modeling Number",
            typeTip: "Please enter the modeling type",
        },
        button: {
            search: "Search",
            reset: "Reset",
            expandCollapse: "Expand/Collapse",
            add: "Add",
            synchronous: "Sync",
            edit: "Edit",
            delete: "Delete",
            download: "Download",
            close: "Close",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            modelName: "Model Name",
            modelNumber: "Model Number",
            modelType: "Model Type",
            parentModelCode: "Parent Model Code",
            mesStart: "Mes Start",
            mesDelete: "Mes Delete",
            addTime: "Add Time",
            changeTime: "Change Time",
            order: "Order",
            state: "State",
            remark: "Remark",
            creater: "Creater",
            creatTime: "Creat Time",
            processors: "Processors",
            processTime: "Process Time",
            select: "Is Deleted",
            operate: "Operate",
        },
        message: {
            alarm01: "The modeling number cannot be empty",
            alarm02: "The modeling name cannot be empty",
            editAlarm: "Edited successfully",
            addAlarm: "The new is successful",
            deleteAlarm: "The deletion is successful",
            importAlarm: "The import was successful",
            ifDelete: "Check whether to confirm that the deletion name is set to",
            success: "The synchronization is successful, and the number of rows is changed",
            ifData: "Data items?",
            add_title: "Add",
            edit_title: "Edit",
            submit_alarm01: "The current model cannot be used as one's own superior",
            submit_alarm02: "Code repeat, Not allowed to save",
            submit_alarm03: "Code repeat, Not allowed to save",
        },
    },
    deptManage: {
        queryField: {
            deptName: "Dept Name",
            nameTip: "Please enter the name of the department",
            depCode: "Dept Code",
            codeTip: "Please enter the department number",
            state: "State",
            deptState: "Dept State",
        },
        tbColumn: {
            deptName: "Dept Name",
            deptCode: "Dept Code",
            deptNumber: "Dept Number",
            state: "State",
            creatTime: "CreatTime",
            operate: "Operate",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            show: "Expand/Collapse",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            superiorDept: "Superior Dept",
            superiorSelect: "Please select the parent department",
            deptName: "Dept Name",
            nameTip: "Please enter the name of the department",
            deptNumber: "Dept Number",
            numberTip: "Please enter the department number",
            order: "Order",
            leader: "Leader",
            leaderTip: "Please enter the person in charge",
            deptState: "Dept State",
        },
        message: {
            alarm01: "The department number cannot be empty",
            alarm02: "The department name cannot be empty",
            alarm03: "The sort cannot be empty",
            editSuccess: "Edited Successfully",
            addSucess: "Increase Success",
            ifDelete: "Check whether to confirm that the deletion name is set to",
            ifData: "Data Items",
            deleteSucess: "The deletion is successful",
            add_title: "Add Department",
            edit_title: "Edit Department",
            edit_msg01: "The current Dept cannot be used as one's own superior",
        },
    },
    post: {
        queryField: {
            postCode: "Post Code",
            codeTip: "Please enter the post code",
            postName: "Post Name",
            nameTip: "Please enter the post title",
            state: "State",
            postState: "Post State",
        },
        tbColumn: {
            postCode: "Post Code",
            postName: "Post Name",
            postOrder: "Post Order",
            state: "State",
            creatTime: "CreatTime",
            operate: "Operate",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            edit: "Edit",
            delete: "Delete",
            download: "Download",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            postName: "PostName",
            nameTip: "Please enter the job title",
            postCode: "Post Code",
            codeTip: "Please enter a code name",
            postOrder: "Post Order",
            postState: "Post State",
            remark: "Remark",
            remarkTip: "Please enter a comment",
        },
        message: {
            alarm01: "The job title cannot be empty",
            alarm02: "The post code cannot be empty",
            alarm03: "The post order cannot be empty",
            editSuccess: "Edited successfully",
            addSucess: "The new is successful",
            ifDelete: "Whether to confirm the deletion of the post number",
            ifData: "Data items?",
            deleteSucess: "The deletion is successful",
        },
    },
    material: {
        queryField: {
            materialCode: "Material Code",
            pnCode: "PnCode",
        },
        tbColumn: {
            id: "id",
            materialCode: "Material Code",
            materialName: "Material Name",
            materialType: "Material Type",
            materialVersion: "Material Version",
            pnCode: "Pn Code",
            pnShortCode: "Pn Short Code",
            pnName: "Pn Name",
            measureUnitCode: "Measure Unit Code",
            measureUnitName: "Measure Unit Name",
            measureUnitQuantity: "Measure Unit Quantity",
            regular: "Regular",
            productModelCode: "Product Model Code",
            effactTime: "Effact Time",
            enableStatus: "Enable Status",
            dataStatus: "Data Status",
            addTime: "Add Time",
            editTime: "Edit Time",
            oldSortID: "Old SortID",
            newSortID: "New SortID",
            order: "Order",
            status: "Status",
            remark: "Remark",
            creator: "Creator",
            creatTime: "CreatTime",
            lastModifierId: "Last ModifierId",
            lastModificationTime: "Last Modification Time",
            ifdelete: "Is Deleted",
            operate: "Operate",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            synchronous: "Synchronous",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            materialCode: "Material Code",
            codeTip: "Material Code",
            materialName: "Material Name",
            nameTip: "Material Name",
            materialType: "Material Type",
            typeTip: "Material Type",
            materialVersion: "Material Version",
            versionTip: "Material Version",
            pnCode: "Pn Code",
            pncodeTip: "Pn Code",
            pnName: "Pn Name",
            pnNametIP: "Pn Name",
            pnShortCode: "Pn Short Code",
            pnShortCodeTip: "Pn Short Code",
            measureUnitCode: "Measure Unit Code",
            measureUnitCodeTip: "Measure Unit Code",
            measureUnitName: "Measure Unit Name",
            measureUnitNameTip: "Measure Unit Name",
            measureUnitQuantity: "Measure Unit Quantity",
            measureUnitQuantityTip: "Measure Unit Quantity",
            regular: "Regular",
            regularTip: "Regular",
            productModelCode: "Product Model Code",
            productModelCodeTip: "Product Model Code",
            effactTime: "Effact Time",
            state: "State",
            remark: "Remark",
            remarkTip: "Please enter your content",
        },
        message: {
            codeAlarm: "Please fill in the material code",
            nameAlarm: "Please fill in the name of the item",
            typeAlarm: "Please fill in the type of andon",
            pnAlarm: "Please fill in the Pn number",
            measureUnitNameAlarm: "Please fill in the unit of measurement",
            measureUnitQuantityAlarm: "Please fill in the number of units of measurement",
            timeAlarm: "Please select an effective time",
            ifdelete: "Whether to delete the number",
            ifDAta: "Data items?",
            deleteAlarm: "The deletion is successful",
            successAlarm: "The synchronization is successful, and the number of rows is changed",
            editAlarm: "Edited successfully",
            addAlarm: "The new is successful",
            importAlarm: "The import was successful",
        },
    },
    pskill: {
        queryField: {
            empCode: "Employee Code",
            empCodeTip: "Employee Code",
            certificateNo: "Certificate No.",
            certificateNoTip: "Certificate No.",
        },
        tbColumn: {
            num: "Num",
            empCode: "Emp Code",
            operationCode: "Operation Code",
            certificateNo: "Certificate No.",
            controlStatusCode: "Control Status Code",
            skillId: "Skill Id",
            skillLevelCode: "Skill Level Code",
            skillStartDate: "skill Start Date",
            skillValidity: "Skill Validity",
            enableStatus: "Enable Status",
            dataStatus: "Data Status",
            addTime: "Add Time",
            editTime: "Edit Time",
            orderNum: "Order Num",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "Creator Name",
            creationTime: "Creation Time",
            lastModifierId: "Last ModifierId",
            lastModifierName: "Last Modifier Name",
            lastModificationTime: "Last Modification Time",
            isDeleted: "Is Deleted",
            operate: "Operate",

        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            synchronous: "Synchronous",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            empCode: "EmpCode",
            empCodeTIP: "EmpCode",
            operationCode: "Operation Code",
            operationCodeTip: "Operation Code",
            certificateNo: "Certificate No.",
            certificateNoTip: "Certificate No.",
            controlStatusCode: "Status code",
            controlStatusCodeTip: "Status code",
            skillLevelCode: "Level Code",
            skillLevelCodeTip: "Level Code",
            skillStartDate: "Effective Date",
            skillValidity: "Valid Time",
            skillValidityTip: "Valid Time",
            state: "State",
            remark: "Remark",
            remarkTip: "Please enter your content",

        },
        message: {
            alarm01: "Please fill in the employee number",
            alarm02: "Please fill in the certificate number",
            alarm03: "Please fill in the control status code",
            alarm04: "Please fill in the skill ID",
            alarm05: "Please fill in the skill level number",
            alarm06: "Please fill in the effective date",
            alarm07: "Please select a validity period",
            ifDelete: "Do you want to delete the data item?",
            deleteSucess: "The deletion is successful",
            success: "The synchronization is successful, and the number of rows is changed:",
            editSuccess: "Edited successfully",
            addSucess: "The new is successful",

        },
    },
}