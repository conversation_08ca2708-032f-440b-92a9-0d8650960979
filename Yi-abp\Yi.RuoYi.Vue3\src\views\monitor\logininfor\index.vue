<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('monitor.login.query.ipAddress')" prop="loginIp">
            <el-input v-model="queryParams.loginIp" :placeholder="t('monitor.login.query.ipAddress')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('monitor.login.query.userName')" prop="loginUser">
            <el-input v-model="queryParams.loginUser" :placeholder="t('monitor.login.query.userName')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('monitor.login.query.onlineStatus')" prop="isDeleted">
            <el-select v-model="queryParams.isDeleted" :placeholder="t('monitor.login.query.onlineStatus')" clearable style="width: 240px">
               <el-option v-for="dict in sys_common_status" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
         </el-form-item>
         <el-form-item :label="t('monitor.login.query.logTime')" style="width: 308px">
            <el-date-picker v-model="dateRange" value-format="YYYY-MM-DD" type="daterange" range-separator="-" :start-placeholder="t('monitor.login.query.startTime')" :end-placeholder="t('monitor.login.query.endTime')"></el-date-picker>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('monitor.login.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('monitor.login.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="single" @click="handleDelete" v-hasPermi="['monitor:logininfor:remove']" v-if="false">{{t('monitor.login.button.delete')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" @click="handleClean" v-hasPermi="['monitor:logininfor:clean']">{{t('monitor.login.button.clean')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Unlock" :disabled="single" @click="handleUnlock" v-hasPermi="['monitor:logininfor:unlock']">{{t('monitor.login.button.unlock')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['monitor:logininfor:export']">{{t('monitor.login.button.export')}}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table ref="logininforRef" v-loading="loading" :data="logininforList" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="编号" align="center" prop="id" v-if="false" />
         <el-table-column :label="t('monitor.login.tbCol.userName')" align="center" prop="loginUser" :show-overflow-tooltip="true" sortable="custom" :sort-orders="['descending', 'ascending']" />
         <el-table-column :label="t('monitor.login.tbCol.ipAddress')" align="center" prop="loginIp" :show-overflow-tooltip="true" />
         <el-table-column :label="t('monitor.login.tbCol.location')" align="center" prop="loginLocation" :show-overflow-tooltip="true" />
         <el-table-column :label="t('monitor.login.tbCol.system')" align="center" prop="os" :show-overflow-tooltip="true" />
         <el-table-column :label="t('monitor.login.tbCol.browser')" align="center" prop="browser" :show-overflow-tooltip="true" />
         <el-table-column :label="t('monitor.login.tbCol.msg')" align="center" prop="msg" />
         <el-table-column :label="t('monitor.login.tbCol.creationTime')" align="center" prop="creationTime" sortable="custom" :sort-orders="['descending', 'ascending']" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.creationTime) }}</span>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />
   </div>
</template>

<script setup name="LoginInfor">
import { list, delLogininfor, cleanLogininfor, unlockLogininfor } from "@/api/monitor/logininfor";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { sys_common_status } = proxy.useDict("sys_common_status");

const logininforList = ref([]);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const selectName = ref("");
const total = ref(0);
const dateRange = ref([]);
const defaultSort = ref({ prop: "createTime", order: "descending" });

// 查询参数
const queryParams = ref({
   skipCount: 1,
   maxResultCount: 10,
   loginIp: undefined,
   loginUser: undefined,
   isDeleted: undefined,
   orderByColumn: undefined,
   isAsc: undefined
});

/** 查询登录日志列表 */
function getList() {
   loading.value = true;
   list(proxy.addDateRange(queryParams.value, dateRange.value)).then(response => {
      logininforList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   dateRange.value = [];
   proxy.resetForm("queryRef");
   proxy.$refs["logininforRef"].sort(defaultSort.value.prop, defaultSort.value.order);
   handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   multiple.value = !selection.length;
   single.value = selection.length != 1;
   selectName.value = selection.map(item => item.loginUser);
}
/** 排序触发事件 */
function handleSortChange(column, prop, order) {
   if (!column.order) {
      queryParams.value.orderByColumn = null;
   } else {
      queryParams.value.orderByColumn = column.prop;
   }
   queryParams.value.isAsc = column.order;
   getList();
}
/** 删除按钮操作 */
function handleDelete(row) {
   const infoIds = row.id || ids.value;
   proxy.$modal.confirm(t('monitor.login.message.delMsg') + infoIds).then(function () {
      return delLogininfor(infoIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('monitor.login.message.delSuc'));
   }).catch(() => { });
}
/** 清空按钮操作 */
function handleClean() {
   proxy.$modal.confirm(t('monitor.login.message.cleanMsg')).then(function () {
      return cleanLogininfor();
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('monitor.login.message.cleanSuc'));
   }).catch(() => { });
}
/** 解锁按钮操作 */
function handleUnlock() {
   const loginUser = selectName.value;
   proxy.$modal.confirm(t('monitor.login.message.unlockMsg') + loginUser).then(function () {
      return unlockLogininfor(loginUser);
   }).then(() => {
      proxy.$modal.msgSuccess(loginUser + t('monitor.login.message.unlockSuc'));
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   proxy.download("monitor/logininfor/export", {
      ...queryParams.value,
   }, `config_${new Date().getTime()}.xlsx`);
}

getList();
</script>
