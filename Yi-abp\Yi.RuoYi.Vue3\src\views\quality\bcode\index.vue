<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('quality.badCode.query.operationCode')" prop="operationCode">
            <el-input v-model="queryParams.operationCode" :placeholder="t('quality.badCode.query.operationCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('quality.badCode.query.badCode')" prop="badCode">
            <el-input v-model="queryParams.badCode" :placeholder="t('quality.badCode.query.badCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('quality.badCode.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('quality.badCode.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['quality:bcode:add']">{{t('quality.badCode.button.add')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['quality:bcode:down']" :loading="downLoading">{{t('quality.badCode.button.sync')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['quality:bcode:import']" :loading="importLoading">{{t('quality.badCode.button.import')}}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column :label="t('quality.badCode.tbCol.operationCode')" align="center" prop="operationCode" min-width="100" />
         <el-table-column :label="t('quality.badCode.tbCol.badCode')" align="center" prop="badCode" />
         <el-table-column :label="t('quality.badCode.tbCol.badName')" align="center" prop="badName" />
         <el-table-column :label="t('quality.badCode.tbCol.badTypeCode')" align="center" prop="badTypeCode" />
         <el-table-column :label="t('quality.badCode.tbCol.badTypeName')" align="center" prop="badTypeName" />
         <el-table-column :label="t('quality.badCode.tbCol.enableStatus')" align="center" prop="enableStatus" width="80" />
         <el-table-column :label="t('quality.badCode.tbCol.dataStatus')" align="center" prop="dataStatus" width="80" />
         <el-table-column :label="t('quality.badCode.tbCol.addTime')" align="center" prop="addTime" width="155" />
         <el-table-column :label="t('quality.badCode.tbCol.editTime')" align="center" prop="editTime" width="155" />

         <el-table-column prop="orderNum" :label="t('process.rtfile.tbCol.order')" v-if="false"></el-table-column>
         <el-table-column prop="status" :label="t('process.rtfile.tbCol.status')">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('process.rtfile.tbCol.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creator')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creator')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.creatTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModifierId')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('process.rtfile.tbCol.ifdelete')" prop="isDeleted" v-if="false" />

         <el-table-column :label="t('quality.badCode.tbCol.operation')" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['quality:bcode:edit']">{{t('quality.badCode.button.edit')}}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['quality:bcode:remove']">{{t('quality.badCode.button.delete')}}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="120px">
            <el-form-item :label="t('quality.badCode.form.operationCode')" prop="operationCode" label-width="138px">
               <el-input v-model="form.operationCode" :placeholder="t('quality.badCode.form.operationCode')" />
            </el-form-item>
            <el-form-item :label="t('quality.badCode.form.badCode')" prop="badCode">
               <el-input v-model="form.badCode" :placeholder="t('quality.badCode.form.badCode')" />
            </el-form-item>
            <el-form-item :label="t('quality.badCode.form.badName')" prop="badName">
               <el-input v-model="form.badName" :placeholder="t('quality.badCode.form.badName')" />
            </el-form-item>
            <el-form-item :label="t('quality.badCode.form.badTypeCode')" prop="badTypeCode">
               <el-input v-model="form.badTypeCode" :placeholder="t('quality.badCode.form.badTypeCode')" />
            </el-form-item>
            <el-form-item :label="t('quality.badCode.form.badTypeName')" prop="badTypeName">
               <el-input v-model="form.badTypeName" :placeholder="t('quality.badCode.form.badTypeName')" />
            </el-form-item>
            <el-form-item :label="t('quality.badCode.form.status')">
               <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in base_is_open" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
               </el-radio-group>
            </el-form-item>
            <el-form-item :label="t('quality.badCode.form.remark')" prop="remark">
               <el-input v-model="form.Remark" type="textarea" rows="3" :placeholder="t('quality.badCode.form.remarkTip')" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('quality.badCode.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('quality.badCode.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Material">
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/quality/bcode";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      operationCode: [{ required: true, message: t('quality.badCode.message.alarm01'), trigger: "blur" }],
      badCode: [{ required: true, message: t('quality.badCode.message.alarm02'), trigger: "blur" }],
      badName: [{ required: true, message: t('quality.badCode.message.alarm03'), trigger: "blur" }],
      badTypeCode: [{ required: true, message: t('quality.badCode.message.alarm04'), trigger: "blur" }],
      badTypeName: [{ required: true, message: t('quality.badCode.message.alarm05'), trigger: "blur" }],
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1,
      remark: undefined
   };
   proxy.resetForm("submitRef");
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('quality.badCode.message.addTit');
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('quality.badCode.message.editTit');
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('quality.badCode.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('quality.badCode.message.delSuc'));
   }).catch(() => { });
}

/** 同步按钮操作 */
function handleDown() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('quality.badCode.message.syncSuc')+":"+response.data);
      }, 1500);
   });
}

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess(t('quality.badCode.message.importSuc'));
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('quality.badCode.message.editSuc'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('quality.badCode.message.addSuc'));
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>