<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="工序编号" prop="operationCode">
            <el-input v-model="queryParams.operationCode" placeholder="工序编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="不良代码" prop="badCode">
            <el-input v-model="queryParams.badCode" placeholder="不良代码" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['quality:bcode:add']">新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['quality:bcode:down']" :loading="downLoading">同步</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['quality:bcode:import']" :loading="importLoading">导入</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column label="工序编号" align="center" prop="operationCode" width="120" />
         <el-table-column label="不良代码" align="center" prop="badCode" />
         <el-table-column label="不良名称" align="center" prop="badName" />
         <el-table-column label="类型编码" align="center" prop="badTypeCode" />
         <el-table-column label="类型名称" align="center" prop="badTypeName" />
         <el-table-column label="启用" align="center" prop="enableStatus" width="80" />
         <el-table-column label="删除" align="center" prop="dataStatus" width="80" />
         <el-table-column label="新增时间" align="center" prop="addTime" width="155" />
         <el-table-column label="更新时间" align="center" prop="editTime" width="155" />

         <el-table-column prop="orderNum" label="排序" v-if="false"></el-table-column>
         <el-table-column prop="status" label="状态">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="备注" prop="remark" v-if="false" />
         <el-table-column label="创建者" prop="creatorId" v-if="false" />
         <el-table-column label="创建者" prop="creatorName" v-if="false" />
         <el-table-column label="创建时间" prop="creationTime" width="155" v-if="false" />
         <el-table-column label="处理人" prop="lastModifierId" v-if="false" />
         <el-table-column label="处理人" prop="lastModifierName" v-if="false" />
         <el-table-column label="处理时间" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column label="是否删除" prop="isDeleted" v-if="false" />

         <el-table-column label="操作" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['quality:bcode:edit']">编辑</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['quality:bcode:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="工序编号" prop="operationCode">
               <el-input v-model="form.operationCode" placeholder="工序编号" />
            </el-form-item>
            <el-form-item label="不良代码" prop="badCode">
               <el-input v-model="form.badCode" placeholder="不良代码" />
            </el-form-item>
            <el-form-item label="不良名称" prop="badName">
               <el-input v-model="form.badName" placeholder="不良名称" />
            </el-form-item>
            <el-form-item label="类型编码" prop="badTypeCode">
               <el-input v-model="form.badTypeCode" placeholder="类型编码" />
            </el-form-item>
            <el-form-item label="类型名称" prop="badTypeName">
               <el-input v-model="form.badTypeName" placeholder="类型名称" />
            </el-form-item>
            <el-form-item label="状态">
               <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in base_is_open" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
               </el-radio-group>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
               <el-input v-model="form.Remark" type="textarea" rows="3" placeholder="请输入内容" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Material">
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/quality/bcode";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      operationCode: [{ required: true, message: "请填写工序编号", trigger: "blur" }],
      badCode: [{ required: true, message: "请填写不良代码", trigger: "blur" }],
      badName: [{ required: true, message: "请填写不良名称", trigger: "blur" }],
      badTypeCode: [{ required: true, message: "请填写类型编码", trigger: "blur" }],
      badTypeName: [{ required: true, message: "请填写类型名称", trigger: "blur" }],
      status: [{ required: true, message: "请填写状态", trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      status: 1,
      remark: undefined
   };
   proxy.resetForm("submitRef");
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm('是否删除编号为"' + postIds + '"的数据项？').then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

/** 同步按钮操作 */
function handleDown() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess("同步成功,变更行数:"+response.data);
      }, 1500);
   });
}

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess("导入成功");
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess("编辑成功");
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>