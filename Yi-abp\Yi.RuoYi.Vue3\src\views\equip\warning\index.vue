<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('equip.equipWarning.queryField.lineCode')" prop="lineCode">
            <el-input v-model="queryParams.lineCode" :placeholder="t('equip.equipWarning.queryField.lineCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('equip.equipWarning.queryField.stationCode')" prop="stationCode">
            <el-input v-model="queryParams.stationCode" :placeholder="t('equip.equipWarning.queryField.stationCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('equip.equipWarning.queryField.machineCode')" prop="machineCode">
            <el-input v-model="queryParams.machineCode" :placeholder="t('equip.equipWarning.queryField.machineCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('equip.equipWarning.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('equip.equipWarning.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['process:bom:add']">{{t('equip.equipWarning.button.add')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['process:bom:down']" :loading="downLoading" v-if="false">{{t('equip.equipWarning.button.synchronous')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['process:bom:import']" :loading="importLoading" v-if="false">{{t('equip.equipWarning.button.import')}}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column align="center" label="编号" prop="id" v-if="false" />
         <el-table-column align="center" :label="t('equip.equipWarning.tbColumn.lineCode')" prop="lineCode"  />
         <el-table-column align="center" :label="t('equip.equipWarning.tbColumn.stationCode')" prop="stationCode" />
         <el-table-column align="center" :label="t('equip.equipWarning.tbColumn.resourceCode')" prop="machineCode" />
         <el-table-column align="center" :label="t('equip.equipWarning.tbColumn.machineCode')" prop="resourceCode" />
         <el-table-column align="center" :label="t('equip.equipWarning.tbColumn.deviceStatus')" prop="deviceStatus" />
         <el-table-column align="center" :label="t('equip.equipWarning.tbColumn.deviceAlarm')" prop="deviceAlarm" />         
         <el-table-column align="center" :label="t('equip.equipWarning.tbColumn.accumulatedPowerOnTime')" prop="accumulatedPowerOnTime" width="155" />
         <el-table-column align="center" :label="t('equip.equipWarning.tbColumn.accumulatedOperationTime')" prop="accumulatedOperationTime" width="155" />

         <el-table-column :label="t('equip.equipWarning.tbColumn.orderNum')" prop="orderNum" v-if="false"> </el-table-column>
         <el-table-column :label="t('equip.equipWarning.tbColumn.status')" prop="status" v-if="false">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('equip.equipWarning.tbColumn.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('equip.equipWarning.tbColumn.creatorId')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('equip.equipWarning.tbColumn.creatorName')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('equip.equipWarning.tbColumn.creationTime')" prop="creationTime" v-if="false" width="155" />
         <el-table-column :label="t('equip.equipWarning.tbColumn.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('equip.equipWarning.tbColumn.lastModifierName')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('equip.equipWarning.tbColumn.lastModificationTime')" prop="lastModificationTime" v-if="false" width="155" />
         <el-table-column :label="t('equip.equipWarning.tbColumn.isDeleted')" prop="isDeleted" v-if="false" />

         <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['process:bom:edit']">{{t('equip.equipWarning.button.edit')}}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['process:bom:remove']">{{t('equip.equipWarning.button.delete')}}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="700px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="140px">
            <el-form-item :label="t('equip.equipWarning.elForm.lineCode')" prop="lineCode">
               <el-input v-model="form.lineCode" :placeholder="t('equip.equipWarning.elForm.lineCode')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipWarning.elForm.stationCode')" prop="stationCode">
               <el-input v-model="form.stationCode" :placeholder="t('equip.equipWarning.elForm.stationCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipWarning.elForm.machineCode')" prop="machineCode">
               <el-input v-model="form.machineCode" :placeholder="t('equip.equipWarning.elForm.machineCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipWarning.elForm.resourceCode')" prop="resourceCode">
               <el-input v-model="form.resourceCode" :placeholder="t('equip.equipWarning.elForm.resourceCodeTip')" />
            </el-form-item>
            <el-form-item  :label="t('equip.equipWarning.elForm.deviceStatus')" prop="deviceStatus">
               <el-input v-model="form.deviceStatus" :placeholder="t('equip.equipWarning.elForm.deviceStatusTip')" />
            </el-form-item>
            <el-form-item  :label="t('equip.equipWarning.elForm.deviceAlarm')" prop="deviceAlarm">
               <el-input v-model="form.deviceAlarm" :placeholder="t('equip.equipWarning.elForm.deviceAlarmTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipWarning.elForm.accumulatedPowerOnTime')" prop="accumulatedPowerOnTime">
               <el-input v-model="form.accumulatedPowerOnTime" :placeholder="t('equip.equipWarning.elForm.accumulatedPowerOnTimeTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipWarning.elForm.accumulatedOperationTime')" prop="accumulatedOperationTime">
               <el-input v-model="form.accumulatedOperationTime" :placeholder="t('equip.equipWarning.elForm.accumulatedOperationTimeTip')" />
            </el-form-item>
            <el-form-item :label="t('equip.equipWarning.elForm.status')" prop="status" v-if="false">
               <el-select v-model="form.status">
                  <el-option v-for="dict in base_is_open"  :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
               </el-select>
            </el-form-item>
            <el-form-item :label="t('equip.equipWarning.elForm.remark')" prop="remark" v-if="false">
               <el-input v-model="form.remark"  type="textarea" rows="3" :placeholder="t('equip.equipWarning.elForm.RemarkTip')" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('equip.equipWarning.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('equip.equipWarning.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="EquipmentWarning">
/** 引入----------------------------------------------------------------------------*/
import { listDataAsync, listData, addData, delData, getData, updateData, getDown } from "@/api/equip/warning";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");


/** 结构定义----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      lineCode: [{ required: true, message: t('equip.equipWarning.message.alarmMsg01'), trigger: "blur" }],
      stationCode: [{ required: true, message: t('equip.equipWarning.message.alarmMsg02'), trigger: "blur" }],
      machineCode: [{ required: true, message: t('equip.equipWarning.message.alarmMsg03'), trigger: "blur" }],
      resourceCode: [{ required: true, message: t('equip.equipWarning.message.alarmMsg04'), trigger: "blur" }],
   }
});
const { form, queryParams, rules } = toRefs(data);

/** 列表----------------------------------------------------------------------------*/
/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置搜索条件 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 列表操作----------------------------------------------------------------------------*/
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 编辑页面操作----------------------------------------------------------------------------*/
/** 重置提交表单 */
function reset() {
   form.value = {
      id: undefined,
   };
   proxy.resetForm("submitRef");
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess(t('equip.equipWarning.message.editSuccess'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('equip.equipWarning.message.addSuccess'));
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 增删改按钮----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('equip.equipWarning.message.add');
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('equip.equipWarning.message.edit');
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('equip.equipWarning.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('equip.equipWarning.message.delSuccess'));
   }).catch(() => { });
}

/** 数据同步----------------------------------------------------------------------------*/
/** 同步按钮操作 */
// function handleDown() {
//    downLoading.value = true;
//    getDown().then(response => {
//       setTimeout(function() {  // 延迟1.5秒执行
//          downLoading.value =false;
//          proxy.$modal.msgSuccess("同步成功,变更行数:"+response.data);
//       }, 1500);
//    });
// }

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess(t('equip.equipWarning.message.importSuccess'));
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>