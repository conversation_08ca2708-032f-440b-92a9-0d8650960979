export default {
    login: {
        query: {
            ipAddress: "Endereço IP",
            userName: "Nome Usuário",
            onlineStatus: "Estado Online",
            logTime: "Tempo Login",
            startTime: "Tempo Início",
            endTime: "Tempo Fim",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            export: "Exportar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
            clean: "Limpar",
            unlock: "Desbloquear",
        },
        tbCol: {
            userName: "Nome Usuário",
            ipAddress: "Endereço IP",
            location: "Localização",
            system: "Sistema",
            browser: "Navegador",
            msg: "Mensagem",

            orderNum: "Ordenação",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Conta criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Conta modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            cleanMsg: "Se esvaziar todos os itens de dados?",
            cleanSuc: "Esvaziar com sucesso",
            unlockMsg: "Deseja desbloquear os itens de dados deste usuário?",
            unlockSuc: "Desbloquear com sucesso?",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",
        }
    },
    operation: {
        query: {
            optTime: "Tempo Operação",
            module: "Módulo Sistema",
            optType: "Tipo Operação",
            optUser: "Usuário Operação",

            startTime: "Tempo Início",
            endTime: "Tempo Fim",
        },
        button: {
            search: "Buscar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            export: "Exportar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
            clean: "Limpar",
            unlock: "Desbloquear",
            Close: "Fechar",

            details: "Detalhes",
        },
        tbCol: {
            module: "Módulo Sistema",
            optType: "Tipo Operação",
            requestMethod: "Método Solicitação",
            optUser: "Usuário Operação",
            ipAds: "Endereço IP",
            optTime: "Tempo Operação",

            orderNum: "Ordenação",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Conta criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Conta modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
            module: "Módulo Sistema",
            userInfo: "Informações Usuário",
            creationTime: "Tempo criação",
            routePath: "Caminho Rota",
            optData: "Dados Operação",
            errMsg: "Mensagem Erro",

            orderNum: "Ordenação",
            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor insira o conteúdo",
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",
            alarm11: "",
            alarm12: "",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",

            cleanMsg: "Deseja limpar todos os itens?",
            cleanSuc: "Limpeza bem-sucedida?",
        }
    },
    apiLog: {
        query: {
            serviceId: "Instância Serviço",
            status: "Estado",
            creationTime: "Tempo Criação",

            startTime: "Tempo Início",
            endTime: "Tempo Fim",
        },
        button: {
            search: "Pesquisar",
            reset: "Redefinir",
            add: "Adicionar",
            sync: "Sincronizar",
            import: "Importar",
            export: "Exportar",
            edit: "Editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
            clean: "Limpar",
            Close: "Fechar",
        },
        tbCol: {
            httpCount: "Contagem HTTP",
            UniqueCode: "Código Único",
            businessCode: "Código Negócios",
            businessName: "Nome Negócio",
            appId: "AppId",
            appKey: "AppKey",
            processData: "Processar Dados",
            sendData: "Enviar Dados",
            saveData: "Salvar Dados",
            httpPath: "Caminho HTTP",

            orderNum: "Ordenação",
            status: "Estado",
            remark: "Observação",
            creatorId: "Id criador",
            creatorName: "Conta criador",
            creationTime: "Tempo criação",
            lastModifierId: "Id modificador",
            lastModifierName: "Conta modificador",
            lastModificationTime: "Tempo modificação",
            isDeleted: "Excluído",
            operation: "Operação",
        },
        form: {
            status: "Estado",
            remark: "Observação",
            remarkTip: "Por favor, preencha o conteúdo",
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "Adicionar",
            addSuc: "Adicionado com sucesso",
            editTit: "Editar",
            editSuc: "Editado com sucesso",
            delMsg: "Deseja excluir este item de dados?",
            delSuc: "Excluído com sucesso",
            syncSuc: "Sincronizado com sucesso",
            importSuc: "Importado com sucesso",
        }
    },
    online: {
    }
}