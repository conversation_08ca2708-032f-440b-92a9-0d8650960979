export default {
    dictionary: {
        queryField: {
            dictName: "Dicionário Nome",
            dictype: "Dicionário Tipo",
            state: "Estado",
            creattime: "Tempo Criação",
            search: "Procurar",
            tipContent: "Por favor, insira um nome de dicionário",
            tipType: "Por favor, insira um tipo de dicionário",
            tipState: "Estado Dicionário",
            startTime: "Hora Início",
            endTime: "Hora Fim",
        },
        tbColumn: {
            dictName: "Nome chinês",
            dicEngName: "Nome Inglês",
            dicPtName: "Nome Português",
            dicttype: "Tipo Dicionário",
            state: "Estado",
            remark: "Notas",
            creationTime: "Tempo Criação",
            operate: "Operar",
            edit: "Editar",
            delete: "Apagar",
        },
        button: {
            search: "Procurar",
            reset: "Reset",
            increase: "Aumento",
            edit: "Editar",
            delete: "Apagar",
        },
        elForm: {
            dictName: "Nome chinês",
            chinaTips: "Dica: Indique por favor o nome do dicionário",
            dictEngName: "Nome Inglês",
            engTips: "Dica: Por favor insira um nome inglês",
            dictPtName: "Nome Português",
            ptTips: "Dica: Por favor insira um nome português",
            dictType: "Tipo Dicionário",
            tipType: "Dica: Indique por favor o tipo de dicionário",
            state: "Estado",
            remark: "Notas",
            tipContent: "Indique por favor o conteúdo",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        message: {
            addTitle: "Adicionar Tipo Dicionário",
            addSuccess: "Adicionar Sucesso",
            editTitle: "Editar Tipo Dicionário",
            editSuccess: "Editar Sucesso",
            delMsg: "Você tem certeza de excluir o item selecionado?",
            delSuccess: "apagar Sucesso",
            refreshSuccess: "Atualizar Sucesso",
            alarm01: "O nome do dicionário não pode estar vazio",
            alarm02: "O tipo de dicionário não pode estar vazio",
        }
    },
    dictionaryData: {
        queryField: {
            dictType: "Nome Dicionário",
            dictLabel: "Rótulo Dicionário",
            labelTip: "Indique por favor o rótulo do dicionário",
            state: "Estado",
        },
        tbColumn: {
            chiDictags: "Rótulo Chinês",
            engDictags: "Rótulo Inglês",
            ptDictags: "Rótulo Português",
            dicKey: "Chave Valor",
            dicSort: "Ordenação",
            state: "Estado",
            remark: "Notas",
            creaTime: "Tempo Criação",
            operate: "Operar",
            edit: "Editar",
            delete: "Apagar",
        },
        button: {
            search: "procurar",
            reset: "reset",
            add: "aumento",
            edit: "editar",
            delete: "apagar",
            download: "download",
            close: "fechar",
            confirm: "confirmar",
            cancel: "cancelar",
        },
        elForm: {
            dicType: "Tipo Dicionário",
            chineseLabels: "Rótulo Chinês",
            englishLabels: "Rótulo Inglês",
            labelPortuguese: "Rótulo Portuguesa",
            keyValue: "Chave Valor",
            styleAttributes: "Estilo Atributo",
            displaySorting: "Classificação",
            echoStyle: "Estilo Echo",
            state: "Estado",
            remark: "Notas",
            chinaTip: "Indique por favor o rótulo chinês",
            engTip: "Introduza uma rótulo inglesa",
            ptTip: "Indique a rótulo portuguesa",
            keyTip: "Indique a rótulo portuguesa",
            attriTip: "Indique por favor os atributos do estilo",
            contentTip: "Indique por favor o conteúdo",
        },
        message: {
            alarm01: "O rótulo dos dados não pode estar vazio",
            alarm02: "O valor da chave de dados não pode estar vazio",
            alarm03: "A ordem dos dados não pode estar vazia",
            add_title: "Adicionar Dicionário Dados",
            add_msg: "Adicionar Sucesso",
            edit_title: "Editar Dicionário Dados",
            edit_msg: "Editar Sucesso",
            del_tip: "Você tem certeza de excluir o item de dados selecionado?",
            del_msg: "Excluir Sucesso",
        },
    },
    factoryModel: {
        queryField: {
            modelNumber: "Número Modelo",
            modelType: "Tipo Modelo",

        },
        tbColumn: {
            parentModel: "Modelo Pai",
            modelName: "Nome Modelo",
            modelNumber: "Número Modelo",
            order: "Ordem",
            modelType: "Tipo Modelo",
            modelState: "Estado Modelo",
            nameTip: "Indique por favor o nome da modelo",
            numberTip: "Número de modelo",
            typeTip: "Indique por favor o tipo de modelo",
        },
        button: {
            search: "Procurar",
            reset: "redefinir",
            expandCollapse: "Expandir/Recolher",
            add: "Aumento",
            synchronous: "Síncrono",
            Import: "Importação",
            edit: "Editar",
            delete: "Apagar",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        elForm: {
            modelName: "Nome da Modelagem",
            modelNumber: "Número de modelo",
            modelType: "Tipo de modelo",
            parentModelCode: "Número do Pai",
            mesStart: "MES activado",
            mesDelete: "Remoção do MES",
            addTime: "Adicionar tempo",
            changeTime: "Mudar a hora",
            order: "sort",
            state: "estado",
            remark: "notas",
            creater: "criador",
            creatTime: "Tempo de criação",
            processors: "Pessoal de transformação",
            processTime: "Tempo de processamento",
            select: "Deseja apagá-lo",
            operate: "operar",
        },
        message: {
            alarm01: "O número de modelagem não pode estar vazio",
            alarm02: "O nome da modelagem não pode estar vazio",
            editAlarm: "Editado com sucesso",
            addAlarm: "Novo adicionado com sucesso",
            deleteAlarm: "Apagar com sucesso",
            importAlarm: "A importação foi bem sucedida",
            ifDelete: "Tem a certeza de apagar o nome",
            success: "Sincronização bem sucedida, alterar o número de linhas",
            ifData: "O item de dados?",
            add_title: "Adicionar",
            edit_title: "Editar",
            submit_alarm01: "O modelo atual não pode ser usado como seu próprio superior.",
            submit_alarm02: "Repetição do código, Não é permitido salvar",
            submit_alarm03: "Repetição do código, Não é permitido salvar",
        },
    },
    deptManage: {
        queryField: {
            deptName: "Nome do Departamento",
            nameTip: "Indique por favor o nome do departamento",
            depCode: "Número do Departamento",
            codeTip: "Indique o número do departamento",
            state: "estado",
            deptState: "Estatuto do departamento",
        },
        tbColumn: {
            deptName: "Nome do Departamento",
            deptCode: "Número do Departamento",
            deptNumber: "sort",
            state: "estado",
            creatTime: "Tempo de criação",
            operate: "operar",
        },
        button: {
            search: "procurar",
            reset: "reset",
            add: "aumento",
            show: "Expandir/Recolher",
            edit: "editar",
            delete: "apagar",
            confirm: "confirm",
            cancel: "cancel",
        },
        elForm: {
            superiorDept: "Departamento Superior",
            superiorSelect: "Seleccionar o departamento superior",
            deptName: "Nome do Departamento",
            nameTip: "Indique por favor o nome do departamento",
            deptNumber: "Número do Departamento",
            numberTip: "Indique o número do departamento",
            order: "Mostrar a classificação",
            leader: "Responsável",
            leaderTip: "Por favor, indique a pessoa responsável",
            deptState: "Estatuto do departamento",
        },
        message: {
            alarm01: "O número do departamento não pode estar vazio",
            alarm02: "O nome do departamento não pode estar vazio",
            alarm03: "A classificação não pode estar vazia",
            editSuccess: "Editado com sucesso",
            addSucess: "Aumentar o sucesso",
            ifDelete: "Tem a certeza de apagar o nome",
            ifData: "Rubrica de dados",
            deleteSucess: "Apagar com sucesso",
            add_title: "Adicionar departamento",
            edit_title: "Editar Department",
            edit_msg01: "O Departamento atual não pode ser usado como seu próprio superior.",
        },
    },
    post: {
        queryField: {
            postCode: "Código da tarefa",
            codeTip: "Indique por favor o código da tarefa",
            postName: "Posição",
            nameTip: "Indique por favor o título da função",
            state: "estado",
            postState: "Situação da posição",

        },
        tbColumn: {
            postCode: "Código da Código do Departamento",
            postName: "Posição",
            postOrder: "Classificação das Tarefas",
            state: "estado",
            creatTime: "Tempo de criação",
            operate: "operar",

        },
        button: {
            search: "procurar",
            reset: "reset",
            add: "aumento",
            edit: "editar",
            delete: "apagar",
            download: "download",
            confirm: "certo",
            cancel: "cancelar",


        },
        elForm: {
            postName: "Posição",
            nameTip: "Indique por favor o título da função",
            postCode: "Código da tarefa",
            codeTip: "Indique por favor o nome de código",
            postOrder: "Sequência de posição",
            postState: "Situação da posição",
            remark: "notas",
            remarkTip: "Introduza uma nota",

        },
        message: {
            alarm01: "O nome da posição não pode estar vazio",
            alarm02: "O código da posição não pode estar vazio",
            alarm03: "A ordem das posições não pode estar vazia",
            editSuccess: "Editado com sucesso",
            addSucess: "Novo adicionado com sucesso",
            ifDelete: "Tem a certeza de apagar o número da posição",
            ifData: "O item de dados?",
            deleteSucess: "Apagar com sucesso",

        },

    },
    material: {
        queryField: {
            materialCode: "Material Código",
            pnCode: "Número Peça",
        },
        tbColumn: {
            id: "número",
            materialCode: "Material Código",
            materialName: "Material Nome",
            materialType: "Material Tipo",
            materialVersion: "Material Versão",
            pnCode: "PN Número",
            pnShortCode: "PN Código",
            pnName: "PN Nome",
            measureUnitCode: "Medição Unidade Código",
            measureUnitName: "Medição Unidade Nome",
            measureUnitQuantity: "Medição Unidade Quantidade",
            regular: "Regular",
            productModelCode: "Produto Modelo Código",
            effactTime: "Data Efectiva",
            enableStatus: "Estado Material",
            dataStatus: "Estado Dos Dados",
            addTime: "Adicionar Tempo",
            editTime: "Actualização Tempo",
            oldSortID: "Antigo ID",
            newSortID: "Novo ID",
            order: "Sort",
            status: "Estado",
            remark: "Notas",
            creator: "Criador",
            creatTime: "Criação Tempo",
            lastModifierId: "Transformação Pessoal",
            lastModificationTime: "Processamento Tempo",
            ifdelete: "Deseja Apagá-lo",
            operate: "Operar",

        },
        button: {
            search: "procurar",
            reset: "reset",
            add: "aumento",
            synchronous: "síncrona",
            import: "importação",
            edit: "editar",
            delete: "apagar",
            confirm: "confirmar",
            cancel: "cancelar",
        },
        elForm: {
            materialCode: "Material Código",
            codeTip: "Material Código",
            materialName: "Material Nome",
            nameTipe: "Material Nome",
            materialType: "Material Tipo",
            typeTip: "Material Tipo",
            materialVersion: "Material Versão",
            versionTip: "Material Versão",
            pnCode: "Número",
            pncodeTip: "Número",
            pnName: "Nome",
            pnNameTip: "Nome",
            pnShortCode: "Código",
            pnShortCodeTip: "Código",
            measureUnitCode: "Medição Código",
            measureUnitCodeTip: "Medição Código",
            measureUnitName: "Medida  Nome",
            measureUnitNameTip: "Medida  Nome",
            measureUnitQuantity: "Medida Quantidade",
            measureUnitQuantityTip: "Quantidade Unitária",
            regular: "Regular",
            regularTip: "Regular",
            productModelCode: "Produção Modo",
            productModelCodeTip: "Produção Modo",
            effactTime: "Efectiva Data",
            state: "Estado",
            remark: "Notas",
            remarkTip: "Indique por favor o conteúdo",
        },
        message: {
            codeAlarm: "Preencha o código do material",
            nameAlarm: "Preencha o nome do material",
            typeAlarm: "Preencha o tipo de instalação da lâmpada",
            pnAlarm: "Indicar o número Pn",
            measureUnitNameAlarm: "Preencher a unidade de medida",
            measureUnitQuantityAlarm: "Preencher a unidade de medida",
            timeAlarm: "Seleccione por favor a hora efectiva",
            ifdelete: "Deseja apagar o número",
            ifDAta: "O item de dados?",
            deleteAlarm: "Apagar com sucesso",
            successAlarm: "Sincronização bem sucedida, alterar o número de linhas",
            editAlarm: "Editado com sucesso",
            addAlarm: "Novo adicionado com sucesso",
            importAlarm: "A importação foi bem sucedida",
        },

    },
    pskill: {
        queryField: {
            empCode: "funcionário Código",
            empCodeTip: "funcionário Código",
            certificateNo: "Certificado No.",
            certificateNoTip: "Certificado No.",
        },
        tbColumn: {
            num: "Numeração",
            empCode: "funcionário Código",
            operationCode: "operação Código",
            certificateNo: "Certificado No.",
            controlStatusCode: "Controle Estado Código",
            skillId: "ID habilidade",
            skillLevelCode: "Habilidade Nível Código",
            skillStartDate: "Habilidade Início Data",
            skillValidity: "Habilidade Validade",
            enableStatus: "Estado ativado",
            dataStatus: "Estado dados",
            addTime: "Adicionar Data",
            editTime: "Atualizado Data",
            orderNum: "Número pedido",
            status: "Estado",
            remark: "comentário",
            creatorId: "Criador Id",
            creatorName: "Criador Nome",
            creationTime: "Tempo criação",
            lastModifierId: "Último ID modificador",
            lastModifierName: "Último Nome modificador",
            lastModificationTime: "Tempo Modificação",
            isDeleted: "Apagado",
            operate: "Operar",

        },
        button: {
            search: "Procurar",
            reset: "repor",
            add: "aumento",
            synchronous: "síncrono",
            import: "Importação",
            edit: "editar",
            delete: "Excluir",
            confirm: "Confirmar",
            cancel: "Cancelar",
        },
        elForm: {
            empCode: "Código funcionário",
            empCodeTip: "Código funcionário",
            operationCode: "Código operação",
            operationCodeTip: "Código operação",
            certificateNo: "Número certificado",
            certificateNoTip: "Número certificado",
            controlStatusCode: "Código estado",
            controlStatusCodeTip: "Código estado",
            skillLevelCode: "Código nível",
            skillLevelCodeTip: "Código nível",
            skillStartDate: "Eficaz Data",
            skillValidity: "Habilidade Validez",
            skillValidityTip: "Habilidade Validez",
            state: "Estado",
            remark: "Observação",
            remarkTip: "Por favor, insira seu conteúdo",
        },
        message: {
            alarm01: "Por favor, preencha o código do funcionário",
            alarm02: "Preencha o número do certificado",
            alarm03: "Por favor, preencha o código de status de controle",
            alarm04: "Por favor, preencha o ID da habilidade",
            alarm05: "Por favor, preencha o código do nível de habilidade",
            alarm06: "Por favor, preencha a data de vigência",
            alarm07: "Selecione um período de validade",
            ifDelete: "Deseja excluir o item de dados?",
            deleteSucess: "A exclusão foi bem-sucedida",
            success: "A sincronização é bem-sucedida e o número de linhas é alterado:",
            editSuccess: "Editado com sucesso",
            addSucess: "O novo é bem-sucedido",

        },
    },
}