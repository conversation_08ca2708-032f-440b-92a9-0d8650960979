export default {
    dictionary: {
        queryField: {
            dictName: "字典名称",
            dictype: "字典类型",
            state: "状态",
            creattime: "创建时间",
            search: "搜索",
            tipContent: "请输入字典名称",
            tipType: "请输入字典类型",
            tipState: "字典状态",
            startTime: "开始时间",
            endTime: "结束时间",
        },
        tbColumn: {
            dictName: "中文名称",
            dicEngName: "英文名称",
            dicPtName: "葡语名称",
            dicttype: "字典类型",
            state: "状态",
            remark: "备注",
            creationTime: "创建时间",
            operate: "操作",
            edit: "编辑",
            delete: "删除"
        },
        button: {
            search: "搜索",
            reset: "重置",
            increase: "新增",
            edit: "编辑",
            delete: "删除",
        },
        elForm: {
            dictName: "字典名称",
            chinaTips: "提示：请输入字典名称",
            dictEngName: "英文名称",
            engTips: "提示：请输入英文名称",
            dictPtName: "葡语名称",
            ptTips: "提示：请输入葡语名称",
            dictType: "字典类型",
            tipType: "提示：请输入字典类型",
            state: "状态",
            remark: "备注",
            tipContent: "请输入内容",
            confirm: "确认",
            cancel: "取消",
        },
        message: {
            addTitle: "新增字典类型",
            addSuccess: "新增成功",
            editTitle: "编辑字典类型",
            editSuccess: "编辑成功",
            delMsg: "确认是否删除选中的数据项？",
            delSuccess: "删除成功",
            refreshSuccess: "刷新成功",
            alarm01: "字典名称不能为空",
            alarm02: "字典类型不能为空",
        },
    },
    dictionaryData: {
        queryField: {
            dictType: "字典名称",
            dictLabel: "字典标签",
            labelTip: "请输入字典标签",
            state: "状态",
        },
        tbColumn: {
            chiDictags: "中文字典标签",
            engDictags: "英文字典标签",
            ptDictags: "葡语字典标签",
            dicKey: "字典值",
            dicSort: "字典排序",
            state: "状态",
            remark: "备注",
            creaTime: "创建时间",
            operate: "操作",
            edit: "编辑",
            delete: "删除",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            edit: "编辑",
            delete: "删除",
            download: "导出",
            close: "关闭",
            confirm: "确认",
            cancel: "取消",
        },
        elForm: {
            dicType: "字典类型",
            chineseLabels: "中文数据标签",
            englishLabels: "英文标签",
            labelPortuguese: "葡语标签",
            keyValue: "数据键值",
            styleAttributes: "样式属性",
            displaySorting: "显示排序",
            echoStyle: "回显样式",
            state: "状态",
            remark: "备注",
            chinaTip: "请输入中文标签",
            engTip: "请输入英文标签",
            ptTip: "请输入葡语标签",
            keyTip: "请输入数据键值",
            attriTip: "请输入样式属性",
            contentTip: "请输入内容",
        },
        message: {
            alarm01: "数据标签不能为空",
            alarm02: "数据键值不能为空",
            alarm03: "数据顺序不能为空",
            add_title: "添加字典数据",
            add_msg: "新增成功",
            edit_title: "编辑字典数据",
            edit_msg: "编辑成功",
            del_tip: "是否确认删除选择的数据项？",
            del_msg: "删除成功",
        },
    },
    factoryModel: {
        queryField: {
            modelNumber: "建模编号",
            modelType: " 建模类型",
        },
        tbColumn: {
            parentModel: "父建模",
            modelName: "建模名称",
            modelNumber: "建模编号",
            order: "排序",
            modelType: "建模类型",
            modelState: "建模状态",
            nameTip: "请输入建模名称",
            numberTip: "建模编号",
            typeTip: "请输建模类型",
        },
        button: {
            search: "搜索",
            reset: "重置",
            expandCollapse: "展开/折叠",
            add: "新增",
            synchronous: "同步",
            Import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        elForm: {
            modelName: "建模名称",
            modelNumber: "建模编号",
            modelType: "建模类型",
            parentModelCode: "父编号",
            mesStart: "MES启用",
            mesDelete: "MES删除",
            addTime: "新增时间",
            changeTime: "变更时间",
            order: "排序",
            state: "状态",
            remark: "备注",
            creater: "创建者",
            creatTime: "创建时间",
            processors: "处理人",
            processTime: "处理时间",
            select: "是否删除",
            operate: "操作",
        },
        message: {
            alarm01: "建模编号不能为空",
            alarm02: "建模名称不能为空",
            editAlarm: "编辑成功",
            addAlarm: "新增成功",
            deleteAlarm: "删除成功",
            importAlarm: "导入成功",
            ifDelete: "是否确认删除名称为",
            success: "同步成功,变更行数",
            ifData: "的数据项?",
            add_title: "新增",
            edit_title: "编辑",
            submit_alarm01: "当前建模不可以作为自己的上级",
            submit_alarm02: "编号重复,不允许保存",
            submit_alarm03: "编号重复,不允许保存",
        },

    },
    deptManage: {
        queryField: {
            deptName: "部门名称",
            nameTip: "请输入部门名称",
            depCode: "部门编号",
            codeTip: "请输入部门编号",
            state: "状态",
            deptState: "部门状态",
        },
        tbColumn: {
            deptName: "部门名称",
            deptCode: "部门编号",
            deptNumber: "排序",
            state: "状态",
            creatTime: "创建时间",
            operate: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            show: "展开/折叠",
            edit: "编辑",
            delete: "删除",
            confirm: "确定",
            cancel: "取消",
        },
        elForm: {
            superiorDept: "上级部门",
            superiorSelect: "请选择上级部门",
            deptName: "部门名称",
            nameTip: "请输入部门名称",
            deptNumber: "部门编号",
            numberTip: "请输入部门编号",
            order: "显示排序",
            leader: "负责人",
            leaderTip: "请输入负责人",
            deptState: "部门状态",
        },
        message: {
            alarm01: "部门编号不能为空",
            alarm02: "部门名称不能为空",
            alarm03: "显示排序不能为空",
            editSuccess: "编辑成功",
            addSucess: "新增成功",
            ifDelete: "是否确认删除名称为",
            ifData: "的数据项",
            deleteSucess: "删除成功",
            add_title: "添加部门",
            edit_title: "编辑部门",
            edit_msg01: "当前组织不可以作为自己的上级",
        },
    },
    post: {
        queryField: {
            postCode: "岗位编码",
            codeTip: "请输入岗位编码",
            postName: "岗位名称",
            nameTip: "请输入岗位名称",
            state: "状态",
            postState: "岗位状态",

        },
        tbColumn: {
            postCode: "岗位编码",
            postName: "岗位名称",
            postOrder: "岗位排序",
            state: "状态",
            creatTime: "创建时间",
            operate: "操作",

        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            edit: "编辑",
            delete: "删除",
            download: "导出",
            confirm: "确定",
            cancel: "取消",

        },
        elForm: {
            postName: "岗位名称",
            nameTip: "请输入岗位名称",
            postCode: "岗位编码",
            codeTip: "请输入编码名称",
            postOrder: "岗位顺序",
            postState: "岗位状态",
            remark: "备注",
            remarkTip: "请输入备注",

        },
        message: {
            alarm01: "岗位名称不能为空",
            alarm02: "岗位编码不能为空",
            alarm03: "岗位顺序不能为空",
            editSuccess: "编辑成功",
            addSucess: "新增成功",
            ifDelete: "是否确认删除岗位编号为",
            ifData: "的数据项？",
            deleteSucess: "删除成功",

        },

    },
    material: {
        queryField: {
            materialCode: "物料编号",
            pnCode: "零件编号",
        },
        tbColumn: {
            id: "编号",
            materialCode: "物料编号",
            materialName: "物料名称",
            materialType: "物料类型",
            materialVersion: "物料版本",
            pnCode: "PN编号",
            pnShortCode: "PN代码",
            pnName: "PN名称",
            measureUnitCode: "计量编码",
            measureUnitName: "计量单位",
            measureUnitQuantity: "计量数量",
            regular: "正则",
            productModelCode: "生产模式",
            effactTime: "生效日期",
            enableStatus: "物料状态",
            dataStatus: "数据状态",
            addTime: "新增时间",
            editTime: "更新时间",
            oldSortID: "旧ID",
            newSortID: "新ID",
            order: "排序",
            status: "状态",
            remark: "备注",
            creator: "创建者",
            creatTime: "创建时间",
            lastModifierId: "处理人",
            lastModificationTime: "处理时间",
            ifdelete: "是否删除",
            operate: "操作",

        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            synchronous: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确定",
            cancel: "取消",

        },
        elForm: {
            materialCode: "物料编码",
            codeTip: "物料编码",
            materialName: "物料名称",
            nameTip: "物料名称",
            materialType: "物料类型",
            typeTip: "物料类型",
            materialVersion: "物料版本",
            versionTip: "物料版本",
            pnCode: "编号",
            pncodeTip: "编号",
            pnName: "名称",
            pnNametIP: "名称",
            pnShortCode: "代码",
            pnShortCodeTip: "代码",
            measureUnitCode: "计量编码",
            measureUnitCodeTip: "计量编码",
            measureUnitName: "计量单位",
            measureUnitNameTip: "计量单位",
            measureUnitQuantity: "计量数量",
            measureUnitQuantityTip: "单位数量",
            regular: "正则",
            regularTip: "正则",
            productModelCode: "生产模式",
            productModelCodeTip: "生产模式",
            effactTime: "生效日期",
            state: "状态",
            remark: "备注",
            remarkTip: "请输入内容",

        },
        message: {
            codeAlarm: "请填写物料编码",
            nameAlarm: "请填写物料名称",
            typeAlarm: "请填写安灯类型",
            pnAlarm: "请填写Pn号",
            measureUnitNameAlarm: "请填写计量单位",
            measureUnitQuantityAlarm: "请填写计量单位数量",
            timeAlarm: "请选择生效时间",
            ifdelete: "是否删除编号为",
            ifDAta: "的数据项？",
            deleteAlarm: "删除成功",
            successAlarm: "同步成功,变更行数",
            editAlarm: "编辑成功",
            addAlarm: "新增成功",
            importAlarm: "导入成功",

        },

    },
    pskill: {
        queryField: {
            empCode: "员工工号",
            empCodeTip: "员工工号",
            certificateNo: "证书编号",
            certificateNoTip: "证书编号",

        },
        tbColumn: {
            num: "编号",
            empCode: "员工工号",
            operationCode: "工序编号",
            certificateNo: "证书编号",
            controlStatusCode: "状态编码",
            skillId: "技能Id",
            skillLevelCode: "等级编码",
            skillStartDate: "生效日期",
            skillValidity: "有效时长",
            enableStatus: "启用状态",
            dataStatus: "数据状态",
            addTime: "新增时间",
            editTime: "更新时间",
            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建者",
            creatorName: "创建者",
            creationTime: "创建时间",
            lastModifierId: "处理人",
            lastModifierName: "处理人",
            lastModificationTime: "处理时间",
            isDeleted: "是否删除",
            operate: "操作",

        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            synchronous: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        elForm: {
            empCode: "员工工号",
            empCodeTIP: "员工工号",
            operationCode: "工序编号",
            operationCodeTip: "工序编号",
            certificateNo: "证书编号",
            certificateNoTip: "证书编号",
            controlStatusCode: "状态编码",
            controlStatusCodeTip: "状态编码",
            skillLevelCode: "等级编码",
            skillLevelCodeTip: "等级编码",
            skillStartDate: "生效日期",
            skillValidity: "有效时长",
            skillValidityTip: "有效时长",
            state: "状态",
            remark: "备注",
            remarkTip: "请输入内容",

        },
        message: {
            alarm01: "请填写员工工号",
            alarm02: "请填写证书编号",
            alarm03: "请填写控制状态编码",
            alarm04: "请填技能Id",
            alarm05: "请填写技能等级编号",
            alarm06: "请填生效日期",
            alarm07: "请选择有效时长",
            ifDelete: "是否删除该数据项？",
            deleteSucess: "删除成功",
            success: "同步成功,变更行数:",
            editSuccess: "编辑成功",
            addSucess: "新增成功",

        }
    }
}