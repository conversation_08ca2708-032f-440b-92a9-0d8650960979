import request from '@/utils/request'

// 分页查询
export function listData(query) {
  return request({
    url: '/andon/GetUntreatedListAsync',
    method: 'get',
    params: query
  })
}

// id查询
export function getData(id) {
  return request({
    url: `/andon/${id}`,
    method: 'get'
  })
}

// 新增
export function addData(data) {
  return request({
    url: '/andon',
    method: 'post',
    data: data
  })
}

// 编辑
export function updateData(id, data) {
  return request({
    url: `/andon/${id}`,
    method: 'put',
    data: data
  })
}

// 编辑
export function updateAdAsync(id, data) {
  return request({
    url: `/andon/updateAdAsync/${id}`,
    method: 'put',
    params: data
  })
}

// 删除
export function delData(ids) {
  return request({
    url: `/andon`,
    method: 'delete',
    params: { id: ids }
  })
}
