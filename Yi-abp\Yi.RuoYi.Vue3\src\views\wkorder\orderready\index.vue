<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="工单编号" prop="stationCode">
            <el-input v-model="queryParams.stationCode" placeholder="工单编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="SN编号" prop="stationCode">
            <el-input v-model="queryParams.stationCode" placeholder="SN编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5" v-if="false">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['andon:type:add']">新增</el-button>
         </el-col>
         <el-col :span="1.5" v-if="false">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['andon:type:edit']">编辑</el-button>
         </el-col>
         <el-col :span="1.5" v-if="false">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['andon:type:remove']">删除</el-button>
         </el-col>
         <el-col :span="1.5" v-if="false">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['andon:type:export']">同步数据</el-button>
         </el-col>
         <el-col :span="1.5" v-if="false">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['andon:type:export']">Excel导入</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="30" align="center" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column label="工单编号" align="center" prop="" />
         <el-table-column label="工单类型" align="center" prop="" />
         <el-table-column label="排程编号" align="center" prop="" />
         <el-table-column label="产品编号" align="center" prop="" />
         <el-table-column label="产品版本" align="center" prop="" />
         <el-table-column label="BOM编码" align="center" prop="" />
         <el-table-column label="BOM版本" align="center" prop="" />
         <el-table-column label="车间编号" align="center" prop="" />
         <el-table-column label="产线编号" align="center" prop="" />
         <el-table-column label="工站编码" align="center" prop="" />
         <el-table-column label="SN编号" align="center" prop="" />
         <el-table-column label="工序编码" align="center" prop="" />
         <el-table-column label="工序名称" align="center" prop="" />
         <el-table-column label="不良代码" align="center" prop="" />
         <el-table-column label="不良数量" align="center" prop="" />
         <el-table-column label="状态" align="center" prop="" />
         <el-table-column label="备注" align="center" prop="remark" />
         <!-- <el-table-column label="创建者" align="center" prop="creatorId" /> -->
         <el-table-column label="创建者" align="center" prop="creatorName" />
         <el-table-column label="创建时间" align="center" prop="CreationTime" width="150" />
         <el-table-column label="审核人" align="center" prop="CreationTime" />
         <el-table-column label="审核结果" align="center" prop="CreationTime" />
         <el-table-column label="审核时间" align="center" prop="CreationTime" width="150" />

         <!-- <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:dept:edit']">编辑</el-button>
               <el-button link icon="Plus" @click="handleAdd(scope.row)" v-hasPermi="['system:dept:add']">新增</el-button>
               <el-button v-if="scope.row.parentId != 0" link icon="Delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['system:dept:remove']">删除</el-button>
            </template>
</el-table-column> -->
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="500px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="等级" prop="andonLevel">
               <el-input-number v-model="form.andonLevel" controls-position="right" :min="0" />
            </el-form-item>
            <el-form-item label="状态" prop="andonStatus">
               <el-select v-model="form.andonStatus">
                  <el-option v-for="dict in sys_manage_state" :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
               </el-select>
            </el-form-item>
            <el-form-item label="备注" prop="remark">
               <el-input v-model="form.Remark" type="textarea" rows="3" placeholder="请输入内容" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Andon">
import { listData, addData, delData, getData, updateData, updateAdAsync } from "@/api/andon/untreated";

const { proxy } = getCurrentInstance();
const { sys_manage_state } = proxy.useDict("sys_manage_state");
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const tpForm = ref({});

const data = reactive({
   form: {},

   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      andonTypeCode: [{ required: true, message: "请选择安灯类型", trigger: "blur" }],
      andonLevel: [{ required: true, message: "请填写等级", trigger: "blur" }],
      andonStatus: [{ required: true, message: "安灯状态必选", trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listData(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      andonTypeCode: "",
      andonTypeName: ""
   };
 
   proxy.resetForm("submitRef");
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}
/** 安灯状态下拉列表变动 */
function selChange(row) {
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      tpForm.value = response.data;
      tpForm.value.andonStatus = row.andonStatus;
      if (tpForm.value.andonTypeCode == undefined) {
         tpForm.value.andonTypeCode = "";
      }
      if (tpForm.value.andonTypeName == undefined) {
         tpForm.value.andonTypeName = "";
      }
   }).catch(() => {
      // proxy.$modal.msgSuccess("编辑成功");
   }).finally(
      () => {
         updateData(tpForm.value.id, tpForm.value).then(response => {
            proxy.$modal.msgSuccess("编辑成功");
            getList();
         });
      })
}


/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess("编辑成功");
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id;
   proxy.$modal.confirm('是否删除编号为"' + postIds + '"的数据项？').then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   proxy.download("andon/type/export", {
      ...queryParams.value
   }, `post_${new Date().getTime()}.xlsx`);
}

getList();
</script>