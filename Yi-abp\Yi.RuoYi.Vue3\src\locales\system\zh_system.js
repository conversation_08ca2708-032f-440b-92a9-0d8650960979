export default {
    user: {
        query: {
            deptName: "部门名称",
            userName: "登录账号",
            phone: "手机号码",
            userState: "用户状态",
            creationTime: "创建时间",
            creationTime1: "开始时间",
            creationTime2: "结束时间",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            export: "导出",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
            resetPwd: "重置密码",
        },
        tbCol: {
            userId: "用户Id",
            userName: "登录账号",
            nickName: "用户昵称",
            gender: "性别",
            deptName: "部门名称",
            phone: "手机号码",
            userState: "用户状态",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            nickName: "用户昵称",
            deptName: "部门名称",
            phone: "手机号码",
            e_mail: "邮箱",
            userName: "登录账号",
            password: "密码",
            gender: "性别",
            userState: "用户状态",
            posts: "岗位",
            roles: "角色",
            creationTime: "创建时间",
            plsChoice: "请选择",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",

            dragFile: "将文件拖到此处，或",
            clickUpload: "点击上传",
            onlyAllowImport: "仅允许导入xls、xlsx格式文件",
            downloadTemplate: "下载模板",
        },
        message: {
            alarm01: "用户账号不能为空",
            alarm02: "用户名长度必须介于 2 和 20 之间",
            alarm03: "用户昵称不能为空",
            alarm04: "请输入正确的邮箱地址",
            alarm05: "请输入手机号码",

            enable: "启用",
            disable: "禁用",
            useTips1: "确认要",
            useTips2: "用户么",
            useTips3: "成功",

            pwdTips1: "请输入的新密码:",
            pwdTips2: "提示",
            pwdTips3: "确定",
            pwdTips4: "取消",
            pwdTips5: "用户密码长度必须介于 5 和 20 之间",
            pwdTips6: "编辑成功，新密码是:",

            importTips: "用户导入",
            importTips1: "导入结果",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "确认要删除该数据项吗？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
    authRole: {
        basicInfo: "基本信息",
        nickName: "用户昵称",
        userName: "登录账号",
        roleInfo: "角色信息",
        roleId: "角色Id",
        roleName: "角色名称",
        roleKey: "权限字符",
        createTime: "创建时间",
        submit: "提交",
        goBack: "返回",
        grantAccess: "授权成功",
    },
    role: {
        query: {
            roleName: "角色名称",
            roleCode: "权限字符",
            state: "角色状态",
            creationTime: "创建时间",
            startTime: "开始时间",
            endTime: "结束时间",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            export: "导出",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",

            dataAuth: "数据授权",
            userAuth: "用户授权",
        },
        tbCol: {
            roleCode: "角色编号",
            roleName: "角色名称",
            roleKey: "权限字符",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            roleName: "角色名称",
            roleCode: "角色编号",
            roleKey: "权限字符",
            roleCodeTip: "控制器中定义的权限字符,如:",
            roleCodeTip1: "权限字符",
            roleCodeTip2: "请输入权限字符",
            menuAuth: "菜单权限",
            menuAuth1: "展开/折叠",
            menuAuth2: "全选/全不选",
            menuAuth3: "父子联动",
            menuAuth4: "加载中，请稍候",
            authScope: "授权范围",
            dataAuth: "数据授权",
            dataAuth1: "展开/折叠",
            dataAuth2: "全选/全不选",
            dataAuth3: "父子联动",
            dataAuth4: "加载中，请稍候",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "全部数据权限",
            alarm02: "自定数据权限",
            alarm03: "本部门数据权限",
            alarm04: "本部门及以下数据权限",
            alarm05: "仅本人数据权限",
            alarm06: "角色名称不能为空",
            alarm07: "权限字符不能为空",
            alarm08: "角色顺序不能为空",
            alarm09: "",
            alarm10: "",
            alarm11: "",
            alarm12: "",

            enabled: "启用",
            disabled: "禁用",
            useTips1: "确认要",
            useTips2: "角色吗?",
            useTips3: "成功",

            dataAuth: "分配数据权限",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "确认要删除该数据项吗？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
    authUser: {
        userName: "用户名称",
        phone: "手机号码",
        search: "搜索",
        reset: "重置",
        add: "添加用户",
        cancleAuth: "取消授权",
        cancleTip: "是否取消选中用户授权数据项?",
        cancleAcc: "取消授权成功",
        close: "关闭",
        nickName: "昵称",
        email: "邮箱",
        status: "状态",
        creationTime: "创建时间",
        operation: "操作",
    },
    sltUser: {
        choice: "选择用户",
        userName: "用户名称",
        phone: "手机号码",
        nickName: "昵称",
        email: "邮箱",
        status: "状态",
        creationTime: "创建时间",
        search: "搜索",
        reset: "重置",
        confirm: "确定",
        cancle: "取消",
        choiceUser: "请选择要分配的用户",
        success: "成功",
    },
    menu: {
        query: {
            menuName: "菜单名称",
            status: "菜单状态",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            export: "导出",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",

            toggleExpand: "展开/折叠",
        },
        tbCol: {
            menuName: "菜单名称",
            engName: "英文名称",
            ptName: "葡语名称",
            menuIcon: "图标",
            permissionCode: "权限标识",
            router: "路由",
            component: "组件路径",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            lastMenu: "上级菜单",
            menuType: "菜单类型",
            catalogue: "目录",
            menu: "菜单",
            button: "按钮",
            menuIcon: "菜单图标",
            choiceIcon: "点击选择图标",
            menuName: "菜单名称",
            engName: "英文名称",
            ptName: "葡语名称",
            orderNum: "排序",
            outLink: "是否外链",
            linkTip: "选择是外链则路由地址需要以`http(s)://`开头",
            yes: "Yes",
            no: "No",
            router: "路由地址",
            routerContent: "访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",
            component: "组件路径",
            componentContent: "访问的组件路径，如：`system/user/index`，默认在`views`目录下",
            permission: "权限字符",
            permissionContent: "控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)",
            query: "路由参数",
            queryContent: "访问路由的默认传递参数，如：{\"id\": 1, \"name\": \"ry\"}`",
            cache: "是否缓存",
            cacheYes: "确认缓存",
            cacheNo: "不缓存",
            cacheContent: "选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",
            show: "显示状态",
            showContent: "选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",
            menuState: "菜单状态",
            menuStateContent: "选择停用则路由将不会出现在侧边栏，也不能被访问",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "菜单名称不能为空",
            alarm02: "英文名称不能为空",
            alarm03: "葡语名称不能为空",
            alarm04: "菜单顺序不能为空",
            alarm05: "路由地址不能为空",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            mainClass: "主类目",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "确认要删除该数据项吗？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
    httpOption: {
        query: {
            serviceId: "接口实例",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            export: "导出",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        tbCol: {
            name: "标识",
            appId: "AppId",
            appKey: "AppKey",
            facCode: "工厂编号",
            serviceId: "接口实例",
            httpUrl: "Url地址",
            httpRoute: "接口路由",
            updateTime: "请求时间",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        form: {
            name: "标识",
            appId: "AppId",
            appKey: "AppKey",
            facCode: "工厂编号",
            serviceId: "接口实例",
            httpUrl: "Url地址",
            httpRoute: "接口路由",
            updateTime: "请求时间",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarm01: "请填写标识",
            alarm02: "请填写AppId",
            alarm03: "请填写AppKey",
            alarm04: "请填写工厂编号",
            alarm05: "请填写接口实例",
            alarm06: "请填写Url地址",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "确认要删除该数据项吗？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",
        }
    },
}