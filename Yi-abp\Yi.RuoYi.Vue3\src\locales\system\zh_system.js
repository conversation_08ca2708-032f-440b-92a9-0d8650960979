export default {
    equipList: {
        queryField: {
            lineCode: "产线编号",
            lineCodeTip: "产线编号",
            stationCode: "工位编号",
            stationCodeTip: "工位编号",
            machineCode: "设备编码",
            machineCodeTip: "设备编码",
        },
        tbColumn: {
            lineCode: "产线编号",
            stationCode: "工位编号",
            resourceCode: "资源编号",
            machineCode: "设备编码",
            machineName: "设备名称",
            machineStatus: "设备状态",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            synchronous: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        elForm: {
            lineCode: "产线编号",
            lineCodeTip: "产线编号",
            stationCode: "工位编号",
            stationCodeTip: "工位编号",
            resourceCode: "资源编号",
            resourceCodeTip: "资源编号",
            machineCode: "设备编码",
            machineCodeTip: "设备编码",
            machineName: "设备名称",
            machineNameTip: "设备名称",
            machineStatus: "设备状态",
            machineStatusTip: "设备状态",
            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarmMsg01: "请填写产线编号",
            alarmMsg02: "请填写工位编号",
            alarmMsg03: "请填写资产编号",
            alarmMsg04: "请填写设备编号",

            addTitle: "新增",
            addSuccess: "新增成功",
            editTitle: "编辑",
            editSuccess: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuccess: "删除成功",
            importSuccess: "导入成功",
        }
    },
    equipLog: {
        queryField: {
            lineCode: "产线编号",
            lineCodeTip: "产线编号",
            stationCode: "工位编号",
            stationCodeTip: "工位编号",
            machineCode: "设备编码",
            machineCodeTip: "设备编码",
        },
        tbColumn: {
            id: "编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            resourceCode: "资源编号",
            machineCode: "设备编码",
            machineStatus: "设备状态",
            machineStatusBegin: "开始时间",
            machineStatusEnd: "结束时间",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            synchronous: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        elForm: {
            lineCode: "产线编号",
            lineCodeTip: "产线编号",
            stationCode: "工位编号",
            stationCodeTip: "工位编号",
            machineCode: "设备编码",
            machineCodeTip: "设备编码",
            resourceCode: "资源编号",
            resourceCodeTip: "资源编号",
            machineStatus: "设备状态",
            machineStatusTip: "设备状态",
            machineStatusBegin: "开始时间",
            machineStatusBeginTip: "开始时间",
            machineStatusEnd: "结束时间",
            machineStatusEndTip: "结束时间",
            stataus: "状态",
            statausTip: "状态",
            remark: "备注",
            remarkTip: "备注",
        },
        message: {
            alarmMsg01: "请填写产线编号",
            alarmMsg02: "请填写工位编号",
            alarmMsg03: "请填写设备编号",
            alarmMsg04: "请填写资产编号",
            alarmMsg05: "请填写设备状态",

            addTitle: "新增",
            addSuccess: "新增成功",
            editTitle: "编辑",
            editSuccess: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuccess: "删除成功",
            importSuccess: "导入成功",
        },

    },
    equipWarning: {
        queryField: {
            lineCode: "产线编号",
            lineCodeTip: "产线编号",
            stationCode: "工位编号",
            stationCodeTip: "工位编号",
            machineCode: "设备编码",
            machineCodeTip: "设备编码",
        },
        tbColumn: {
            id: "编号",
            lineCode: "产线编号",
            stationCode: "工位编号",
            resourceCode: "资源编号",
            machineCode: "设备编码",
            machineName: "设备名称",
            deviceStatus: "设备状态",
            deviceAlarm: "设备报警",
            accumulatedPowerOnTime: "累计上电(S)",
            accumulatedOperationTime: "累计操作(S)",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            synchronous: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        elForm: {
            lineCode: "产线编号",
            lineCodeTip: "产线编号",
            stationCode: "工位编号",
            stationCodeTip: "工位编号",
            machineCode: "设备编码",
            machineCodeTip: "设备编码",
            resourceCode: "资源编号",
            resourceCodeTip: "资源编号",
            deviceStatus: "设备状态",
            deviceStatusTip: "设备状态",
            deviceAlarm: "报警状态",
            deviceAlarmTip: "报警状态",
            accumulatedPowerOnTime: "上电时间",
            accumulatedPowerOnTimeTip: "上电时间",
            accumulatedOperationTime: "操作时间",
            accumulatedOperationTimeTip: "操作时间",
            Stataus: "状态",
            StatausTip: "状态",
            Remark: "备注",
            RemarkTip: "备注",
        },
        message: {
            alarmMsg01: "请填写产线编号",
            alarmMsg02: "请填写工位编号",
            alarmMsg03: "请填写设备编号",
            alarmMsg04: "请填写资产编号",
            
            addTitle: "新增",
            addSuccess: "新增成功",
            editTitle: "编辑",
            editSuccess: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuccess: "删除成功",
            importSuccess: "导入成功",
        },
    }
}