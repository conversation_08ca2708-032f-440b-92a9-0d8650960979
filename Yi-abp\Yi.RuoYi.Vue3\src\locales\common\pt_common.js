export default {
    currentLanguage: "pt",
    title: "BYD MES",
    routerIndex: {
        firstPage: "Página Inicial",
        personalCenter: "Centro Pessoal",
        userAuth: "User Autorizar",
        roleAuth: "Role Autorizar",
        dictData: "Dados Dicionário",
        jobLog: "Log Trabalho",
        genEdit: "Gerar Edição",
    },
    navbar: {
        personalCenter: "Centro Pessoal",
        layout: "Layout",
        logout: "Saída"
    },
    tagsView:{
        refresh:"Refreshar",
        closeNow:"<PERSON><PERSON>r Agora",
        closeElse:"Fechar Outro",
        closeLeft:"<PERSON><PERSON><PERSON>",
        closeRight:"Fechar Direita",
        closeAll:"<PERSON><PERSON><PERSON>"        
    },
    layoutFormat: {
        layoutTitle: "Estilo Layout",
        colorText: "cor Layout",
        subTitle: "Sistema Layout",
        showTopNav: "Abrir TopNav",
        showTagsViews: "Abrir Tags-Visão",
        fixedHeader: "<PERSON>xo C<PERSON>ho",
        showLogo: "Mostrar Logotipo",
        dynamicTitle: "<PERSON><PERSON><PERSON><PERSON>",
        saveBtn: "Salvar Botão",
        resetBtn: "Redefinir Botão",
        //commponents sizeSelect
        larger: "Maior",
        default: "Padrão",
        smaller: "Menor",
        szieSwitchMsg: "Definindo o tamanho do layout, por favor aguarde.."
    },
    userProfile: {
        profilePicture: "Editar Perfil",
        pictureTips: "Clique Carregar",
        select: "Selecionar",
        upload: "Carregar",
        uploadfailMsg: "Reconhecimento de imagem falhou, por favor, carregue um arquivo do tipo de imagem, como JPG, PNG",

        personalInfo: "Pessoais Informações",
        userName: "Usuário Nome",
        cellPhone: "Telefone Número",
        email: "Usuário E-mail",
        deptName: "Departamento Nome",
        createTime: "Criação Tempo",

        baseInfo: "Básicas Informações",
        userNameEdit: "Usuário Nome",
        userNameVerify: "O apelido do usuário não pode estar vazio",
        cellPhoneEdit: "Telefone Número",
        cellPhoneVerify: "O número de telefone não pode estar vazio",
        emailEdit: "Usuário E-mail",
        emailVerify: "O endereço de e-mail não pode estar vazio",
        emailRegex: "Por favor, insira o endereço de e-mail correto",

        pwdEdit: "Editar Senha",
        oldPwd: "Senha Antiga",
        oldPwdTip: "Por favor, insira a senha antiga",
        oldPwdVerify: "A senha antiga não pode estar vazia",
        newPwd: "Nova Senha",
        newPwdTip: "Por favor, insira uma nova senha",
        newPwdVerify: "A nova senha não pode estar vazia",
        newPwdRegex: "Comprimento variando de 6 a 20 caracteres",
        confirmPwd: "Confirmar Senha",
        confirmPwdTip: "Confirme a nova senha",
        confirmPwdVerify: "Confirmar que a senha não pode estar vazia",
        pwdComparisonMsg: "As senhas digitadas diferem!",

        saveBtn: "Salvar",
        colseBtn: "Fechar",

        editMsg: "Editado com sucesso",
    }
}