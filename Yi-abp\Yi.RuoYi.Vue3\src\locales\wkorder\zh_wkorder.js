export default {
    wkOrder: {
        query: {
            status: "工单状态",
            orderCode: "工单编号",
            scheduleCode: "排程编码",
            lineCode: "产线编号",
            materialCode: "产品编号",
            dateRangeProduct: "生产日期",
            dateRangeProductTip1: "起始日期",
            dateRangeProductTip2: "终止日期",
            dateRangeBegin: "计划开始日期",
            dateRangeBeginTip1: "起始日期",
            dateRangeBeginTip2: "终止日期",
            dateRangeEnd: "计划结束日期",
            dateRangeEndTip1: "起始日期",
            dateRangeEndTip2: "终止日期"
        },
        tbCol: {
            scheduleCode: "排程编号",
            planTime: "生产日期",
            planStartTime: "计划开始日期",
            planEndTime: "计划结束日期",
            materialCode: "产品编号",
            materialName: "产品名称",
            lineCode: "产线编号",
            scheduleStatus: "排程状态",
            scheduleQty: "排程数量",
            orderCode: "工单编号",
            orderType: "工单类型",
            orderStatus: "工单状态",
            orderQty: "工单数量",
            onlineQty: "投产数量",
            completedQty: "下线数量",
            scheduleEditTime: "排程时间",
            workshopCode: "车间编号",
            materialVersion: "产品版本",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            start: "开产",
            stop: "暂停",
            restore: "复产",
            cancle: "取消",
            close: "关闭",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
            orderCode: "工单编号",
            orderType: "工单类型",
            orderQty: "工单数量",
            orderStatus: "工单状态",
            scheduleCode: "排程编号",
            scheduleQty: "排程数量",
            scheduleStatus: "排程状态",
            scheduleEditTime: "排程时间",
            onlineQty: "投产数量",
            completedQty: "下线数量",

            workshopCode: "工位编号",
            lineCode: "产线编号",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",
            planStartTime: "计划开始",
            planEndTime: "计划结束",
            planTime: "计划时间",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarmMsg01: "请填写工单号",
            alarmMsg02: "请填写工单类型",
            alarmMsg03: "请填写工单数量",
            alarmMsg04: "请填写排程编号",
            alarmMsg05: "请填写排程数量",
            alarmMsg06: "请选择排程编辑时间",
            alarmMsg07: "请填写车间编号",
            alarmMsg08: "请填写产线编号",
            alarmMsg09: "请填写产品编号",
            alarmMsg10: "请填写产品名称",
            alarmMsg11: "请填写产品版本",
            alarmMsg12: "请选择计划开始时间",
            alarmMsg13: "请选择计划结束时间",

            editPlanTime: "编辑成功",
            editOrderNum: "编辑成功",
            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",

            statusTitle01: "确认开始生产工单:",
            statusTitle02: "确认暂停生产工单:",
            statusTitle03: "确认恢复生产工单:",
            statusTitle04: "确认取消生产工单:",
            statusTitle05: "确认关闭生产工单:",
            statusMiddleTitle: "排程:",
        }
    },
    orderBom: {
        query: {
            orderCode: "工单编号",
            scheduleCode: "排程编码",
            materialCode: "产品编号",
            operationCode: "工序编码",
            assemblyMaterialCode: "物料编码",
        },
        tbCol: {
            orderCode: "工单号",
            orderQty: "工单数量",
            scheduleCode: "排程编号",
            scheduleQty: "排程数量",
            lineCode: "产线编号",
            lineName: "产线名称",
            bomId: "BomId",
            bomType: "Bom类型",
            bomVersion: "Bom版本",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品名称",
            operationCode: "工序编号",
            assemblyMaterialId: "物料Id",
            assemblyMaterialCode: "物料编号",
            assemblyMaterialName: "物料名称",
            assemblyMaterialVersion: "物料版本",
            assemblyUnitCode: "物料单位",
            assemblyConsumption: "物料用量",
            standWorkHours: "标准工时",
            retroactive: "追溯方式",
            addTime: "创建时间",
            editTime: "更新时间",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
            orderCode: "工单号",
            orderQty: "工单数量",
            scheduleCode: "排程编号",
            scheduleQty: "排程数量",
            scheduleEditTime: "排程时间",
            bomId: "BomId",
            bomType: "Bom类型",
            bomVersion: "Bom版本",
            lineCode: "产线编号",
            lineName: "产线编号",
            materialCode: "产品编号",
            materialName: "产品名称",
            materialVersion: "产品版本",

            operationCode: "工序编号",
            assemblyMaterialId: "物料Id",
            assemblyMaterialCode: "物料编号",
            assemblyMaterialName: "物料名称",
            assemblyMaterialVersion: "物料版本",
            assemblyUnitCode: "物料单位",
            assemblyConsumption: "物料用量",
            standWorkHours: "标准工时",
            retroactive: "追溯方式",
            addTime: "创建时间",
            editTime: "更新时间",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarmMsg01: "请填写工单编号",
            alarmMsg02: "请填写工单数量",
            alarmMsg03: "请填写排程编号",
            alarmMsg04: "请填写排程数量",
            alarmMsg05: "请填写BomId",
            alarmMsg06: "请选择Bom类型",
            alarmMsg07: "请填写Bom版本",
            alarmMsg08: "请填写产线编号",
            alarmMsg09: "请填写产品编号",
            alarmMsg10: "请填写产品版本",
            alarmMsg11: "请填写工序编号",
            alarmMsg12: "请填物料Id",
            alarmMsg13: "请填物料编号",
            alarmMsg14: "请填物料版本",
            alarmMsg15: "请填写物料单位",
            alarmMsg16: "请填物料户量",
            alarmMsg17: "请填标准工时",
            alarmMsg18: "请填追溯方式",

            editPlanTime: "调整成功",
            editOrderNum: "调整成功",
            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",

            syncTit01: "请填写工单编号:",
            syncTit02: "请填写排程编号:",
        }
    },
    orderRoute: {
        query: {
            orderCode: "工单编号",
            scheduleCode: "排程编码",
            routeNumber: "工艺路线",
            operationCode: "工序编码",
        },
        tbCol: {
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            routeNumber: "工艺路线",
            routeVersion: "工艺版本",
            lastOperationCode: "前序编码",
            nextOperationCode: "后序编码",
            operationCode: "工序编号",
            operationName: "工序名称",
            sort: "工序顺序",
            stationCode: "工位编码",
            editTime: "更新时间",

            orderNum: "排序",
            status: "状态",
            remark: "备注",
            creatorId: "创建Id",
            creatorName: "创建人账号",
            creationTime: "创建时间",
            lastModifierId: "修改Id",
            lastModifierName: "修改人账号",
            lastModificationTime: "修改时间",
            isDeleted: "是否删除",
            operation: "操作",
        },
        button: {
            search: "搜索",
            reset: "重置",
            add: "新增",
            sync: "同步",
            import: "导入",
            edit: "编辑",
            delete: "删除",
            confirm: "确认",
            cancel: "取消",
        },
        form: {
            orderCode: "工单编号",
            scheduleCode: "排程编号",
            routeNumber: "工艺路线",
            routeVersion: "工艺版本",
            lastOperationCode: "前序编码",
            nextOperationCode: "后序编码",
            operationCode: "工序编号",
            operationName: "工序名称",
            sort: "工序顺序",
            stationCode: "工位编码",
            editTime: "更新时间",

            status: "状态",
            remark: "备注",
            remarkTip: "请输入内容",
        },
        message: {
            alarmMsg01: "请填写工单编号",
            alarmMsg02: "请填写排程编号",
            alarmMsg03: "请填写工艺路线编号",
            alarmMsg04: "请填写工艺路线版本",
            alarmMsg05: "请填写前一道工序编号",
            alarmMsg06: "请填写下一道工序编号",
            alarmMsg07: "请填写工序编号",
            alarmMsg08: "请填写工序名称",
            alarmMsg09: "请填写工序顺序",
            alarmMsg10: "请填写工位编号",

            editPlanTime: "调整成功",
            editOrderNum: "调整成功",
            addTit: "新增",
            addSuc: "新增成功",
            editTit: "编辑",
            editSuc: "编辑成功",
            delMsg: "是否删除该数据项？",
            delSuc: "删除成功",
            syncSuc: "同步成功",
            importSuc: "导入成功",

            syncTit01: "请填写工单编号:",
            syncTit02: "请填写排程编号:",
        }
    }
}