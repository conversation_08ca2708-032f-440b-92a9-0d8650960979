<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="14 0px">
         <el-form-item :label="t('basedata.post.queryField.postCode')" prop="postCode">
            <el-input v-model="queryParams.postCode" :placeholder="t('basedata.post.queryField.codeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.post.queryField.postName')" prop="postName">
            <el-input v-model="queryParams.postName" :placeholder="t('basedata.post.queryField.nameTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.post.queryField.state')" prop="state">
            <el-select v-model="queryParams.state" :placeholder="t('basedata.post.queryField.postState')" clearable style="width: 240px">
               <el-option v-for="dict in sys_normal_disable" :key="dict.value" :label="dict.label" :value="JSON.parse(dict.value)" />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ t('basedata.post.button.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ t('basedata.post.button.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:post:add']">{{ t('basedata.post.button.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate" v-hasPermi="['basic:post:edit']" v-if="false">{{ t('basedata.post.button.edit') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete" v-hasPermi="['basic:post:remove']" v-if="false">{{ t('basedata.post.button.delete') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['basic:post:export']">{{ t('basedata.post.button.download') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="postList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="55" align="center" />
         <el-table-column label="岗位编号" align="center" prop="id" v-if="false" />
         <el-table-column :label="t('basedata.post.tbColumn.postCode')" align="center" prop="postCode" />
         <el-table-column :label="t('basedata.post.tbColumn.postName')" align="center" prop="postName" />
         <el-table-column :label="t('basedata.post.tbColumn.postOrder')" align="center" prop="orderNum" />
         <el-table-column :label="t('basedata.post.tbColumn.state')" align="center" prop="state">
            <template #default="scope">
               <dict-tag :options="sys_normal_disable" :value="scope.row.state" />
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.post.tbColumn.creatTime')" align="center" prop="creationTime" width="180">
            <template #default="scope">
               <span>{{ parseTime(scope.row.creationTime) }}</span>
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.post.tbColumn.operate')" align="center" class-name="small-padding fixed-width">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:post:edit']">{{ t('basedata.post.button.edit') }}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:post:remove']">{{ t('basedata.post.button.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑岗位对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="postRef" :model="form" :rules="rules" label-width="180px">
            <el-form-item :label="t('basedata.post.elForm.postCode')" prop="postCode">
               <el-input v-model="form.postCode" :placeholder="t('basedata.post.elForm.codeTip')" />
            </el-form-item>
            <el-form-item :label="t('basedata.post.elForm.postName')" prop="postName">
               <el-input v-model="form.postName" :placeholder="t('basedata.post.elForm.nameTip')" />
            </el-form-item>
            <el-form-item :label="t('basedata.post.elForm.postOrder')" prop="orderNum">
               <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
            </el-form-item>
            <el-form-item :label="t('basedata.post.elForm.postState')" prop="state">
               <el-radio-group v-model="form.state">
                  <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
               </el-radio-group>
            </el-form-item>
            <el-form-item :label="t('basedata.post.elForm.remark')" prop="remark">
               <el-input v-model="form.remark" type="textarea" :placeholder="t('basedata.post.elForm.remarkTip')" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ t('basedata.post.button.confirm') }}</el-button>
               <el-button @click="cancel">{{ t('basedata.post.button.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Post">
import { listPost, addPost, delPost, getPost, updatePost } from "@/api/basic/post";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const postList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      postCode: undefined,
      postName: undefined,
      state: undefined
   },
   rules: {
      postName: [{ required: true, message: t('basedata.post.message.alarm01'), trigger: "blur" }],
      postCode: [{ required: true, message: t('basedata.post.message.alarm02'), trigger: "blur" }],
      orderNum: [{ required: true, message: t('basedata.post.message.alarm03'), trigger: "blur" }],
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询岗位列表 */
function getList() {
   loading.value = true;
   listPost(queryParams.value).then(response => {
      postList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}
/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
      postCode: undefined,
      postName: undefined,
      orderNum: 0,
      state: false,
      remark: undefined
   };
   proxy.resetForm("postRef");
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "添加岗位";
}
/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getPost(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑岗位";
   });
}
/** 提交按钮 */
function submitForm() {
   proxy.$refs["postRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updatePost(form.value).then(response => {
               proxy.$modal.msgSuccess(t('basedata.post.message.editSuccess'));
               open.value = false;
               getList();
            });
         } else {
            addPost(form.value).then(response => {
               proxy.$modal.msgSuccess(t('basedata.post.message.addSucess'));
               open.value = false;
               getList();
            });
         }
      }
   });
}
/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('basedata.post.message.ifDelete') + postIds + t('basedata.post.message.ifData')).then(function () {
      return delPost(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('basedata.post.message.deleteSucess'));
   }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
   proxy.download("basic/post/export", {
      ...queryParams.value
   }, `post_${new Date().getTime()}.xlsx`);
}

getList();
</script>
