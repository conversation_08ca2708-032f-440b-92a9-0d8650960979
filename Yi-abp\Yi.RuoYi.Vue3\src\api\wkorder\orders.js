import request from '@/utils/request'

// 自定义列表
export function listDataAsync(query) {
  return request({
    url: '/wk-order/getListAsync',
    method: 'get',
    params: query
  })
}

// 查询列表
export function listData(query) {
  return request({
    url: '/wk-order',
    method: 'get',
    params: query
  })
}

// 查询详细
export function getData(id) {
  return request({
    url: '/wk-order/' + id,
    method: 'get'
  })
}

// 新增
export function addData(data) {
  return request({
    url: '/wk-order',
    method: 'post',
    data: data
  })
}

// 编辑
export function updateData(id, data) {
  return request({
    url: `/wk-order/` + id,
    method: 'put',
    data: data
  })
}

// 删除
export function delData(ids) {
  return request({
    url: `/wk-order`,
    method: 'delete',
    params: { id: ids }
  })
}

// 同步
export function getDown() {
  return request({
    url: '/wk-order/getDown',
    method: 'get'
  })
}

// 导入
export function importAsync(data) {
  return request({
    url: '/wk-order/importAsync',
    method: 'post',
    data: data
  })
}