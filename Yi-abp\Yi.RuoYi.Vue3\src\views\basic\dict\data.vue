<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="130px">
      <el-form-item :label="t('basedata.dictionaryData.queryField.dictType')" prop="dictType">
        <el-select v-model="queryParams.dictType" style="width: 200px">
          <el-option v-for="item in typeOptions" :key="item.id" :label="item.dictName" :value="item.dictType" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('basedata.dictionaryData.queryField.dictLabel')" prop="dictLabel">
        <el-input v-model="queryParams.dictLabel" :placeholder="t('basedata.dictionaryData.queryField.labelTip')" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item :label="t('basedata.dictionaryData.queryField.state')" prop="state">
        <el-select v-model="queryParams.state" placeholder="数据状态" clearable style="width: 200px">
          <el-option v-for="dict in sys_normal_disable" :key="JSON.parse(dict.value)" :label="dict.label" :value="JSON.parse(dict.value)" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">{{ $t('basedata.dictionaryData.button.search') }}</el-button>
        <el-button icon="Refresh" @click="resetQuery">{{ $t('basedata.dictionaryData.button.reset') }}</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:dict:add']">{{ $t('basedata.dictionaryData.button.add') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" v-if="false" :disabled="single" @click="handleUpdate" v-hasPermi="['basic:dict:edit']">{{ $t('basedata.dictionaryData.button.edit') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" v-if="false" :disabled="multiple" @click="handleDelete" v-hasPermi="['basic:dict:remove']">{{ $t('basedata.dictionaryData.button.delete') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport" v-hasPermi="['basic:dict:export']">{{ $t('basedata.dictionaryData.button.download') }}</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Close" v-if="false" @click="handleClose" disabled>{{ $t('basedata.dictionaryData.button.close') }}</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.chiDictags')" prop="dictLabel" width="260">
        <template #default="scope">
          <span v-if="scope.row.listClass == '' || scope.row.listClass == 'default'">{{ scope.row.dictLabel }}</span>
          <el-tag v-else :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass">{{ scope.row.dictLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.engDictags')" prop="dictEngLabel" width="260">
        <template #default="scope">
          <span v-if="scope.row.listClass == '' || scope.row.listClass == 'default'">{{ scope.row.dictEngLabel }}</span>
          <el-tag v-else :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass">{{ scope.row.dictEngLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.ptDictags')" prop="dictPtLabel" width="260">
        <template #default="scope">
          <span v-if="scope.row.listClass == '' || scope.row.listClass == 'default'">{{ scope.row.dictPtLabel }}</span>
          <el-tag v-else :type="scope.row.listClass == 'primary' ? '' : scope.row.listClass">{{ scope.row.dictPtLabel }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.dicKey')" prop="dictValue" width="260" />
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.dicSort')" align="center" prop="orderNum" width="100" />
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.state')" align="center" prop="state">
        <template #default="scope">
          <dict-tag :options="sys_normal_disable" :value="scope.row.state" />
        </template>
      </el-table-column>
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.remark')" align="center" prop="remark" :show-overflow-tooltip="true" width="110"/>
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.creaTime')" align="center" prop="creationTime" width="155">
        <template #default="scope">
          <span>{{ parseTime(scope.row.creationTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column :label="t('basedata.dictionaryData.tbColumn.operate')" align="center" width="150" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:dict:edit']" type="success">{{ $t('basedata.dictionaryData.tbColumn.edit') }}</el-button>
          <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:dict:remove']" type="danger">{{ $t('basedata.dictionaryData.tbColumn.delete') }}</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

    <!-- 添加或编辑参数配置对话框 -->
    <el-dialog :title="title" v-model="open" width="600px" append-to-body>
      <el-form ref="dataRef" :model="form" :rules="rules" label-width="140px">
        <el-form-item :label="t('basedata.dictionaryData.elForm.dicType')">
          <el-input v-model="form.dictType" :disabled="true" />
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.chineseLabels')" prop="dictLabel">
          <el-input v-model="form.dictLabel" :placeholder="t('basedata.dictionaryData.elForm.chinaTip')" />
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.englishLabels')" prop="dictEngLabel">
          <el-input v-model="form.dictEngLabel" :placeholder="t('basedata.dictionaryData.elForm.engTip')" />
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.labelPortuguese')" prop="dictPtLabel">
          <el-input v-model="form.dictPtLabel" :placeholder="t('basedata.dictionaryData.elForm.ptTip')" />
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.keyValue')" prop="dictValue">
          <el-input v-model="form.dictValue" :placeholder="t('basedata.dictionaryData.elForm.keyTip')" />
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.styleAttributes')" prop="cssClass">
          <el-input v-model="form.cssClass" :placeholder="t('basedata.dictionaryData.elForm.attriTip')" />
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.displaySorting')" prop="dictSort">
          <el-input-number v-model="form.orderNum" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.echoStyle')" prop="listClass">
          <el-select v-model="form.listClass">
            <el-option v-for="item in listClassOptions" :key="item.value" :label="item.label + '(' + item.value + ')'" :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.state')" prop="state">
          <el-radio-group v-model="form.state">
            <el-radio v-for="dict in sys_normal_disable" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label
            }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item :label="t('basedata.dictionaryData.elForm.remark')" prop="remark">
          <el-input v-model="form.remark" type="textarea" :placeholder="t('basedata.dictionaryData.elForm.contentTip')"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">{{ $t('basedata.dictionaryData.button.confirm') }}</el-button>
          <el-button @click="cancel">{{ $t('basedata.dictionaryData.button.cancel') }}</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="Data">
import useDictStore from '@/store/modules/dict'
import { optionselect as getDictOptionselect, getType } from "@/api/basic/dict/type";
import { listData, getData, delData, addData, updateData } from "@/api/basic/dict/data";

import { useI18n } from 'vue-i18n';
const { t } = useI18n();

const { proxy } = getCurrentInstance();
const { sys_normal_disable } = proxy.useDict("sys_normal_disable");

const dataList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const defaultDictType = ref("");
const typeOptions = ref([]);
const route = useRoute();
// 数据标签回显样式
const listClassOptions = ref([
  { value: "default", label: "默认" },
  { value: "primary", label: "主要" },
  { value: "success", label: "成功" },
  { value: "info", label: "信息" },
  { value: "warning", label: "警告" },
  { value: "danger", label: "危险" }
]);

const data = reactive({
  form: {},
  queryParams: {
    skipCount: 1,
    maxResultCount: 10,
    dictName: undefined,
    dictType: undefined,
    state: true
  },
  rules: {
    dictLabel: [{ required: true, message: t('basedata.dictionaryData.message.alarm01'), trigger: "blur" }],
    dictValue: [{ required: true, message: t('basedata.dictionaryData.message.alarm02'), trigger: "blur" }],
    orderNum: [{ required: true, message: t('basedata.dictionaryData.message.alarm03'), trigger: "blur" }]
  }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询字典类型详细 */
function getTypes(dictId) {
  getType(dictId).then(response => {
    queryParams.value.dictType = response.data.dictType;
    defaultDictType.value = response.data.dictType;
    getList();
  });
}

/** 查询字典类型列表 */
function getTypeList() {
  getDictOptionselect().then(response => {
    typeOptions.value = response.data.items;
  });
}
/** 查询字典数据列表 */
function getList() {
  loading.value = true;
  listData(queryParams.value).then(response => {
    dataList.value = response.data.items;
    total.value = response.data.totalCount;
    loading.value = false;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    id: undefined,
    dictLabel: undefined,
    dictValue: undefined,
    cssClass: undefined,
    listClass: "default",
    orderNum: 0,
    state: true,
    remark: undefined
  };
  proxy.resetForm("dataRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.skipCount = 1;
  getList();
}
/** 返回按钮操作 */
function handleClose() {
  // const obj = { path: "/basic/dict" };
  // proxy.$tab.closeOpenPage(obj);
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.dictType = defaultDictType;
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value =  t('basedata.dictionaryData.message.add_title');
  form.value.dictType = queryParams.value.dictType;
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.id);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 编辑按钮操作 */
function handleUpdate(row) {
  reset();
  const id = row.id || ids.value;
  getData(id).then(response => {
    form.value = response.data;
    open.value = true;
    title.value =  t('basedata.dictionaryData.message.edit_title');
  });
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["dataRef"].validate(valid => {
    if (valid) {
      if (form.value.id != undefined) {
        updateData(form.value).then(response => {
          useDictStore().removeDict(queryParams.value.dictType);
          proxy.$modal.msgSuccess(t('basedata.dictionaryData.message.edit_msg'));
          open.value = false;
          getList();
        });
      } else {
        addData(form.value).then(response => {
          useDictStore().removeDict(queryParams.value.dictType);
          proxy.$modal.msgSuccess(t('basedata.dictionaryData.message.add_msg'));
          open.value = false;
          getList();
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  const dictIds = row.id || ids.value;
  proxy.$modal.confirm(t('basedata.dictionaryData.message.del_tip')).then(function () {
    return delData(dictIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess(t('basedata.dictionaryData.message.del_msg'));
    useDictStore().removeDict(queryParams.value.dictType);
  }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download("basic/dict/data/export", {
    ...queryParams.value
  }, `dict_data_${new Date().getTime()}.xlsx`);
}

getTypes(route.query && route.query.dictId);
getTypeList();
</script>
