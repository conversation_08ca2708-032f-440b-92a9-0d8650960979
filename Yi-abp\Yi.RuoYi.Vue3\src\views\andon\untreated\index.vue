<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="产线" prop="lineCode">
            <el-input v-model="queryParams.lineCode" placeholder="产线编号" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="安灯类型" prop="andonType">
            <el-select v-model="queryParams.andonType" placeholder="安灯类型" clearable style="width: 240px">
               <el-option v-for="dict in andon_type" :key="JSON.parse(dict.value)" :label="dict.label" :value="JSON.parse(dict.value)" />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8" v-if="false">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" :disabled="single" @click="handleAdd" v-hasPermi="['andon:untreated:add']">新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" :disabled="multiple" @click="handleExport" v-hasPermi="['andon:untreated:export']">导出</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="30" align="center" />
         <el-table-column label="编号" v-if="false" align="center" prop="id" />
         <el-table-column label="车间" align="center" prop="workShopCode" />
         <el-table-column label="产线" align="center" prop="lineCode" />
         <el-table-column label="工位" align="center" prop="stationCode" />
         <el-table-column label="设备" align="center" prop="machineCode" />
         <el-table-column label="Sn号" v-if="false" align="center" prop="SnNumber" />
         <el-table-column label="安灯类型" align="center" prop="andonType" />
         <el-table-column label="异常等级" align="center" prop="andonLevel" />
         <el-table-column label="异常预警时间编码" align="center" prop="unusualAlarmCode" />

         <el-table-column label="状态" align="center" prop="andonStatus">
            <template #default="scope">
               <el-select size="small" v-model="scope.row.andonStatus" @change="selChange(scope.row)">
                  <el-option v-for="item in sys_manage_state" :key="JSON.parse(item.value)" :value="JSON.parse(item.value)" :label="item.label" />
               </el-select>
            </template>
         </el-table-column>
         <el-table-column label="异常信息" align="center" prop="alarmMessage" :show-overflow-tooltip=true width="80" />
         <el-table-column label="处理建议" align="center" prop="remark" :show-overflow-tooltip=true width="80" />
         <el-table-column label="创建时间" align="center" prop="creationTime" width="155" />
         <el-table-column label="处理人" align="center" prop="lastModifierName" />
         <el-table-column label="处理时间" align="center" prop="lastModificationTime" width="155" />
         <el-table-column label="操作" align="left" fixed="right" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['andon:untreated:edit']">处理</el-button>
               <!-- <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['andon:untreated:remove']">删除</el-button> -->
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item label="Sn号" prop="SnNumber">
               <el-input-number v-model="form.SnNumber" controls-position="right" :min="0" />
            </el-form-item>
            <el-form-item label="等级" prop="andonLevel">
               <el-input-number v-model="form.andonLevel" controls-position="right" :min="0" />
            </el-form-item>
            <el-form-item label="状态" prop="andonStatus">
               <el-select v-model="form.andonStatus">
                  <el-option v-for="dict in sys_manage_state" :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
               </el-select>
            </el-form-item>
            <el-form-item label="异常信息" prop="alarmMessage" disabled>
               <el-input v-model="form.alarmMessage" type="textarea" rows="5" />
            </el-form-item>
            <el-form-item label="处理建议" prop="remark">
               <el-input v-model="form.Remark" type="textarea" rows="5" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定</el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="AndonUntreated">
import { listData, addData, delData, getData, updateData, updateAdAsync, } from "@/api/andon/untreated";

const { proxy } = getCurrentInstance();
const { andon_type } = proxy.useDict("andon_type");
const { sys_manage_state } = proxy.useDict("sys_manage_state");
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const tpForm = ref({});

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      andonType: [{ required: true, message: "请选择安灯类型", trigger: "blur" }],
      andonStatus: [{ required: true, message: "安灯状态必选", trigger: "blur" }],
      andonLevel: [{ required: true, message: "请填写等级", trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listData(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}

/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 下拉保存 */
function selChange(row) {
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      tpForm.value = response.data;
   }).catch(() => {
   }).finally(() => {
      tpForm.value.andonStatus = row.andonStatus;
      updateData(tpForm.value.id, tpForm.value).then(response => {
         proxy.$modal.msgSuccess("编辑成功");
         getList();
      });
   })
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined
   };
   proxy.resetForm("submitRef");
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm('是否删除编号为"' + postIds + '"的数据项？').then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess("编辑成功");
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 导出按钮操作 */
function handleExport() {
   proxy.download("andon/type/export", {
      ...queryParams.value
   }, `post_${new Date().getTime()}.xlsx`);
}

getList();
</script>
