<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="128px">
         <el-form-item :label="t('andon.untreated.query.lineCode')" prop="lineCode">
            <el-input v-model="queryParams.lineCode" :placeholder="t('andon.untreated.query.lineCode')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('andon.untreated.query.andonType')" prop="andonType">
            <el-select v-model="queryParams.andonType" :placeholder="t('andon.untreated.query.andonType')" clearable style="width: 240px">
               <el-option v-for="dict in andon_type" :key="JSON.parse(dict.value)" :label="dict.label" :value="JSON.parse(dict.value)" />
            </el-select>
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{t('andon.untreated.button.search')}}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{t('andon.untreated.button.reset')}}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8" v-if="false">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" :disabled="single" @click="handleAdd" v-hasPermi="['andon:untreated:add']">{{t('andon.untreated.button.add')}}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" :disabled="multiple" @click="handleExport" v-hasPermi="['andon:untreated:export']">{{t('andon.untreated.button.export')}}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange">
         <el-table-column type="selection" width="30" align="center" />
         <el-table-column label="编号" align="center" prop="id" v-if="false" />
         <el-table-column :label="t('andon.untreated.tbCol.workShopCode')" align="center" prop="workShopCode" min-width="130" />
         <el-table-column :label="t('andon.untreated.tbCol.lineCode')" align="center" prop="lineCode" min-width="100" />
         <el-table-column :label="t('andon.untreated.tbCol.stationCode')" align="center" prop="stationCode" min-width="120" />
         <el-table-column :label="t('andon.untreated.tbCol.machineCode')" align="center" prop="machineCode" min-width="120" />
         <el-table-column :label="t('andon.untreated.tbCol.snNumber')" align="center" prop="SnNumber" min-width="100" />
         <el-table-column :label="t('andon.untreated.tbCol.andonType')" align="center" prop="andonType" min-width="100" />
         <el-table-column :label="t('andon.untreated.tbCol.andonLevel')" align="center" prop="andonLevel" min-width="100" />
         <el-table-column :label="t('andon.untreated.tbCol.unusualAlarmCode')" align="center" prop="unusualAlarmCode" min-width="150" />

         <el-table-column :label="t('andon.untreated.tbCol.andonStatus')" align="center" prop="andonStatus" min-width="120">
            <template #default="scope">
               <el-select size="small" v-model="scope.row.andonStatus" @change="selChange(scope.row)">
                  <el-option v-for="item in sys_manage_state" :key="JSON.parse(item.value)" :value="JSON.parse(item.value)" :label="item.label" />
               </el-select>
            </template>
         </el-table-column>
         <el-table-column :label="t('andon.untreated.tbCol.alarmMessage')" align="center" prop="alarmMessage" :show-overflow-tooltip=true width="120" />
         <el-table-column :label="t('andon.untreated.tbCol.remark')" align="center" prop="remark" :show-overflow-tooltip=true min-width="100" />
         <el-table-column :label="t('andon.untreated.tbCol.creationTime')" align="center" prop="creationTime" min-width="155" />
         <el-table-column :label="t('andon.untreated.tbCol.lastModifierName')" align="center" prop="lastModifierName"  width="160"/>
         <el-table-column :label="t('andon.untreated.tbCol.lastModificationTime')" align="center" prop="lastModificationTime" width="165" />
         <el-table-column :label="t('andon.untreated.tbCol.operation')" align="left" fixed="right" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['andon:untreated:edit']">{{t('andon.untreated.button.edit')}}</el-button>
               <!-- <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['andon:untreated:remove']">删除</el-button> -->
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="600px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-form-item :label="t('andon.untreated.form.snNumber')" prop="SnNumber">
               <el-input-number v-model="form.SnNumber" controls-position="right" :min="0" />
            </el-form-item>
            <el-form-item :label="t('andon.untreated.form.andonLevel')" prop="andonLevel">
               <el-input-number v-model="form.andonLevel" controls-position="right" :min="0" />
            </el-form-item>
            <el-form-item :label="t('andon.untreated.form.andonStatus')" prop="andonStatus">
               <el-select v-model="form.andonStatus">
                  <el-option v-for="dict in sys_manage_state" :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
               </el-select>
            </el-form-item>
            <el-form-item :label="t('andon.untreated.form.alarmMessage')" prop="alarmMessage" disabled>
               <el-input v-model="form.alarmMessage" type="textarea" rows="5" />
            </el-form-item>
            <el-form-item :label="t('andon.untreated.form.remark')" prop="remark">
               <el-input v-model="form.Remark" type="textarea" rows="5" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{t('andon.untreated.button.confirm')}}</el-button>
               <el-button @click="cancel">{{t('andon.untreated.button.cancel')}}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="AndonUntreated">
import { listData, addData, delData, getData, updateData, updateAdAsync, } from "@/api/andon/untreated";
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { andon_type } = proxy.useDict("andon_type");
const { sys_manage_state } = proxy.useDict("sys_manage_state");
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const tpForm = ref({});

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      andonType: [{ required: true, message: t('andon.untreated.message.alarm01'), trigger: "blur" }],
      andonStatus: [{ required: true, message: t('andon.untreated.message.alarm02'), trigger: "blur" }],
      andonLevel: [{ required: true, message: t('andon.untreated.message.alarm03'), trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listData(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}

/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 下拉保存 */
function selChange(row) {
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      tpForm.value = response.data;
   }).catch(() => {
   }).finally(() => {
      tpForm.value.andonStatus = row.andonStatus;
      updateData(tpForm.value.id, tpForm.value).then(response => {
         proxy.$modal.msgSuccess(t('andon.untreated.message.editSuc'));
         getList();
      });
   })
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined
   };
   proxy.resetForm("submitRef");
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = t('andon.untreated.message.addTit');
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = t('andon.untreated.message.editTit');
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('andon.untreated.message.delMsg')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('andon.untreated.message.delSuc'));
   }).catch(() => { });
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess(t('andon.untreated.message.editSuc'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('andon.untreated.message.addSuc'));
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 导出按钮操作 */
function handleExport() {
   proxy.download("andon/type/export", {
      ...queryParams.value
   }, `post_${new Date().getTime()}.xlsx`);
}

getList();
</script>
