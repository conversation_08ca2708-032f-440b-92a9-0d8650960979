<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="148px">
         <el-form-item :label="t('basedata.pskill.queryField.empCode')" prop="empCode">
            <el-input v-model="queryParams.empCode" :placeholder="t('basedata.pskill.queryField.empCodeTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item :label="t('basedata.pskill.queryField.certificateNo')" prop="certificateNo">
            <el-input v-model="queryParams.certificateNo" :placeholder="t('basedata.pskill.queryField.certificateNoTip')" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">{{ t('basedata.pskill.button.search') }}</el-button>
            <el-button icon="Refresh" @click="resetQuery">{{ t('basedata.pskill.button.reset') }}</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['basic:pskill:add']">{{ t('basedata.pskill.button.add') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['basic:pskill:down']" :loading="downLoading">{{ t('basedata.pskill.button.synchronous') }}</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['basic:pskill:import']" :loading="importLoading" v-if="false">{{ t('basedata.pskill.button.import') }}</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column type="selection" width="50" align="center" />
         <el-table-column :label="t('basedata.pskill.tbColumn.num')" align="center" prop="id" v-if="false" />
         <!-- <el-table-column label="员工Id" align="center" prop="empId" width="100" /> -->
         <el-table-column :label="t('basedata.pskill.tbColumn.empCode')" align="center" prop="empCode" width="120" />
         <el-table-column :label="t('basedata.pskill.tbColumn.operationCode')" align="center" prop="operationCode" width="120" />
         <el-table-column :label="t('basedata.pskill.tbColumn.certificateNo')" align="center" prop="certificateNo" width="120" />
         <!-- <el-table-column label="状态Id" align="center" prop="controlStatusId" width="100" /> -->
         <el-table-column :label="t('basedata.pskill.tbColumn.controlStatusCode')" align="center" prop="controlStatusCode" width="120" />
         <el-table-column :label="t('basedata.pskill.tbColumn.skillId')" align="center" prop="skillId" width="180" />
         <!-- <el-table-column label="等级Id" align="center" prop="skillLevelId" width="100" /> -->
         <el-table-column :label="t('basedata.pskill.tbColumn.skillLevelCode')" align="center" prop="skillLevelCode" width="140" />
         <el-table-column :label="t('basedata.pskill.tbColumn.skillStartDate')" align="center" prop="skillStartDate" width="155" />
         <el-table-column :label="t('basedata.pskill.tbColumn.skillValidity')" align="center" prop="skillValidity" width="120" />
         <el-table-column :label="t('basedata.pskill.tbColumn.enableStatus')" align="center" prop="enableStatus" width="80" />
         <el-table-column :label="t('basedata.pskill.tbColumn.dataStatus')" align="center" prop="dataStatus" width="80" />
         <el-table-column :label="t('basedata.pskill.tbColumn.addTime')" align="center" prop="addTime" width="155" />
         <el-table-column :label="t('basedata.pskill.tbColumn.editTime')" align="center" prop="editTime" width="155" />

         <el-table-column prop="orderNum" :label="t('basedata.pskill.tbColumn.orderNum')" v-if="false"></el-table-column>
         <el-table-column prop="status" :label="t('basedata.pskill.tbColumn.status')">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column :label="t('basedata.pskill.tbColumn.remark')" prop="remark" v-if="false" />
         <el-table-column :label="t('basedata.pskill.tbColumn.creatorId')" prop="creatorId" v-if="false" />
         <el-table-column :label="t('basedata.pskill.tbColumn.creatorName')" prop="creatorName" v-if="false" />
         <el-table-column :label="t('basedata.pskill.tbColumn.creationTime')" prop="creationTime" width="155" v-if="false" />
         <el-table-column :label="t('basedata.pskill.tbColumn.lastModifierId')" prop="lastModifierId" v-if="false" />
         <el-table-column :label="t('basedata.pskill.tbColumn.lastModifierName')" prop="lastModifierName" v-if="false" />
         <el-table-column :label="t('basedata.pskill.tbColumn.lastModificationTime')" prop="lastModificationTime" width="155" v-if="false" />
         <el-table-column :label="t('basedata.pskill.tbColumn.isDeleted')" prop="isDeleted" v-if="false" />

         <el-table-column :label="t('basedata.pskill.tbColumn.operate')" align="center" fixed="right" class-name="small-padding fixed-width" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['basic:pskill:edit']">{{ t('basedata.pskill.button.edit') }}</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['basic:pskill:remove']">{{ t('basedata.pskill.button.delete') }}</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="180px">
            <el-form-item :label="t('basedata.pskill.elForm.empCode')" prop="empCode">
               <el-input v-model="form.empCode" :placeholder="t('basedata.pskill.elForm.empCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('basedata.pskill.elForm.operationCode')" prop="operationCode">
               <el-input v-model="form.operationCode" :placeholder="t('basedata.pskill.elForm.operationCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('basedata.pskill.elForm.certificateNo')" prop="certificateNo">
               <el-input v-model="form.certificateNo" :placeholder="t('basedata.pskill.elForm.certificateNoTip')" />
            </el-form-item>
            <el-form-item :label="t('basedata.pskill.elForm.controlStatusCode')" prop="controlStatusCode">
               <el-input v-model="form.controlStatusCode" :placeholder="t('basedata.pskill.elForm.controlStatusCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('basedata.pskill.elForm.skillLevelCode')" prop="skillLevelCode">
               <el-input v-model="form.skillLevelCode" :placeholder="t('basedata.pskill.elForm.skillLevelCodeTip')" />
            </el-form-item>
            <el-form-item :label="t('basedata.pskill.elForm.skillStartDate')" prop="skillStartDate">
               <el-date-picker v-model="form.skillStartDate" type="date" placeholder="Pick a day" format="YYYY-MM-DD" value-format="YYYY-MM-DD" />
            </el-form-item>
            <el-form-item :label="t('basedata.pskill.elForm.skillValidity')" prop="skillValidity">
               <el-input v-model="form.skillValidity" :placeholder="t('basedata.pskill.elForm.skillValidityTip')" />
            </el-form-item>
            <el-form-item :label="t('basedata.pskill.elForm.state')">
               <el-radio-group v-model="form.status">
                  <el-radio v-for="dict in base_is_open" :key="dict.value" :value="JSON.parse(dict.value)">{{ dict.label }}</el-radio>
               </el-radio-group>
            </el-form-item>
            <el-form-item :label="t('basedata.pskill.elForm.remark')" prop="remark">
               <el-input v-model="form.Remark" type="textarea" rows="3" :placeholder="t('basedata.pskill.elForm.remarkTip')" />
            </el-form-item>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">{{ t('basedata.pskill.button.confirm') }}</el-button>
               <el-button @click="cancel">{{ t('basedata.pskill.button.cancel') }}</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="PersionalSkill">
import { listDataAsync, listData, getData, delData, addData, updateData, getDown } from "@/api/basic/pskill";
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");

const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      empCode: [{ required: true, message: t('basedata.pskill.message.alarm01'), trigger: "blur" }],
      certificateNo: [{ required: true, message: t('basedata.pskill.message.alarm02'), trigger: "blur" }],
      controlStatusCode: [{ required: true, message: t('basedata.pskill.message.alarm03'), trigger: "blur" }],
      skillId: [{ required: true, message: t('basedata.pskill.message.alarm04'), trigger: "blur" }],
      skillLevelCode: [{ required: true, message: t('basedata.pskill.message.alarm05'), trigger: "blur" }],
      skillStartDate: [{ required: true, message: t('basedata.pskill.message.alarm06'), trigger: "blur" }],
      skillValidity: [{ required: true, message: t('basedata.pskill.message.alarm07'), trigger: "blur" }]
   }
});

const { queryParams, form, rules } = toRefs(data);

/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}

/** 表单重置 */
function reset() {
   form.value = {
      id: undefined,
   };
   proxy.resetForm("submitRef");
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置按钮操作 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm(t('basedata.pskill.message.ifDelete')).then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess(t('basedata.pskill.message.deleteSucess'));
   }).catch(() => { });
}

/** 同步按钮操作 */
function handleDown() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess(t('basedata.pskill.message.success')+response.data);
      }, 1500);
   });
}

/** 导出按钮操作 */
function handleImport() {
   proxy.download("andon/type/import", {
      ...queryParams.value
   }, `post_${new Date().getTime()}.xlsx`);
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('basedata.pskill.message.editSuccess'));
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess(t('basedata.pskill.message.addSucess'));
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>