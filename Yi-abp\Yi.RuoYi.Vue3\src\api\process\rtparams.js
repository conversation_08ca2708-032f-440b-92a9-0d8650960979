import request from '@/utils/request'


export function listDataAsync(query) {
    return request({
        url: '/route-params/getListAsync',
        method: 'get',
        params: query
    })
}

// 查询列表
export function listData(query) {
    return request({
        url: '/route-params',
        method: 'get',
        params: query
    })
}

// 查询详细
export function getData(id) {
    return request({
        url: '/route-params/' + id,
        method: 'get'
    })
}

// 删除
export function delData(id) {
    return request({
        url: `/route-params/${id}`,
        method: 'delete',
    })
}

// 新增
export function addData(data) {
    return request({
        url: '/route-params',
        method: 'post',
        data: data
    })
}

// 编辑
export function updateData(data) {
    return request({
        url: `/route-params/${data.id}`,
        method: 'put',
        data: data
    })
}

// 同步数据
export function getDown() {
  return request({
    url: '/route-params/getDown',
    method: 'get'
  })
}

// 导入
export function importAsync(data) {
    return request({
        url: '/route-params/importAsync',
        method: 'post',
        data: data
    })
}

// 查询菜单下拉树结构
export function treeSelect(roleId) {
    return request({
        url: '/route-params/' + roleId,
        method: 'get'
    })
}