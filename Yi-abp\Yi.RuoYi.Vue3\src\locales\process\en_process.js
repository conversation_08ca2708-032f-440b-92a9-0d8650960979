export default {
    bom: {
    },
    route: {
    },
    rtfile: {
        query: {
            materialCode: "ProductCode",
            materialCodeTip: "ProductCode",
            routeCode: "RouteCode",
            routeCodeTip: "RouteCode",
            operationCode: "OperationCode",
            operationCodeTip: "OperationCode",
        },
        tbCol: {
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",
            routeCode: "RouteCode",
            routeName: "RouteName",
            routeVersion: "RouteVersion",
            operationCode: "OperationCode",
            fileType: "FileType",
            fileName: "FileName",
            fileLoad: "FilePath",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            synchronous: "Sync",
            import: "Import",
            importSOP: "Import SOP",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
            materialCode: "ProductCode",
            materialCodeTip: "ProductCode",
            materialName: "ProductName",
            materialNameTip: "ProductName",
            materialVersion: "ProductVersion",
            materialVersionTip: "ProductVersion",
            routeCode: "RouteCode",
            routeCodeTip: "RouteCode",
            routeName: "RouteName",
            routeNameTip: "RouteName",
            routeVersion: "RouteVersion",
            routeVersionTip: "RouteVersion",
            operationCode: "OperationCode",
            operationCodeTip: "OperationCode",

            fileType: "FileType",
            fileTypeTip: "FileType",
            fileName: "FileName",
            fileNameTip: "FileName",
            fileLoad: "FilePath",
            fileLoadTip: "FilePath",
            status: "Status",
            remark: "Remark",
            remarkTip: "Remark",
        },
        message: {
            alarmMsg01: "Please fill in the product code",
            alarmMsg02: "Please fill in the product version",
            alarmMsg03: "Please fill in the process code",
            alarmMsg04: "Please fill in the process version",
            alarmMsg05: "Please fill in the operation code",
            alarmMsg06: "Failed : only PDF format files can be uploaded",
            alarmMsg07: "Failed : file size cannot exceed 50MB",
            alarmMsg08: "Data saved successfully",
            alarmMsg09: "Import failed",
            alarmMsg10: "",

            addTitle: "Add",
            addSuccess: "Add Success",
            editTitle: "Edit",
            editSuccess: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuccess: "Delete Success",
            importSuccess: "Import Success",
            syncSuccess: "Synchronous Success",
        }
    },
    rtparam: {
        query: {
            lineCode: "LineCode",
            lineCodeTip: "LineCode",
            materialCode: "ProductCode",
            materialCodeTip: "ProductCode",
            operationCode: "OperationCode",
            operationCodeTip: "OperationCode",
            paramCode: "ParamCode",
            paramCodeTip: "ParamCode",
        },
        tbCol: {
            materialCode: "ProductCode",
            materialName: "ProductName",
            materialVersion: "ProductVersion",
            lineCode: "LineCode",
            operationCode: "OperationCode",
            machineCode: "MachineCode",
            paramCode: "ParamCode",
            paramName: "ParamName",
            standardRange1: "LowerLimit",
            standardRange2: "UpperLimit",
            standardValue: "StandardValue",
            targetValue: "TargetValue",
            addTime: "AddTime",
            editTime: "EditTime",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        form: {
            materialCode: "ProductCode",
            materialCodeTip: "ProductCode",
            materialName: "ProductName",
            materialNameTip: "ProductName",
            materialVersion: "ProductVersion",
            materialVersionTip: "ProductVersion",
            lineCode: "LineCode",
            lineCodeTip: "LineCode",
            operationCode: "OperationCode",
            operationCodeTip: "OperationCode",
            machineCode: "MachineCode",
            machineCodeTip: "MachineCode",
            paramCode: "ParamCode",
            paramCodeTip: "ParamCode",
            paramName: "ParamName",
            paramNameTip: "ParamName",
            standardRange1: "LowerLimit",
            standardRange1Tip: "LowerLimit",
            standardRange2: "UpperLimit",
            standardRange2Tip: "UpperLimit",
            standardValue: "StandardValue",
            standardValueTip: "StandardValue",
            targetValue: "TargetValue",
            targetValueTip: "TargetValue",

            status: "Status",
            remark: "Remark",
            remarkTip: "Remark",
        },
        message: {
            alarmMsg01: "Please fill in the product code",
            alarmMsg02: "Please fill in the product version",
            alarmMsg03: "Please fill in the production line code",
            alarmMsg04: "Please fill in the process code",
            alarmMsg05: "Please fill in the parameter code",
            alarmMsg06: "Please fill in the lower limit value",
            alarmMsg07: "Please fill in the upper limit value",
            alarmMsg08: "Please fill in the standard value",
            alarmMsg09: "Please fill in the target value",
            alarmMsg10: "Please fill in the production line code",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    }
}