export default {
    equipList: {
        queryField: {
            lineCode: "LineCode",
            lineCodeTip: "LineCode",
            stationCode: "StationCode",
            stationCodeTip: "StationCode",
            machineCode: "MachineCode",
            machineCodeTip: "MachineCode",
        },
        tbColumn: {
            lineCode: "LineCode",
            stationCode: "StationCode",
            resourceCode: "ResourceCode",
            machineCode: "MachineCode",
            machineName: "machineName",
            machineStatus: "machineStatus",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            synchronous: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            lineCode: "LineCode",
            lineCodeTip: "LineCode",
            stationCode: "StationCode",
            stationCodeTip: "StationCode",
            resourceCode: "ResourceCode",
            resourceCodeTip: "ResourceCode",
            machineCode: "MachineCode",
            machineCodeTip: "MachineCode",
            machineName: "MachineName",
            machineNameTip: "MachineName",
            machineStatus: "MachineStatus",
            machineStatusTip: "MachineStatus",
            status: "Status",
            remark: "Remark",
            remarkTip: "Please enter the content",
        },
        message: {
            alarmMsg01: "Please fill in the line code",
            alarmMsg02: "Please fill in the station code",
            alarmMsg03: "Please fill in the resource code",
            alarmMsg04: "Please fill in the machine code",

            addTitle: "Add",
            addSuccess: "Add Success",
            editTitle: "Edit",
            editSuccess: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuccess: "Delete Success",
            importSuccess: "Import Success",
        }
    },
    equipLog: {
        queryField: {
            lineCode: "LineCode",
            lineCodeTip: "LineCode",
            stationCode: "StationCode",
            stationCodeTip: "StationCode",
            machineCode: "MachineCode",
            machineCodeTip: "MachineCode",
        },
        tbColumn: {
            id: "编号",
            lineCode: "LineCode",
            stationCode: "StationCode",
            resourceCode: "ResourceCode",
            machineCode: "MachineCode",
            machineName: "MachineName",
            machineStatus: "MachineStatus",
            machineStatusBegin: "StatusBegin",
            machineStatusEnd: "StatusEnd",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            synchronous: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            lineCode: "LineCode",
            lineCodeTip: "LineCode",
            stationCode: "StationCode",
            stationCodeTip: "StationCode",
            machineCode: "MachineCode",
            machineCodeTip: "MachineCode",
            resourceCode: "ResourceCode",
            resourceCodeTip: "ResourceCode",
            machineStatus: "MachineStatus",
            machineStatusTip: "MachineStatus",
            machineStatusBegin: "StatusBegin",
            machineStatusBeginTip: "StatusBegin",
            machineStatusEnd: "StatusEnd",
            machineStatusEndTip: "StatusEnd",
            Stataus: "Stataus",
            StatausTip: "Stataus",
            Remark: "Remark",
            RemarkTip: "Remark",
        },
        message: {
            alarmMsg01: "Please fill in the line code",
            alarmMsg02: "Please fill in the station code",
            alarmMsg03: "Please fill in the machine code",
            alarmMsg04: "Please fill in the resource code",

            addTitle: "Add",
            addSuccess: "Add Success",
            editTitle: "Edit",
            editSuccess: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuccess: "Delete Success",
            importSuccess: "Import Success",
        },

    },
    equipWarning: {
        queryField: {
            lineCode: "LineCode",
            lineCodeTip: "LineCode",
            stationCode: "StationCode",
            stationCodeTip: "StationCode",
            machineCode: "MachineCode",
            machineCodeTip: "MachineCode",
        },
        tbColumn: {
            id: "编号",
            lineCode: "LineCode",
            stationCode: "StationCode",
            resourceCode: "ResourceCode",
            machineCode: "MachineCode",
            machineName: "MachineName",
            deviceStatus: "DeviceStatus",
            deviceAlarm: "DeviceAlarm",
            accumulatedPowerOnTime: "Power Time(S)",
            accumulatedOperationTime: "Operation Time(S)",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            synchronous: "Sync",
            import: "Import",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
        },
        elForm: {
            lineCode: "LineCode",
            lineCodeTip: "LineCode",
            stationCode: "StationCode",
            stationCodeTip: "StationCode",
            machineCode: "MachineCode",
            machineCodeTip: "MachineCode",
            resourceCode: "ResourceCode",
            resourceCodeTip: "ResourceCode",
            deviceStatus: "DeviceStatus",
            deviceStatusTip: "DeviceStatus",
            deviceAlarm: "DeviceAlarm",
            deviceAlarmTip: "DeviceAlarm",
            accumulatedPowerOnTime: "PowerTime(S)",
            accumulatedPowerOnTimeTip: "PowerTime(S)",
            accumulatedOperationTime: "OperationTime(S)",
            accumulatedOperationTimeTip: "OperationTime(S)",
            Stataus: "Stataus",
            StatausTip: "Stataus",
            Remark: "Remark",
            RemarkTip: "Remark",
        },
        message: {
            alarmMsg01: "Please fill in the line code",
            alarmMsg02: "Please fill in the station code",
            alarmMsg03: "Please fill in the machine code",
            alarmMsg04: "Please fill in the resource code",

            addTitle: "Add",
            addSuccess: "Add Success",
            editTitle: "Edit",
            editSuccess: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuccess: "Delete Success",
            importSuccess: "Import Success",
        },
    }
}