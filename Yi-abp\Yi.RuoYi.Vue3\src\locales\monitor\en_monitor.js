export default {
    login: {
        query: {
            ipAddress: "IpAddress",
            userName: "UserName",
            onlineStatus: "OnlineState",
            logTime: "LoginTime",
            startTime: "StartTime",
            endTime: "EndTime",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
            clean: "Clean",
            unlock: "Unlock"
        },
        tbCol: {
            userName: "UserName",
            ipAddress: "IpAddress",
            location: "Location",
            system: "System",
            browser: "Browser",
            msg: "Msg",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete selected data item?",
            delSuc: "Delete Success",
            cleanMsg: "Do you want to clean all the items?",
            cleanSuc: "Clean Success?",
            unlockMsg: "Do you want to unlock selected the items?",
            unlockSuc: "Unlock Success?",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    operation: {
        query: {
            optTime: "OperationTime",
            module: "SystemModule",
            optType: "OperationType",
            optUser: "OperationUser",

            startTime: "StartTime",
            endTime: "EndTime",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
            clean: "Clean",
            Close: "Close",

            details: "Details",
        },
        tbCol: {
            module: "SystemModule",
            optType: "OperationType",
            requestMethod: "RequestMethod",
            optUser: "OperationUser",
            ipAds: "IpAddress",
            optTime: "OperationTime",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            module: "SystemModule",
            userInfo: "UserInfo",
            creationTime: "CreationTime",
            routePath: "RoutePath",
            optData: "OperationData",
            errMsg: "ExceptionMessage",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            remarkTip: "Please fill in contents",
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",
            alarm11: "",
            alarm12: "",

            addTit: "Add Role",
            addSuc: "Add Success",
            editTit: "Edit Role",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            cleanMsg: "Do you want to clean all the items?",
            cleanSuc: "Clean Success?",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    apiLog: {
        query: {
            serviceId: "ServiceInterface",
            status: "Status",
            creationTime: "CreationTime",

            startTime: "StartTime",
            endTime: "EndTime",
        },
        button: {
            search: "Search",
            reset: "Reset",
            add: "Add",
            sync: "Sync",
            import: "Import",
            export: "Export",
            edit: "Edit",
            delete: "Delete",
            confirm: "Confirm",
            cancel: "Cancel",
            clean: "Clean",
            Close: "Close",
        },
        tbCol: {
            httpCount: "HttpCount",
            UniqueCode: "UniqueCode",
            businessCode: "BusinessCode",
            businessName: "BusinessName",
            appId: "AppId",
            appKey: "AppKey",
            processData: "processData",
            sendData: "SendData",
            saveData: "SaveData",
            httpPath: "HttpPath",

            orderNum: "OrderNum",
            status: "Status",
            remark: "Remark",
            creatorId: "CreatorId",
            creatorName: "CreatorName",
            creationTime: "CreationTime",
            lastModifierId: "LastModifierId",
            lastModifierName: "LastModifierName",
            lastModificationTime: "LastModificationTime",
            isDeleted: "IsDeleted",
            operation: "Operation",
        },
        form: {
            status: "Status",
            remark: "Remark",
            remarkTip: "Please fill in contents",
        },
        message: {
            alarm01: "",
            alarm02: "",
            alarm03: "",
            alarm04: "",
            alarm05: "",
            alarm06: "",
            alarm07: "",
            alarm08: "",
            alarm09: "",
            alarm10: "",

            addTit: "Add",
            addSuc: "Add Success",
            editTit: "Edit",
            editSuc: "Edit Success",
            delMsg: "Do you want to delete this data item?",
            delSuc: "Delete Success",
            syncSuc: "Synchronous Success",
            importSuc: "Import Success",
        }
    },
    online: {
    }
}