<template>
   <div class="app-container">
      <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
         <el-form-item label="工艺路线" prop="routeNumber">
            <el-input v-model="queryParams.routeNumber" placeholder="工艺路线" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item label="工序编码" prop="operationCode">
            <el-input v-model="queryParams.operationCode" placeholder="工序编码" clearable @keyup.enter="handleQuery" />
         </el-form-item>
         <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
         </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
         <el-col :span="1.5">
            <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['process:route:add']">新增</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleDown" v-hasPermi="['process:route:down']" :loading="downLoading" v-if="false">同步</el-button>
         </el-col>
         <el-col :span="1.5">
            <el-button type="warning" plain icon="Download" @click="handleImport" v-hasPermi="['process:route:import']" :loading="importLoading" v-if="false">导入</el-button>
         </el-col>
         <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <el-table v-loading="loading" :data="dataList" @selection-change="handleSelectionChange" :row-class-name="tableRowClassName">
         <el-table-column align="center" type="selection" width="30" />
         <el-table-column align="center" label="编号" prop="id" v-if="false" />
         <el-table-column align="center" label="工艺路线" prop="routeNumber"  min-width="264"  />
         <el-table-column align="center" label="工艺版本" prop="routeVersion" />
         <el-table-column align="center" label="前序编码" prop="lastOperationCode" min-width="110" />
         <el-table-column align="center" label="后序编码" prop="nextOperationCode" min-width="110" />
         <el-table-column align="center" label="工序编码" prop="operationCode" min-width="130" />
         <el-table-column align="center" label="工序名称" prop="operationName" min-width="140" />
         <el-table-column align="center" label="工序顺序" prop="sort" width="80" />
         <el-table-column align="center" label="工位编码" prop="stationCode" width="280" />
         <el-table-column align="center" label="更新时间" prop="editTime" width="155" />

         <el-table-column label="排序" prop="orderNum" v-if="false"> </el-table-column>
         <el-table-column label="状态" prop="status">
            <template #default="scope">
               <dict-tag :options="base_is_open" :value="scope.row.status" />
            </template>
         </el-table-column>
         <el-table-column label="备注" prop="remark" v-if="false" />
         <el-table-column label="创建者" prop="creatorId" v-if="false" />
         <el-table-column label="创建者" prop="creatorName" v-if="false" />
         <el-table-column label="创建时间" prop="creationTime" v-if="false" width="155" />
         <el-table-column label="处理人" prop="lastModifierId" v-if="false" />
         <el-table-column label="处理人" prop="lastModifierName" v-if="false" />
         <el-table-column label="处理时间" prop="lastModificationTime" v-if="false" width="155" />
         <el-table-column label="是否删除" prop="isDeleted" v-if="false" />

         <el-table-column label="操作" align="center" class-name="small-padding fixed-width" fixed="right" width="150">
            <template #default="scope">
               <el-button link icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['process:route:edit']">编辑</el-button>
               <el-button link icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['process:route:remove']">删除</el-button>
            </template>
         </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="Number(total)" v-model:page="queryParams.skipCount" v-model:limit="queryParams.maxResultCount" @pagination="getList" />

      <!-- 添加或编辑对话框 -->
      <el-dialog :title="title" v-model="open" width="800px" append-to-body>
         <el-form ref="submitRef" :model="form" :rules="rules" label-width="80px">
            <el-row>
               <el-col :span="12">
                  <el-form-item label="工艺路线" prop="routeNumber">
                     <el-input v-model="form.routeNumber" placeholder="工艺路线" />
                  </el-form-item>
                  <el-form-item label="工艺版本" prop="routeVersion">
                     <el-input v-model="form.routeVersion" placeholder="工艺版本" />
                  </el-form-item>
                  <el-form-item label="工序编号" prop="operationCode">
                     <el-input v-model="form.operationCode" placeholder="工序编号" />
                  </el-form-item>
                  <el-form-item label="工序名称" prop="operationName">
                     <el-input v-model="form.operationName" placeholder="工序名称" />
                  </el-form-item>
               </el-col>
               <el-col :span="12">
                  <el-form-item label="工序顺序" prop="sort">
                     <el-input v-model="form.sort" placeholder="工序顺序" />
                  </el-form-item>
                  <el-form-item label="前序编码" prop="lastOperationCode">
                     <el-input v-model="form.lastOperationCode" placeholder="前序编码" />
                  </el-form-item>
                  <el-form-item label="后序编码" prop="nextOperationCode">
                     <el-input v-model="form.nextOperationCode" placeholder="后序编码" />
                  </el-form-item>
                  <el-form-item label="更新时间" prop="editTime">
                     <el-input v-model="form.editTime" placeholder="更新时间" />
                  </el-form-item>
               </el-col>
               <el-col :span="24">
                  <el-form-item label="工位编码" prop="stationCode">      
                     <el-input v-model="form.stationCode" type="textarea" rows="5" placeholder="工位编码" />
                  </el-form-item>
                  <el-form-item label="状态" prop="status">
                     <el-select v-model="form.status">
                        <el-option v-for="dict in base_is_open"  :key="JSON.parse(dict.value)" :value="JSON.parse(dict.value)" :label="dict.label" />
                     </el-select>
                  </el-form-item>
                  <el-form-item label="备注" prop="remark">
                     <el-input v-model="form.remark"  type="textarea" rows="5" placeholder="请输入内容" />
                  </el-form-item>
               </el-col>
         </el-row>
         </el-form>
         <template #footer>
            <div class="dialog-footer">
               <el-button type="primary" @click="submitForm">确 定 </el-button>
               <el-button @click="cancel">取 消</el-button>
            </div>
         </template>
      </el-dialog>
   </div>
</template>

<script setup name="Route">
/** 引入----------------------------------------------------------------------------*/
import { listDataAsync, listData, addData, delData, getData, updateData, getDown } from "@/api/process/route";

const { proxy } = getCurrentInstance();
const { base_is_open } = proxy.useDict("base_is_open");


/** 结构定义----------------------------------------------------------------------------*/
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dataList = ref([]);
const ids = ref([]);
const downLoading= ref(false);
const importLoading = ref(false);
const tableRowClassName = ({row,rowIndex}) => {
   var nowDate = new Date();
   var tenMinutesAgo = new Date(nowDate.getTime() - 10 * 60 * 1000);
   var rowTime = new Date(row.creationTime );
   if (rowTime >= tenMinutesAgo) {
      return 'success-row'
   } 
   return ''
}

const data = reactive({
   form: {},
   queryParams: {
      skipCount: 1,
      maxResultCount: 10,
      Sorting: undefined
   },
   rules: {
      routeNumber: [{ required: true, message: "请填写工艺路线编号", trigger: "blur" }],
      routeVersion: [{ required: true, message: "请填写工艺路线版本", trigger: "blur" }],
      operationCode: [{ required: true, message: "请填写工序编号", trigger: "blur" }],
      operationName: [{ required: true, message: "请填写工序名称", trigger: "blur" }],
      lastOperationCode: [{ required: true, message: "请填写前一道工序编号", trigger: "blur" }],
      nextOperationCode: [{ required: true, message: "请填写下一道工序编号", trigger: "blur" }],
      sort: [{ required: true, message: "请填写工序顺序", trigger: "blur" }],
      stationCode: [{ required: true, message: "请填写工位编码", trigger: "blur" }],
      status: [{ required: true, message: "请填写状态", trigger: "blur" }]
   }
});
const { form, queryParams, rules } = toRefs(data);

/** 列表----------------------------------------------------------------------------*/
/** 查询列表 */
function getList() {
   loading.value = true;
   listDataAsync(queryParams.value).then(response => {
      dataList.value = response.data.items;
      total.value = response.data.totalCount;
      loading.value = false;
   });
}
/** 搜索按钮操作 */
function handleQuery() {
   queryParams.value.skipCount = 1;
   getList();
}
/** 重置搜索条件 */
function resetQuery() {
   proxy.resetForm("queryRef");
   handleQuery();
}

/** 列表操作----------------------------------------------------------------------------*/
/** 多选框选中数据 */
function handleSelectionChange(selection) {
   ids.value = selection.map(item => item.id);
   single.value = selection.length != 1;
   multiple.value = !selection.length;
}

/** 编辑页面操作----------------------------------------------------------------------------*/
/** 重置提交表单 */
function reset() {
   form.value = {
      id: undefined,
   };
   proxy.resetForm("submitRef");
}

/** 提交按钮 */
function submitForm() {
   proxy.$refs["submitRef"].validate(valid => {
      if (valid) {
         if (form.value.id != undefined) {
            updateData(form.value.id, form.value).then(response => {
               proxy.$modal.msgSuccess("编辑成功");
               open.value = false;
               getList();
            });
         } else {
            addData(form.value).then(response => {
               proxy.$modal.msgSuccess("新增成功");
               open.value = false;
               getList();
            });
         }
      }
   });
}

/** 取消按钮 */
function cancel() {
   open.value = false;
   reset();
}

/** 增删改按钮----------------------------------------------------------------------------*/
/** 新增按钮操作 */
function handleAdd() {
   reset();
   open.value = true;
   title.value = "新增";
}

/** 编辑按钮操作 */
function handleUpdate(row) {
   reset();
   const postId = row.id || ids.value;
   getData(postId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "编辑";
   });
}

/** 删除按钮操作 */
function handleDelete(row) {
   const postIds = row.id || ids.value;
   proxy.$modal.confirm('是否删除编号为"' + postIds + '"的数据项？').then(function () {
      return delData(postIds);
   }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
   }).catch(() => { });
}

/** 数据同步----------------------------------------------------------------------------*/
/** 同步按钮操作 */
function handleDown() {
   downLoading.value = true;
   getDown().then(response => {
      setTimeout(function() {  // 延迟1.5秒执行
         downLoading.value =false;
         proxy.$modal.msgSuccess("同步成功,变更行数:"+response.data);
      }, 1500);
   });
}

/** 导入按钮操作 */
function handleImport() {
   proxy.$modal.msgSuccess("导入成功");
}

getList();
</script>

<style>
.el-table .warning-row {
  --el-table-tr-bg-color: var(--el-color-warning-light-9);
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>